"""add default builin tools

Revision ID: 2e06332b68b0
Revises: ccce3422b5c8
Create Date: 2025-07-19 11:04:23.191823

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from uuid import uuid4


# revision identifiers, used by Alembic.
revision = '2e06332b68b0'
down_revision = 'ccce3422b5c8'
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    # Add default builtin tools
    builtin_tools = [
        {
            "name": "create_chart",
            "display_name": "Create Chart",
            "description": "Built-in connector for creating charts",
            "default_required_permission": False,
        },
        {
            "name": "push_alert",
            "display_name": "Push Alert",
            "description": "Built-in connector for pushing alerts",
            "default_required_permission": False,
        },
        {
            "name": "recommendation",
            "display_name": "Recommendation",
            "description": "Built-in connector for recommendations",
            "default_required_permission": False,
        },
        {
            "name": "search_internet",
            "display_name": "Search Internet",
            "description": "Built-in connector for searching the internet",
            "default_required_permission": True,
        },
        {
            "name": "planning",
            "display_name": "Planning",
            "description": "Built-in connector for planning",
            "default_required_permission": False,
        },
        {
            "name": "schedule_task",
            "display_name": "Schedule Task",
            "description": "Built-in connector for scheduling tasks",
            "default_required_permission": True,
        },
        {
            "name": "report",
            "display_name": "Report",
            "description": "Built-in connector for reports",
            "default_required_permission": False,
        },
        {
            "name": "use_console_read_only_permissions",
            "display_name": "Use Console Read Only Permissions",
            "description": "Built-in connector for using console with read only permissions",
            "default_required_permission": False,
        },
        {
            "name": "use_console_write_permissions",
            "display_name": "Use Console Write Permissions",
            "description": "Built-in connector for using console with write permissions",
            "default_required_permission": True,
        },
        {
            "name": "search_knowledge_base",
            "display_name": "Search Knowledge Base",
            "description": "Built-in connector for searching knowledge bases",
            "default_required_permission": False,
        },
        {
            "name": "dashboard",
            "display_name": "Dashboard",
            "description": "Built-in connector for creating dashboards",
            "default_required_permission": False,
        },
        {
            "name": "group_chat",
            "display_name": "Group Chat",
            "description": "Built-in connector for group chat collaboration",
            "default_required_permission": False,
        },
    ]

    for tool in builtin_tools:
        conn.execute(
            sa.text(
                """
                INSERT INTO builtintool (id, name, display_name, description, default_required_permission, created_at, updated_at)
                VALUES (:id, :name, :display_name, :description, :default_required_permission, NOW(), NOW())
                ON CONFLICT (name) DO UPDATE
                SET display_name = EXCLUDED.display_name,
                    description = EXCLUDED.description,
                    default_required_permission = EXCLUDED.default_required_permission,
                    updated_at = NOW()
                """
            ),
            {
                "id": str(uuid4()),
                "name": tool["name"],
                "display_name": tool["display_name"],
                "description": tool["description"],
                "default_required_permission": tool["default_required_permission"],
            }
        )

    # Add builtin tools to all existing workspaces
    tools = conn.execute(sa.text("SELECT id FROM builtintool")).fetchall()
    workspaces = conn.execute(sa.text("SELECT id FROM workspace")).fetchall()

    for workspace in workspaces:
        for tool in tools:
            # Check if this workspace-tool combination already exists
            exists = conn.execute(
                sa.text(
                    """
                    SELECT 1 FROM workspacebuiltintool
                    WHERE workspace_id = :workspace_id AND builtin_tool_id = :tool_id
                    """
                ),
                {"workspace_id": workspace[0], "tool_id": tool[0]}
            ).fetchone()

            if not exists:
                # Get default permission for this tool
                permission = conn.execute(
                    sa.text(
                        """
                        SELECT default_required_permission FROM builtintool
                        WHERE id = :tool_id
                        """
                    ),
                    {"tool_id": tool[0]}
                ).fetchone()[0]

                # Insert the new workspace-tool relationship
                conn.execute(
                    sa.text(
                        """
                        INSERT INTO workspacebuiltintool (id, workspace_id, builtin_tool_id, required_permission)
                        VALUES (:id, :workspace_id, :tool_id, :required_permission)
                        """
                    ),
                    {
                        "id": str(uuid4()),
                        "workspace_id": workspace[0],
                        "tool_id": tool[0],
                        "required_permission": permission
                    }
                )


def downgrade():
    # We don't remove the tools in downgrade as this could cause issues with existing data
    # Just providing an empty downgrade function
    pass
