"""update connection table

Revision ID: 81bc3c3e0114
Revises: 28818926176a
Create Date: 2025-07-19 17:48:25.349170

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '81bc3c3e0114'
down_revision = '28818926176a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('STREAMABLE_HTTP', 'SSE', 'STDIO', name='connectiontransport').create(op.get_bind())
    op.create_table('agent_connection',
    sa.Column('agent_id', sa.Uuid(), nullable=False),
    sa.Column('conn_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['agent_id'], ['agent.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['conn_id'], ['connection.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('agent_id', 'conn_id')
    )
    op.drop_table('agent_connection_servers')
    op.alter_column('connection', 'type',
               existing_type=postgresql.ENUM('STREAMABLE_HTTP', 'SSE', 'STDIO', name='connectionservertransport'),
               type_=sa.Enum('STREAMABLE_HTTP', 'SSE', 'STDIO', name='connectiontransport'),
               existing_nullable=False,
               postgresql_using='type::text::connectiontransport')
    sa.Enum('STREAMABLE_HTTP', 'SSE', 'STDIO', name='connectionservertransport').drop(op.get_bind())
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('STREAMABLE_HTTP', 'SSE', 'STDIO', name='connectionservertransport').create(op.get_bind())
    op.alter_column('connection', 'type',
               existing_type=sa.Enum('STREAMABLE_HTTP', 'SSE', 'STDIO', name='connectiontransport'),
               type_=postgresql.ENUM('STREAMABLE_HTTP', 'SSE', 'STDIO', name='connectionservertransport'),
               existing_nullable=False)
    op.create_table('agent_connection_servers',
    sa.Column('agent_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('conn_server_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['agent_id'], ['agent.id'], name='agent_connection_servers_agent_id_fkey', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['conn_server_id'], ['connection.id'], name='agent_connection_servers_conn_server_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('agent_id', 'conn_server_id', name='agent_connection_servers_pkey')
    )
    op.drop_table('agent_connection')
    sa.Enum('STREAMABLE_HTTP', 'SSE', 'STDIO', name='connectiontransport').drop(op.get_bind())
    # ### end Alembic commands ###
