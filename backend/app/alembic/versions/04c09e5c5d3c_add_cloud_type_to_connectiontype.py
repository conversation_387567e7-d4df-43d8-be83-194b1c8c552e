"""Add cloud type to connectiontype

Revision ID: 04c09e5c5d3c
Revises: 9ae0745aea2e
Create Date: 2025-07-22 10:53:33.091524

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from alembic_postgresql_enum import TableReference

# revision identifiers, used by Alembic.
revision = '04c09e5c5d3c'
down_revision = '9ae0745aea2e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(
        enum_schema='public',
        enum_name='connectiontype',
        new_values=['BUILTIN', 'MCP', 'CLOUD', 'CLI'],
        affected_columns=[TableReference(table_schema='public', table_name='connection', column_name='type')],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(
        enum_schema='public',
        enum_name='connectiontype',
        new_values=['BUILTIN', 'MCP', 'CLI'],
        affected_columns=[TableReference(table_schema='public', table_name='connection', column_name='type')],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###
