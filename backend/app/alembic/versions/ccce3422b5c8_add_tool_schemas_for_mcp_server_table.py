"""add tool_schemas for mcp server table

Revision ID: ccce3422b5c8
Revises: 195a5c16fb92
Create Date: 2025-07-17 19:23:38.309575

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'ccce3422b5c8'
down_revision = '195a5c16fb92'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('mcpserver', sa.Column('tool_schemas', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('mcpserver', 'tool_schemas')
    # ### end Alembic commands ###
