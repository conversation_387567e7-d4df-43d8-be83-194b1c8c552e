"""remove group chat builin tools

Revision ID: f9d462e7063f
Revises: 04c09e5c5d3c
Create Date: 2025-07-22 12:42:56.504480

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'f9d462e7063f'
down_revision = '04c09e5c5d3c'
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    # Remove group chat builtin tool from all workspaces first
    conn.execute(
        sa.text(
            """
            DELETE FROM workspacebuiltintool
            WHERE builtin_tool_id IN (
                SELECT id FROM builtintool WHERE name = 'group_chat'
            )
            """
        )
    )

    # Remove the group chat builtin tool itself
    conn.execute(
        sa.text("DELETE FROM builtintool WHERE name = 'group_chat'")
    )


def downgrade():
    conn = op.get_bind()

    # Re-add the group chat builtin tool
    conn.execute(
        sa.text(
            """
            INSERT INTO builtintool (id, name, display_name, description, default_required_permission, created_at, updated_at)
            VALUES (gen_random_uuid(), 'group_chat', 'Group Chat', 'Built-in connector for group chat collaboration', false, NOW(), NOW())
            ON CONFLICT (name) DO NOTHING
            """
        )
    )

    # Add the tool back to all existing workspaces
    workspaces = conn.execute(sa.text("SELECT id FROM workspace")).fetchall()
    tool_id = conn.execute(sa.text("SELECT id FROM builtintool WHERE name = 'group_chat'")).fetchone()

    if tool_id:
        for workspace in workspaces:
            # Check if this workspace-tool combination already exists
            exists = conn.execute(
                sa.text(
                    """
                    SELECT 1 FROM workspacebuiltintool
                    WHERE workspace_id = :workspace_id AND builtin_tool_id = :tool_id
                    """
                ),
                {"workspace_id": workspace[0], "tool_id": tool_id[0]}
            ).fetchone()

            if not exists:
                conn.execute(
                    sa.text(
                        """
                        INSERT INTO workspacebuiltintool (id, workspace_id, builtin_tool_id, required_permission)
                        VALUES (gen_random_uuid(), :workspace_id, :tool_id, false)
                        """
                    ),
                    {
                        "workspace_id": workspace[0],
                        "tool_id": tool_id[0]
                    }
                )
