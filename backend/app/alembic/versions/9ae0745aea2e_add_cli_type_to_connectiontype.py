"""Add cli type to connectiontype

Revision ID: 9ae0745aea2e
Revises: e34994085fc9
Create Date: 2025-07-22 01:04:09.871601

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from alembic_postgresql_enum import TableReference

# revision identifiers, used by Alembic.
revision = '9ae0745aea2e'
down_revision = 'e34994085fc9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(
        enum_schema='public',
        enum_name='connectiontype',
        new_values=['BUILTIN', 'MCP', 'CLI'],
        affected_columns=[TableReference(table_schema='public', table_name='connection', column_name='type')],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(
        enum_schema='public',
        enum_name='connectiontype',
        new_values=['BUILTIN', 'MCP'],
        affected_columns=[TableReference(table_schema='public', table_name='connection', column_name='type')],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###
