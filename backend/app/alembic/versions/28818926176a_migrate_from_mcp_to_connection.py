"""migrate from mcp to connection

Revision ID: 28818926176a
Revises: 2e06332b68b0
Create Date: 2025-07-19 16:11:18.274590

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '28818926176a'
down_revision = '2e06332b68b0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.<PERSON>um('CONNECTED', 'ERROR', name='connectionstatus').create(op.get_bind())
    sa.Enum('STREAMABLE_HTTP', 'SSE', 'STDIO', name='connectionservertransport').create(op.get_bind())
    op.create_table('connection',
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('prefix', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('type', postgresql.ENUM('STREAMABLE_HTTP', 'SSE', 'STDIO', name='connectionservertransport', create_type=False), nullable=False),
    sa.Column('config', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('tool_list', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('tool_permissions', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('tool_enabled', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('tool_schemas', sa.JSON(), nullable=True),
    sa.Column('status', postgresql.ENUM('CONNECTED', 'ERROR', name='connectionstatus', create_type=False), nullable=False),
    sa.Column('status_message', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('status_updated_at', sa.DateTime(), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('workspace_id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_connection_is_active'), 'connection', ['is_active'], unique=False)
    op.create_index(op.f('ix_connection_workspace_id'), 'connection', ['workspace_id'], unique=False)
    op.create_table('agent_connection_servers',
    sa.Column('agent_id', sa.Uuid(), nullable=False),
    sa.Column('conn_server_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['agent_id'], ['agent.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['conn_server_id'], ['connection.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('agent_id', 'conn_server_id')
    )
    op.drop_table('agent_mcp_servers')
    op.drop_index('ix_mcpserver_is_active', table_name='mcpserver')
    op.drop_index('ix_mcpserver_workspace_id', table_name='mcpserver')
    op.drop_table('mcpserver')
    sa.Enum('CONNECTED', 'ERROR', name='mcpserverstatus').drop(op.get_bind())
    sa.Enum('STREAMABLE_HTTP', 'SSE', name='mcpservertransport').drop(op.get_bind())
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('STREAMABLE_HTTP', 'SSE', name='mcpservertransport').create(op.get_bind())
    sa.Enum('CONNECTED', 'ERROR', name='mcpserverstatus').create(op.get_bind())
    op.create_table('agent_mcp_servers',
    sa.Column('agent_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('mcp_server_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['agent_id'], ['agent.id'], name='agent_mcp_servers_agent_id_fkey', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['mcp_server_id'], ['mcpserver.id'], name='agent_mcp_servers_mcp_server_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('agent_id', 'mcp_server_id', name='agent_mcp_servers_pkey')
    )
    op.create_table('mcpserver',
    sa.Column('name', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('prefix', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('type', postgresql.ENUM('STREAMABLE_HTTP', 'SSE', name='mcpservertransport', create_type=False), autoincrement=False, nullable=False),
    sa.Column('config', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('tool_list', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True),
    sa.Column('tool_permissions', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True),
    sa.Column('tool_enabled', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True),
    sa.Column('status', postgresql.ENUM('CONNECTED', 'ERROR', name='mcpserverstatus', create_type=False), autoincrement=False, nullable=False),
    sa.Column('status_message', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('status_updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('workspace_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('tool_schemas', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], name='mcpserver_workspace_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='mcpserver_pkey')
    )
    op.create_index('ix_mcpserver_workspace_id', 'mcpserver', ['workspace_id'], unique=False)
    op.create_index('ix_mcpserver_is_active', 'mcpserver', ['is_active'], unique=False)
    op.drop_table('agent_connection_servers')
    op.drop_index(op.f('ix_connection_workspace_id'), table_name='connection')
    op.drop_index(op.f('ix_connection_is_active'), table_name='connection')
    op.drop_table('connection')
    sa.Enum('STREAMABLE_HTTP', 'SSE', 'STDIO', name='connectionservertransport').drop(op.get_bind())
    sa.Enum('CONNECTED', 'ERROR', name='connectionstatus').drop(op.get_bind())
    # ### end Alembic commands ###
