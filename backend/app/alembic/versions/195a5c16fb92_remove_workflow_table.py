"""remove workflow table

Revision ID: 195a5c16fb92
Revises: cde8d8e99039
Create Date: 2025-07-17 19:03:25.112667

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '195a5c16fb92'
down_revision = 'cde8d8e99039'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('workflownode')
    op.drop_table('workflow')
    sa.Enum('START', 'TOOL', 'HUMAN_IN_LOOP', 'END', 'OUTPUT', name='nodetype').drop(op.get_bind())
    sa.Enum('CREATED', 'UNVALIDATED', 'RUNNING', 'PENDING', 'COMPLETED', 'ERROR', name='workflowstatus').drop(op.get_bind())
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('CREATED', 'UNVALIDATED', 'RUNNING', 'PENDING', 'COMPLETED', 'ERROR', name='workflowstatus').create(op.get_bind())
    sa.Enum('START', 'TOOL', 'HUMAN_IN_LOOP', 'END', 'OUTPUT', name='nodetype').create(op.get_bind())
    op.create_table('workflow',
    sa.Column('name', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('description', sa.VARCHAR(length=1000), autoincrement=False, nullable=True),
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('workspace_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('status', postgresql.ENUM('CREATED', 'UNVALIDATED', 'RUNNING', 'PENDING', 'COMPLETED', 'ERROR', name='workflowstatus', create_type=False), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], name='workflow_workspace_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='workflow_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_table('workflownode',
    sa.Column('type', postgresql.ENUM('START', 'TOOL', 'HUMAN_IN_LOOP', 'END', 'OUTPUT', name='nodetype', create_type=False), autoincrement=False, nullable=False),
    sa.Column('name', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('description', sa.VARCHAR(length=1000), autoincrement=False, nullable=True),
    sa.Column('position', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('data', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('workflow_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('parent_id', sa.UUID(), autoincrement=False, nullable=True),
    sa.Column('status', postgresql.ENUM('CREATED', 'UNVALIDATED', 'RUNNING', 'PENDING', 'COMPLETED', 'ERROR', name='workflowstatus', create_type=False), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['parent_id'], ['workflownode.id'], name='workflownode_parent_id_fkey'),
    sa.ForeignKeyConstraint(['workflow_id'], ['workflow.id'], name='workflownode_workflow_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='workflownode_pkey')
    )
    # ### end Alembic commands ###
