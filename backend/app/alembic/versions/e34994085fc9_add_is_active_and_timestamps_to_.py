"""Add is_active and timestamps to AgentBuiltInTool

Revision ID: e34994085fc9
Revises: 50e16a23b03e
Create Date: 2025-07-21 14:09:29.089124

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'e34994085fc9'
down_revision = '50e16a23b03e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('agent_builtin_tools', sa.Column('is_active', sa.<PERSON>an(), nullable=False, server_default=sa.text('true')))
    op.add_column('agent_builtin_tools', sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')))
    op.add_column('agent_builtin_tools', sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')))
    op.create_index(op.f('ix_agent_builtin_tools_is_active'), 'agent_builtin_tools', ['is_active'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_agent_builtin_tools_is_active'), table_name='agent_builtin_tools')
    op.drop_column('agent_builtin_tools', 'updated_at')
    op.drop_column('agent_builtin_tools', 'created_at')
    op.drop_column('agent_builtin_tools', 'is_active')
    # ### end Alembic commands ###
