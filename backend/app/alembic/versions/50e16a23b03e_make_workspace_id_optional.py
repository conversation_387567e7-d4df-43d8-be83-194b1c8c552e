"""make workspace id optional

Revision ID: 50e16a23b03e
Revises: b964d72a6d5d
Create Date: 2025-07-21 10:52:59.904682

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '50e16a23b03e'
down_revision = 'b964d72a6d5d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('connection', 'workspace_id',
               existing_type=sa.UUID(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('connection', 'workspace_id',
               existing_type=sa.UUID(),
               nullable=False)
    # ### end Alembic commands ###
