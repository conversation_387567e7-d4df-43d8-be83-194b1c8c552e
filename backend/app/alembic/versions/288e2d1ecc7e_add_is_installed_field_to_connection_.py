"""Add is_installed field to connection table

Revision ID: 288e2d1ecc7e
Revises: b45a99981bcc
Create Date: 2025-07-26 17:37:30.369722

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '288e2d1ecc7e'
down_revision = 'b45a99981bcc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # First add column as nullable
    op.add_column('connection', sa.Column('is_installed', sa.<PERSON>(), nullable=True))
    
    # Update existing records to set default value
    op.execute("UPDATE connection SET is_installed = false WHERE is_installed IS NULL")
    
    # Now make the column non-nullable
    op.alter_column('connection', 'is_installed', nullable=False)
    
    op.create_index(op.f('ix_connection_is_installed'), 'connection', ['is_installed'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_connection_is_installed'), table_name='connection')
    op.drop_column('connection', 'is_installed')
    # ### end Alembic commands ###
