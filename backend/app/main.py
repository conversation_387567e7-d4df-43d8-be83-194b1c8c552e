import sentry_sdk
from fastapi import <PERSON>AP<PERSON>
from fastapi.concurrency import asynccontextmanager
from fastapi.responses import ORJSONResponse
from fastapi.routing import APIRoute
from starlette.middleware.cors import CORSMiddleware

from app.api.main import api_router
from app.core.config import settings


def custom_generate_unique_id(route: APIRoute) -> str:
    return f"{route.tags[0]}-{route.name}"


if settings.SENTRY_DSN and settings.ENVIRONMENT != "local":
    sentry_sdk.init(dsn=str(settings.SENTRY_DSN), enable_tracing=True)


@asynccontextmanager
async def lifespan(app: FastAPI):
    from app.worker import celery_app

    app.state.celery = celery_app

    from app.modules.multi_agents.core.agents.factory import AgentFactory

    await AgentFactory.initialize()
    # await initialize_qdrant()

    yield


app = FastAPI(
    default_response_class=ORJSONResponse,
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs" if settings.ENVIRONMENT in ["dev", "local"] else None,
    redoc_url="/redoc" if settings.ENVIRONMENT in ["dev", "local"] else None,
    generate_unique_id_function=custom_generate_unique_id,
    lifespan=lifespan,
    debug=True,
)


# Set all CORS enabled origins
if settings.all_cors_origins:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.all_cors_origins,
        allow_origin_regex=r"https?://192\.168\.\d{1,3}\.\d{1,3}(:\d+)?$",
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

app.include_router(api_router, prefix=settings.API_V1_STR)
