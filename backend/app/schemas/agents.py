import uuid

from sqlmodel import SQLModel

from app.models import AgentType


class AgentBase(SQLModel):
    id: uuid.UUID
    alias: str
    title: str
    role: str
    goal: str
    instructions: str
    is_active: bool
    type: AgentType


class AgentPublic(AgentBase):
    id: uuid.UUID


class AgentsPublic(SQLModel):
    data: list[AgentPublic]
    count: int


class AgentInstructionsUpdate(SQLModel):
    instructions: str


class AgentStatusUpdate(SQLModel):
    agent_status: bool
