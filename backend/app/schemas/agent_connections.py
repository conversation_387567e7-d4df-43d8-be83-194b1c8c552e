from uuid import UUID

from pydantic import BaseModel

from app.models import ConnectionPublic


class AgentConnectionsPublic(BaseModel):
    """Public schema for agent connections"""

    agent_id: UUID
    connections: list[ConnectionPublic]


class AgentsConnectionsResponse(BaseModel):
    """Response model for multiple agents' connections"""

    agents_connections: list[AgentConnectionsPublic]


class AgentConnectionCreateRequest(BaseModel):
    """Request model for creating agent connection"""

    connection_id: UUID


class AgentConnectionResponse(BaseModel):
    """Response model for single operations"""

    success: bool
    message: str
