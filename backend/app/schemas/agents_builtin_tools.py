from uuid import UUID

from pydantic import BaseModel


class AgentBuiltInToolPublic(BaseModel):
    id: UUID
    name: str
    display_name: str
    description: str | None = None
    required_permission: bool
    is_active: bool


class AgentBuiltInToolsPublic(BaseModel):
    agent_id: UUID
    tools: list[AgentBuiltInToolPublic]


class AgentsBuiltInToolsResponse(BaseModel):
    agents_builtin_tools: list[AgentBuiltInToolsPublic]


class AgentBuiltInToolUpdate(BaseModel):
    workspace_builtin_tool_id: UUID
    is_active: bool


class AgentBuiltInToolsUpdateRequest(BaseModel):
    agent_builtin_tools: list[AgentBuiltInToolUpdate]


class AgentBuiltInToolBulkUpdateResult(BaseModel):
    workspace_builtin_tool_id: UUID
    success: bool
    error_message: str | None = None


class AgentBuiltInToolsBulkUpdateResponse(BaseModel):
    total_count: int
    success_count: int
    failed_count: int
    results: list[AgentBuiltInToolBulkUpdateResult]
