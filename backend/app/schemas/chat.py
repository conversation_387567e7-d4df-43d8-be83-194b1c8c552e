import uuid
from typing import Any

from pydantic import BaseModel
from sqlmodel import Session
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.config import settings
from app.models import Conversation, Message, MessageAttachment
from app.modules.multi_agents.config.config import (
    MultiAgentConfig,
    WorkspaceConfigSchema,
)
from app.modules.multi_agents.tools.tool_manager import ToolManager


class ChatServiceContext(BaseModel):
    model_config = {"arbitrary_types_allowed": True}

    # User context
    user_id: uuid.UUID
    workspace_id: uuid.UUID

    # Conversation context
    conversation: Conversation
    workspace_config: WorkspaceConfigSchema | None = None
    agents_config: MultiAgentConfig | None = None
    recursion_limit: int = 200

    # Message context
    resume: bool
    approve: bool
    user_prompt: str
    user_message_db: Message | None = None
    resource_id: uuid.UUID | None = None

    # Attachment context
    attachment_ids: list[uuid.UUID] | None = None
    attachment_records: list[MessageAttachment] | None = None
    attachment_content: list[dict] | None = None

    # KB context
    kb_prompt: str | None = None
    available_kbs_for_search: list[uuid.UUID] | None = None

    # Runtime context
    session: Session
    async_session: AsyncSession

    # Lanfuse context
    langfuse_user_id: str | None = None
    langfuse_session_id: str | None = None

    graph_name: str = "coordinator_agent"
    run_name: str = "Autonomous Agent"
    callbacks: list[Any] = []

    tool_manager: ToolManager | None = None

    @property
    def thread_id(self) -> uuid.UUID:
        return self.conversation.id

    def get_user_message_db(self) -> Message:
        """Get the user message DB, guaranteed to be non-None."""
        if self.user_message_db is None:
            raise ValueError("user_message_db has not been initialized yet")
        return self.user_message_db

    def to_dict(self):
        result = {
            "configurable": {
                "user_id": str(self.user_id) if self.user_id else None,
                "recursion_limit": self.recursion_limit,
                "thread_id": str(self.thread_id) if self.thread_id else None,
                "conversation_id": str(self.conversation.id)
                if self.conversation.id
                else None,
                "workspace_id": str(self.workspace_id) if self.workspace_id else None,
                "workspace_config": self.workspace_config,
                "agents_config": self.agents_config,
                "available_kbs_for_search": self.available_kbs_for_search,
                "kb_prompt": self.kb_prompt,
                "resource_id": str(self.resource_id) if self.resource_id else None,
                "environment": settings.ENVIRONMENT,
                "attachment_content": self.attachment_content,
                "tool_manager": self.tool_manager,
            },
            "recursion_limit": self.recursion_limit,
            "metadata": {
                "langfuse_user_id": str(self.user_id) if self.user_id else None,
                "langfuse_session_id": str(self.conversation.id)
                if self.conversation.id
                else None,
            },
        }

        return result


class NamespaceChangeOutput(BaseModel):
    namespace: str | None
    response_message: Any  # Should be replaced with a more specific type if available
    response_text: str
    response_text_pointer: int
    full_message_text: str
    current_thought: Any | None  # Replace with specific type if available
    first_checkpoint_id: Any | None  # Replace with UUID or specific type if available
    last_checkpoint_id: Any | None  # Replace with UUID or specific type if available
    current_thought_idx: int
    input_token_usage: int
    output_token_usage: int


class NamespaceChangeInput(BaseModel):
    updated_namespace: str | None
    current_namespace: str | None
    user_message_db: Any  # Should be replaced with a more specific type if available
    response_text: str
    response_text_pointer: int
    full_message_text: str
    conversation_id: uuid.UUID
    first_checkpoint_id: str | None
    last_checkpoint_id: str | None
    current_thought: Any | None  # Replace with specific type if available
    current_thought_idx: int
    input_token_usage: int
    output_token_usage: int
