"""
Schemas for constants/utils API responses.
"""
from datetime import datetime
from typing import Any

from pydantic import BaseModel


class ConstantOption(BaseModel):
    """Schema for a single constant option."""
    value: str
    label: str
    key: str
    metadata: dict[str, Any] | None = None


class ConstantCategory(BaseModel):
    """Schema for a constant category response."""
    category: str
    data: list[ConstantOption]
    description: str
    total: int
    last_updated: str

    @classmethod
    def from_registry_data(cls, category: str, registry_data: dict[str, Any]) -> "ConstantCategory":
        """Create ConstantCategory from registry data."""
        return cls(
            category=category,
            data=[ConstantOption(**item) for item in registry_data["data"]],
            description=registry_data["description"],
            total=len(registry_data["data"]),
            last_updated=registry_data["last_updated"]
        )


class ConstantCategoriesResponse(BaseModel):
    """Schema for listing all available constant categories."""
    categories: list[str]
    total: int


class AllConstantsResponse(BaseModel):
    """Schema for returning all constants at once."""
    constants: dict[str, ConstantCategory]
    total_categories: int
    generated_at: str

    @classmethod
    def from_registry(cls, registry: dict[str, dict[str, Any]]) -> "AllConstantsResponse":
        """Create AllConstantsResponse from registry."""
        constants = {}
        for category, data in registry.items():
            constants[category] = ConstantCategory.from_registry_data(category, data)

        return cls(
            constants=constants,
            total_categories=len(constants),
            generated_at=datetime.utcnow().isoformat()
        )
