import asyncio
import sys
from datetime import datetime
from uuid import uuid4

from sqlmodel import select

from app.core.db_session import get_task_session
from app.logger import logger
from app.models import (
    Connection,
    ConnectionBase,
    ConnectionStatus,
    ConnectionTransport,
    ConnectionType,
)

BUILTIN_CONNECTIONS = [
    {
        "name": "PostgreSQL",
        "prefix": "postgres",
        "type": ConnectionType.BUILTIN,
        "transport_type": ConnectionTransport.STREAMABLE_HTTP,
        "config": {
            "command": "uvx",
            "args": ["postgres-mcp-pro-plus", "--access-mode=unrestricted"],
            "env": {"DATABASE_URI": "******************************************/app"},
        },
        "is_active": True,
        "tool_permissions": [],
    },
    {
        "name": "<PERSON><PERSON>",
        "prefix": "grafana",
        "type": ConnectionType.BUILTIN,
        "transport_type": ConnectionTransport.STREAMABLE_HTTP,
        "config": {
            "command": "mcp-grafana",
            "args": [],
            "env": {
                "GRAFANA_URL": "http://localhost:3000",
                "GRAFANA_API_KEY": "<your service account token>",
            },
        },
        "is_active": True,
        "tool_permissions": [],
    },
    {
        "name": "Amazon Web Services",
        "prefix": "aws",
        "type": ConnectionType.CLOUD,
        "transport_type": ConnectionTransport.STREAMABLE_HTTP,
        "config": {
            "env": {
                "AWS_ACCESS_KEY_ID": "AKIAIOSFODNN7EXAMPLE",
                "AWS_SECRET_ACCESS_KEY": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY",
                "AWS_DEFAULT_REGION": "us-east-1",
            },
        },
        "is_active": True,
        "tool_list": [
            "aws__use_cli_read_only",
            "aws__use_cli_write_only",
        ],
        "tool_permissions": ["aws__use_cli_write_only"],
    },
    {
        "name": "Google Cloud Platform",
        "prefix": "gcp",
        "type": ConnectionType.CLOUD,
        "transport_type": ConnectionTransport.STREAMABLE_HTTP,
        "config": {
            "env": ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************,
        },
        "is_active": True,
        "tool_list": [
            "gcp__use_cli_read_only",
            "gcp__use_cli_write_only",
        ],
        "tool_permissions": ["gcp__use_cli_write_only"],
    },
    {
        "name": "Microsoft Azure",
        "prefix": "azure",
        "type": ConnectionType.CLOUD,
        "transport_type": ConnectionTransport.STREAMABLE_HTTP,
        "config": {
            "env": {
                "username": "APP_ID",
                "password": "CLIENT_SECRET",
                "tenant": "TENANT_ID",
            },
        },
        "is_active": True,
        "tool_list": [
            "azure__use_cli_read_only",
            "azure__use_cli_write_only",
        ],
        "tool_permissions": ["azure__use_cli_write_only"],
    },
    {
        "name": "Kubernetes",
        "prefix": "k8s",
        "type": ConnectionType.CLI,
        "transport_type": ConnectionTransport.STREAMABLE_HTTP,
        "config": {
            "env": {"KUBECONFIG": "Paste your kubeconfig file content here"},
        },
        "is_active": True,
        "tool_list": [
            "k8s__use_cli_read_only",
            "k8s__use_cli_write_only",
        ],
        "tool_permissions": ["k8s__use_cli_write_only"],
    },
]


class BuiltinConnectionMigrator:
    def __init__(self):
        pass

    async def migrate_builtin_connections(self) -> None:
        """Simple migration: create missing builtin connections, skip if they exist."""
        try:
            async with get_task_session() as session:
                # 1. Get existing builtin connections from database
                existing_connections = await self._get_existing_builtin_connections(
                    session
                )
                existing_names = {conn.name for conn in existing_connections}

                # 2. Create missing connections only
                for conn_config in BUILTIN_CONNECTIONS:
                    if conn_config["name"] not in existing_names:
                        await self._create_builtin_connection(session, conn_config)

                await session.commit()

        except Exception as e:
            logger.error(f"Error during builtin connection migration: {str(e)}")
            raise

    async def _get_existing_builtin_connections(self, session) -> list[Connection]:
        """Get all existing system connections (builtin and CLOUD) from database."""
        try:
            statement = select(Connection).where(
                Connection.workspace_id.is_(None),
                Connection.type.in_(
                    [ConnectionType.BUILTIN, ConnectionType.CLOUD, ConnectionType.CLI]
                ),
            )
            result = await session.exec(statement)
            connections = result.all()
            logger.info(f"Found {len(connections)} existing builtin connections")
            return connections
        except Exception as e:
            logger.error(f"Error retrieving existing builtin connections: {str(e)}")
            raise

    async def _create_builtin_connection(self, session, conn_config: dict) -> None:
        """Create a new builtin connection."""
        try:
            connection = Connection(
                id=uuid4(),
                workspace_id=None,
                name=conn_config["name"],
                prefix=conn_config["prefix"],
                type=conn_config["type"],
                transport_type=conn_config["transport_type"],
                config=conn_config["config"],
                is_active=conn_config["is_active"],
                tool_permissions=conn_config["tool_permissions"],
                tool_enabled=[],
                status=ConnectionStatus.ERROR,
                status_message="",
                tool_list=[],
                tool_schemas=[],
            )

            session.add(connection)

            # Test connection for BUILTIN, use config tool_list for CLOUD
            if (
                conn_config["type"] in [ConnectionType.CLOUD, ConnectionType.CLI]
                and "tool_list" in conn_config
            ):
                # Use predefined tools for CLOUD connections
                connection.status = ConnectionStatus.CONNECTED
                connection.status_message = "Cloud connection with predefined tools"
                connection.tool_list = conn_config["tool_list"]
                connection.tool_enabled = conn_config["tool_list"]
                logger.info(
                    f"Created Cloud connection '{conn_config['name']}' with predefined tools: {conn_config['tool_list']}"
                )
            else:
                # Test BUILTIN connections
                await self._test_builtin_connection(connection, conn_config)

        except Exception as e:
            logger.error(
                f"Error creating builtin connection {conn_config['name']}: {str(e)}"
            )
            raise

    async def _test_builtin_connection(
        self, connection: Connection, conn_config: dict
    ) -> None:
        """Test BUILTIN connection and populate tool information."""
        try:
            from app.modules.connectors.connection_client import MCPManagerClient

            test_conn = ConnectionBase(
                name=conn_config["name"],
                prefix=conn_config["prefix"],
                type=conn_config["type"],
                transport_type=conn_config["transport_type"],
                config=conn_config["config"],
                is_active=conn_config["is_active"],
                tool_permissions=conn_config["tool_permissions"],
                tool_enabled=[],
            )

            mcp_manager_client = MCPManagerClient()
            connection_result = await mcp_manager_client.test_connection(test_conn)

            connection.status = connection_result["status"]
            connection.status_message = connection_result["status_message"]
            connection.tool_list = connection_result["tool_list"] or []
            connection.tool_schemas = connection_result["tool_schemas"] or []
            connection.tool_enabled = connection_result["tool_list"] or []
            connection.status_updated_at = datetime.now()

            logger.info(
                f"Created BUILTIN connection '{conn_config['name']}' with {len(connection.tool_list)} tools"
            )

        except Exception as e:
            logger.error(
                f"Error testing builtin connection '{conn_config['name']}': {str(e)}"
            )
            connection.status = ConnectionStatus.ERROR
            connection.status_message = f"Connection test failed: {str(e)}"


async def main():
    """Main function for running the migration."""
    try:
        logger.info("Starting builtin connection migration...")

        # Run migration
        migrator = BuiltinConnectionMigrator()
        await migrator.migrate_builtin_connections()

        logger.info("Builtin connection migration completed successfully")

    except Exception as e:
        logger.error(f"Builtin connection migration failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
