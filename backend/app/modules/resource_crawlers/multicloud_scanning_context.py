import uuid
from abc import ABC, abstractmethod
from typing import Union

from pydantic import BaseModel

from app.models import (
    AWSResourceType,
    AzureResourceType,
    CloudProvider,
    GCPResourceType,
)


class MultiCloudScanningContext(BaseModel, ABC):
    """Base validation model for multicloud scanning parameters"""

    workspace_id: uuid.UUID
    provider: CloudProvider
    regions: list[str]

    @abstractmethod
    def get_supported_resource_types(self) -> set[str]:
        """Return the supported resource types for this cloud provider"""
        pass

    @abstractmethod
    def validate_credentials(self) -> bool:
        """Validate the cloud provider credentials"""
        pass


class AWSScanningContext(MultiCloudScanningContext):
    """AWS-specific scanning context"""

    provider: CloudProvider = CloudProvider.AWS
    aws_account_id: uuid.UUID | None
    aws_access_key_id: str
    aws_secret_access_key: str
    services: set[AWSResourceType]

    def get_supported_resource_types(self) -> set[str]:
        """Return supported AWS resource types"""
        return {resource_type.value for resource_type in AWSResourceType}

    def validate_credentials(self) -> bool:
        """Validate AWS credentials"""
        # Basic validation - non-empty credentials
        return bool(self.aws_access_key_id and self.aws_secret_access_key)


class GCPScanningContext(MultiCloudScanningContext):
    """GCP-specific scanning context"""

    provider: CloudProvider = CloudProvider.GCP
    project_id: str
    service_account_key: str
    services: set[GCPResourceType]

    def get_supported_resource_types(self) -> set[str]:
        """Return supported GCP resource types"""
        return {resource_type.value for resource_type in GCPResourceType}

    def validate_credentials(self) -> bool:
        """Validate GCP credentials"""
        # Basic validation - non-empty credentials
        return bool(self.project_id and self.service_account_key)


class AzureScanningContext(MultiCloudScanningContext):
    """Azure-specific scanning context"""

    provider: CloudProvider = CloudProvider.AZURE
    subscription_id: str
    tenant_id: str
    client_id: str
    client_secret: str
    services: set[AzureResourceType]

    def get_supported_resource_types(self) -> set[str]:
        """Return supported Azure resource types"""
        return {resource_type.value for resource_type in AzureResourceType}

    def validate_credentials(self) -> bool:
        """Validate Azure credentials"""
        # Basic validation - non-empty credentials
        return bool(
            self.subscription_id
            and self.tenant_id
            and self.client_id
            and self.client_secret
        )


# Type alias for any scanning context
ScanningContextType = AWSScanningContext | GCPScanningContext | AzureScanningContext


def create_scanning_context(
    provider: CloudProvider,
    workspace_id: uuid.UUID,
    regions: list[str],
    resource_types: list[str],
    **credentials,
) -> ScanningContextType:
    """Factory function to create the appropriate scanning context"""

    if provider == CloudProvider.AWS:
        # Convert string types to AWS ResourceType enum
        supported_services = set()
        for service_str in resource_types:
            try:
                service = AWSResourceType(service_str)
                supported_services.add(service)
            except ValueError:
                # Skip unsupported types
                continue

        return AWSScanningContext(
            workspace_id=workspace_id,
            regions=regions,
            services=supported_services,
            aws_account_id=credentials.get("aws_account_id"),
            aws_access_key_id=credentials.get("aws_access_key_id"),
            aws_secret_access_key=credentials.get("aws_secret_access_key"),
        )

    elif provider == CloudProvider.GCP:
        # Convert string types to GCP ResourceType enum
        supported_services = set()
        for service_str in resource_types:
            try:
                service = GCPResourceType(service_str)
                supported_services.add(service)
            except ValueError:
                # Skip unsupported types
                continue

        return GCPScanningContext(
            workspace_id=workspace_id,
            regions=regions,
            services=supported_services,
            project_id=credentials.get("project_id"),
            service_account_key=credentials.get("service_account_key"),
        )

    elif provider == CloudProvider.AZURE:
        # Convert string types to Azure ResourceType enum
        supported_services = set()
        for service_str in resource_types:
            try:
                service = AzureResourceType(service_str)
                supported_services.add(service)
            except ValueError:
                # Skip unsupported types
                continue

        return AzureScanningContext(
            workspace_id=workspace_id,
            regions=regions,
            services=supported_services,
            subscription_id=credentials.get("subscription_id"),
            tenant_id=credentials.get("tenant_id"),
            client_id=credentials.get("client_id"),
            client_secret=credentials.get("client_secret"),
        )

    else:
        raise ValueError(f"Unsupported cloud provider: {provider}")
