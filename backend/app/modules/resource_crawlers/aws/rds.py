import time
from typing import Any

from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    AWSResourceType,
    CloudProvider,
    Resource,
    ResourceStatus,
)
from app.repositories.resources import ResourceRepository

from .base_crawler import (
    BaseCrawler,
    ResourceStateMapper,
    aws_retry,
)


class RDSStateMapper(ResourceStateMapper):
    """Maps RDS instance states to ResourceStatus"""

    _STATE_MAPPING = {
        "creating": ResourceStatus.STARTING,
        "available": ResourceStatus.RUNNING,
        "backing-up": ResourceStatus.RUNNING,
        "modifying": ResourceStatus.RUNNING,
        "rebooting": ResourceStatus.STARTING,
        "renaming": ResourceStatus.RUNNING,
        "resetting-master-credentials": ResourceStatus.RUNNING,
        "storage-full": ResourceStatus.RUNNING,
        "upgrading": ResourceStatus.RUNNING,
        "configuring-enhanced-monitoring": ResourceStatus.RUNNING,
        "maintenance": ResourceStatus.RUNNING,
        "moving-to-vpc": ResourceStatus.RUNNING,
        "promoting": ResourceStatus.RUNNING,
        "restore-error": ResourceStatus.RUNNING,
        "storage-optimization": ResourceStatus.RUNNING,
        "incompatible-parameters": ResourceStatus.RUNNING,
        "incompatible-restore": ResourceStatus.RUNNING,
        "insufficient-capacity": ResourceStatus.RUNNING,
        "stopped": ResourceStatus.STOPPED,
        "stopping": ResourceStatus.STOPPED,
        "deleting": ResourceStatus.DELETED,
        "deleted": ResourceStatus.DELETED,
        "failed": ResourceStatus.DELETED,
    }


class RDSResourceCrawler(BaseCrawler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.state_mapper = RDSStateMapper()
        self.rds_client = self.session.client("rds", region_name=self.region)

    def _crawl_rds_instances(self, region: str) -> list[dict[str, Any]]:
        """
        Crawl RDS instances in a specific region.

        :param region: AWS region to crawl
        :return: List of RDS instance data
        """
        rds_client = self.session.client("rds", region_name=region)
        instances = []
        for attempt in range(self.max_retries):
            try:
                paginator = rds_client.get_paginator("describe_db_instances")
                for page in paginator.paginate():
                    instances.extend(page["DBInstances"])
                return instances
            except ClientError as e:
                if attempt == self.max_retries - 1:
                    logger.exception(
                        f"Error crawling RDS instances in {region} after {self.max_retries} attempts: {e}"
                    )
                    return []
                time.sleep(self.retry_delay)
        return []

    def _map_rds_to_resource(self, instance: dict[str, Any], region: str) -> Resource:
        """
        Map RDS instance data to a Resource object.

        :param instance: RDS instance data
        :param region: AWS region of the instance
        :return: Resource object
        """
        tags = {tag["Key"]: tag["Value"] for tag in instance.get("TagList", [])}

        config = {
            "Engine": instance["Engine"],
            "EngineVersion": instance["EngineVersion"],
            "DBInstanceClass": instance["DBInstanceClass"],
            "AllocatedStorage": instance["AllocatedStorage"],
            "Endpoint": instance.get("Endpoint", {}).get("Address"),
            "Port": instance.get("Endpoint", {}).get("Port"),
            "MultiAZ": instance["MultiAZ"],
            "PubliclyAccessible": instance["PubliclyAccessible"],
            "StorageType": instance["StorageType"],
            "BackupRetentionPeriod": instance["BackupRetentionPeriod"],
            "VpcId": instance.get("DBSubnetGroup", {}).get("VpcId"),
            "AvailabilityZone": instance.get("AvailabilityZone"),
            "DBParameterGroups": [
                pg["DBParameterGroupName"]
                for pg in instance.get("DBParameterGroups", [])
            ],
            "OptionGroupMemberships": [
                og["OptionGroupName"]
                for og in instance.get("OptionGroupMemberships", [])
            ],
            "PreferredMaintenanceWindow": instance.get("PreferredMaintenanceWindow"),
            "PreferredBackupWindow": instance.get("PreferredBackupWindow"),
            "Iops": instance.get("Iops"),
            "StorageEncrypted": instance.get("StorageEncrypted", False),
            "PerformanceInsightsEnabled": instance.get(
                "PerformanceInsightsEnabled", False
            ),
            "DeletionProtection": instance.get("DeletionProtection", False),
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=instance["DBInstanceIdentifier"],
            type=AWSResourceType.RDS,
            resource_id=instance["DBInstanceArn"],
            region=region if region else "",
            tags=tags,
            configurations=config,
            description=f"RDS instance of engine {instance['Engine']} version {instance['EngineVersion']}",
            status=self.state_mapper.map_state(instance["DBInstanceStatus"]),
            provider=CloudProvider.AWS,
            category=RESOURCE_TYPE_MAPPINGS[CloudProvider.AWS][
                AWSResourceType.RDS
            ].category,
        )

    @aws_retry()
    def crawl_resources_and_metrics(self, session: Session) -> list[Resource]:
        """
        Crawl all RDS resources.

        :param db: SQLAlchemy database session
        :return: List of Resource objects
        """
        all_resources = []

        # Validate region is available
        if not self.region:
            logger.error("No region specified for RDS crawler")
            return all_resources

        instances = self._crawl_rds_instances(self.region)

        if instances:
            resources = [
                self._map_rds_to_resource(instance, self.region)
                for instance in instances
            ]
            for resource in resources:
                db_resource = ResourceRepository.create_or_update(session, resource)
                all_resources.append(db_resource)

        session.commit()
        logger.info(f"Crawled {len(all_resources)} RDS resources")
        return all_resources
