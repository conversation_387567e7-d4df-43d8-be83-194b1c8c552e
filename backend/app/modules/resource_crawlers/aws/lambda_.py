from typing import Any

import boto3
from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    AWSResourceType,
    CloudProvider,
    Resource,
    ResourceStatus,
)
from app.repositories.resources import ResourceRepository

from .base_crawler import (
    BaseCrawler,
    ResourceStateMapper,
    aws_retry,
)


class LambdaStateMapper(ResourceStateMapper):
    """Maps Lambda function states to ResourceStatus"""

    _STATE_MAPPING = {
        "active": ResourceStatus.RUNNING,
        "pending": ResourceStatus.STARTING,
        "failed": ResourceStatus.DELETED,
        "inactive": ResourceStatus.STOPPED,
    }


class LambdaResourceCrawler(BaseCrawler):
    """Crawler for AWS Lambda resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.state_mapper = LambdaStateMapper()
        self.client = boto3.client(
            "lambda",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    @aws_retry()
    def crawl_resources_and_metrics(self, session: Session) -> list[Resource]:
        """Crawl AWS Lambda resources"""
        all_resources = []

        if not self.region:
            logger.error("No region specified for Lambda crawler")
            return all_resources

        try:
            # Get all functions
            paginator = self.client.get_paginator("list_functions")
            for page in paginator.paginate():
                for function in page["Functions"]:
                    try:
                        # Get function tags
                        tags = self.client.list_tags(Resource=function["FunctionArn"])[
                            "Tags"
                        ]

                        resource = self._map_function_to_resource(function, tags)
                        db_resource = ResourceRepository.create_or_update(
                            session, resource
                        )
                        all_resources.append(db_resource)
                    except ClientError as e:
                        logger.exception(
                            f"Error getting details for function {function['FunctionName']}: {e}"
                        )
                        continue

            session.commit()
            logger.info(f"Crawled {len(all_resources)} AWS Lambda resources")
            return all_resources

        except ClientError as e:
            logger.exception(f"Error crawling AWS Lambda resources: {e}")
            raise

    def _map_function_to_resource(
        self, function: dict[str, Any], tags: dict[str, str]
    ) -> Resource:
        """Map Lambda function data to a Resource object."""
        config = {
            "function_name": function["FunctionName"],
            "function_arn": function["FunctionArn"],
            "runtime": function["Runtime"],
            "role": function["Role"],
            "handler": function["Handler"],
            "code_size": function["CodeSize"],
            "description": function.get("Description"),
            "timeout": function["Timeout"],
            "memory_size": function["MemorySize"],
            "last_modified": function["LastModified"],
            "code_sha256": function["CodeSha256"],
            "version": function["Version"],
            "vpc_config": function.get("VpcConfig", {}),
            "environment": function.get("Environment", {}),
            "kms_key_arn": function.get("KMSKeyArn"),
            "tracing_config": function.get("TracingConfig", {}),
            "master_arn": function.get("MasterArn"),
            "revision_id": function.get("RevisionId"),
            "layers": function.get("Layers", []),
            "state": function.get("State"),
            "state_reason": function.get("StateReason"),
            "state_reason_code": function.get("StateReasonCode"),
            "last_update_status": function.get("LastUpdateStatus"),
            "last_update_status_reason": function.get("LastUpdateStatusReason"),
            "last_update_status_reason_code": function.get(
                "LastUpdateStatusReasonCode"
            ),
            "file_system_configs": function.get("FileSystemConfigs", []),
            "package_type": function.get("PackageType"),
            "image_config_response": function.get("ImageConfigResponse", {}),
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=function["FunctionName"],
            region=self.region if self.region else "",
            type=AWSResourceType.LAMBDA,
            resource_id=function["FunctionArn"],
            tags=tags,
            description=f"Lambda function running {function['Runtime']}",
            configurations=config,
            status=self.state_mapper.map_state(function.get("State", "active")),
            provider=CloudProvider.AWS,
            category=RESOURCE_TYPE_MAPPINGS[CloudProvider.AWS][
                AWSResourceType.LAMBDA
            ].category,
        )
