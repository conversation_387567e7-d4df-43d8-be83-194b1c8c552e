from typing import Any

import boto3
from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    AWSResourceType,
    CloudProvider,
    Resource,
    ResourceStatus,
)
from app.repositories.resources import ResourceRepository

from .base_crawler import (
    Base<PERSON>rawler,
    ResourceStateMapper,
    aws_retry,
)


class NeptuneStateMapper(ResourceStateMapper):
    """Maps Neptune cluster states to ResourceStatus"""

    _STATE_MAPPING = {
        "creating": ResourceStatus.STARTING,
        "available": ResourceStatus.RUNNING,
        "deleting": ResourceStatus.DELETED,
        "deleted": ResourceStatus.DELETED,
        "failed": ResourceStatus.STOPPED,
        "incompatible-restore": ResourceStatus.STOPPED,
        "incompatible-parameters": ResourceStatus.STOPPED,
        "incompatible-network": ResourceStatus.STOPPED,
        "incompatible-credentials": ResourceStatus.STOPPED,
        "incompatible-option-group": ResourceStatus.STOPPED,
        "incompatible-parameter-group": ResourceStatus.STOPPED,
        "incompatible-security-group": ResourceStatus.STOPPED,
        "incompatible-subnet": ResourceStatus.STOPPED,
        "incompatible-vpc": ResourceStatus.STOPPED,
        "incompatible-version": ResourceStatus.STOPPED,
        "incompatible-zone": ResourceStatus.STOPPED,
        "modifying": ResourceStatus.STARTING,
        "rebooting": ResourceStatus.STARTING,
        "renaming": ResourceStatus.STARTING,
        "resizing": ResourceStatus.STARTING,
        "rotating-keys": ResourceStatus.STARTING,
        "storage-full": ResourceStatus.STOPPED,
        "updating-hsm": ResourceStatus.STARTING,
    }


class NeptuneResourceCrawler(BaseCrawler):
    """Crawler for Neptune resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.state_mapper = NeptuneStateMapper()
        self.client = boto3.client(
            "neptune",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    @aws_retry()
    def crawl_resources_and_metrics(self, session: Session) -> list[Resource]:
        """Crawl Neptune resources"""
        all_resources = []

        if not self.region:
            logger.error("No region specified for Neptune crawler")
            return all_resources

        try:
            # Get all clusters
            paginator = self.client.get_paginator("describe_db_clusters")
            for page in paginator.paginate():
                for cluster in page["DBClusters"]:
                    try:
                        # Get cluster tags
                        tags = self.client.list_tags_for_resource(
                            ResourceName=cluster["DBClusterArn"]
                        )["TagList"]

                        resource = self._map_cluster_to_resource(cluster, tags)
                        db_resource = ResourceRepository.create_or_update(
                            session, resource
                        )
                        all_resources.append(db_resource)
                    except ClientError as e:
                        logger.exception(
                            f"Error getting details for cluster {cluster['DBClusterIdentifier']}: {e}"
                        )
                        continue

            session.commit()
            logger.info(f"Crawled {len(all_resources)} Neptune resources")
            return all_resources

        except ClientError as e:
            logger.exception(f"Error crawling Neptune resources: {e}")
            raise

    def _map_cluster_to_resource(
        self, cluster: dict[str, Any], tags: list[dict[str, str]]
    ) -> Resource:
        """Map Neptune cluster data to a Resource object."""
        config = {
            "cluster_identifier": cluster["DBClusterIdentifier"],
            "engine": cluster["Engine"],
            "engine_version": cluster["EngineVersion"],
            "status": cluster["Status"],
            "master_username": cluster["MasterUsername"],
            "endpoint": cluster["Endpoint"],
            "reader_endpoint": cluster.get("ReaderEndpoint"),
            "port": cluster.get("Port"),
            "vpc_security_groups": cluster.get("VpcSecurityGroups", []),
            "db_subnet_group": cluster.get("DBSubnetGroup", {}),
            "availability_zones": cluster.get("AvailabilityZones", []),
            "backup_retention_period": cluster.get("BackupRetentionPeriod"),
            "preferred_backup_window": cluster.get("PreferredBackupWindow"),
            "preferred_maintenance_window": cluster.get("PreferredMaintenanceWindow"),
            "storage_encrypted": cluster.get("StorageEncrypted", False),
            "kms_key_id": cluster.get("KmsKeyId"),
            "db_cluster_parameter_group": cluster.get("DBClusterParameterGroup"),
            "deletion_protection": cluster.get("DeletionProtection", False),
            "hosted_zone_id": cluster.get("HostedZoneId"),
            "iam_database_authentication_enabled": cluster.get(
                "IAMDatabaseAuthenticationEnabled", False
            ),
            "cluster_create_time": cluster.get("ClusterCreateTime", "").isoformat()
            if cluster.get("ClusterCreateTime")
            else None,
            "db_cluster_members": cluster.get("DBClusterMembers", []),
            "associated_roles": cluster.get("AssociatedRoles", []),
            "db_cluster_option_group_memberships": cluster.get(
                "DBClusterOptionGroupMemberships", []
            ),
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=cluster["DBClusterIdentifier"],
            region=self.region if self.region else "",
            type=AWSResourceType.NEPTUNE,
            resource_id=cluster["DBClusterArn"],
            tags={tag["Key"]: tag["Value"] for tag in tags},
            description=f"Neptune cluster running {cluster['Engine']} {cluster['EngineVersion']}",
            configurations=config,
            status=self.state_mapper.map_state(cluster["Status"]),
            provider=CloudProvider.AWS,
            category=RESOURCE_TYPE_MAPPINGS[CloudProvider.AWS][
                AWSResourceType.NEPTUNE
            ].category,
        )
