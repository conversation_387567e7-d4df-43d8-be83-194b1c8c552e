import time
from typing import Any

from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    AWSResourceType,
    CloudProvider,
    Resource,
    ResourceStatus,
)
from app.repositories.resources import ResourceRepository

from .base_crawler import (
    BaseCrawler,
    ResourceStateMapper,
    aws_retry,
)


class BatchStateMapper(ResourceStateMapper):
    """Maps AWS Batch resource states to ResourceStatus"""

    _STATE_MAPPING = {
        # Compute Environment states
        "enabled": ResourceStatus.RUNNING,
        "disabled": ResourceStatus.STOPPED,
        "deleting": ResourceStatus.DELETED,
        "deleted": ResourceStatus.DELETED,
        "creating": ResourceStatus.STARTING,
        "updating": ResourceStatus.STARTING,
        "failed": ResourceStatus.STOPPED,
    }


class BatchResourceCrawler(BaseCrawler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.state_mapper = BatchStateMapper()
        self.batch_client = self.session.client("batch", region_name=self.region)

    def _crawl_compute_environments(self) -> list[dict[str, Any]]:
        """Crawl AWS Batch compute environments in the specified region."""
        compute_environments = []
        for attempt in range(self.max_retries):
            try:
                paginator = self.batch_client.get_paginator(
                    "describe_compute_environments"
                )
                for page in paginator.paginate():
                    for ce in page["computeEnvironments"]:
                        try:
                            # Get compute environment details
                            ce_details = (
                                self.batch_client.describe_compute_environments(
                                    computeEnvironments=[ce]
                                )["computeEnvironments"][0]
                            )

                            # Get compute environment tags
                            tags = {}
                            try:
                                tag_response = self.batch_client.list_tags_for_resource(
                                    resourceArn=ce_details["computeEnvironmentArn"]
                                )
                                tags = {
                                    tag["key"]: tag["value"]
                                    for tag in tag_response.get("tags", [])
                                }
                            except ClientError:
                                # Compute environment may not have tags
                                pass

                            ce_data = {
                                "ComputeEnvironmentName": ce_details[
                                    "computeEnvironmentName"
                                ],
                                "ComputeEnvironmentArn": ce_details[
                                    "computeEnvironmentArn"
                                ],
                                "Type": ce_details["type"],
                                "State": ce_details["state"],
                                "Status": ce_details["status"],
                                "StatusReason": ce_details.get("statusReason"),
                                "ComputeResources": ce_details.get(
                                    "computeResources", {}
                                ),
                                "ServiceRole": ce_details.get("serviceRole"),
                                "Tags": tags,
                            }
                            compute_environments.append(ce_data)
                        except ClientError as e:
                            logger.error(
                                f"Error getting details for compute environment {ce}: {e}"
                            )
                return compute_environments
            except ClientError as e:
                if attempt == self.max_retries - 1:
                    logger.error(
                        f"Error crawling compute environments in {self.region} after {self.max_retries} attempts: {e}"
                    )
                    return []
                time.sleep(self.retry_delay)
        return []

    def _crawl_job_queues(self) -> list[dict[str, Any]]:
        """Crawl AWS Batch job queues in the specified region."""
        job_queues = []
        for attempt in range(self.max_retries):
            try:
                paginator = self.batch_client.get_paginator("describe_job_queues")
                for page in paginator.paginate():
                    for jq in page["jobQueues"]:
                        try:
                            # Get job queue tags
                            tags = {}
                            try:
                                tag_response = self.batch_client.list_tags_for_resource(
                                    resourceArn=jq["jobQueueArn"]
                                )
                                tags = {
                                    tag["key"]: tag["value"]
                                    for tag in tag_response.get("tags", [])
                                }
                            except ClientError:
                                # Job queue may not have tags
                                pass

                            jq_data = {
                                "JobQueueName": jq["jobQueueName"],
                                "JobQueueArn": jq["jobQueueArn"],
                                "State": jq["state"],
                                "Status": jq["status"],
                                "StatusReason": jq.get("statusReason"),
                                "Priority": jq["priority"],
                                "ComputeEnvironmentOrder": jq[
                                    "computeEnvironmentOrder"
                                ],
                                "Tags": tags,
                            }
                            job_queues.append(jq_data)
                        except ClientError as e:
                            logger.error(
                                f"Error getting details for job queue {jq['jobQueueName']}: {e}"
                            )
                return job_queues
            except ClientError as e:
                if attempt == self.max_retries - 1:
                    logger.error(
                        f"Error crawling job queues in {self.region} after {self.max_retries} attempts: {e}"
                    )
                    return []
                time.sleep(self.retry_delay)
        return []

    def _map_batch_to_resource(
        self, resource: dict[str, Any], resource_type: str
    ) -> Resource:
        """Map AWS Batch resource data to a Resource object."""
        if resource_type == "COMPUTE_ENVIRONMENT":
            config = {
                "Type": resource["Type"],
                "State": resource["State"],
                "Status": resource["Status"],
                "StatusReason": resource.get("StatusReason"),
                "ComputeResources": resource["ComputeResources"],
                "ServiceRole": resource.get("ServiceRole"),
            }
            description = f"Batch compute environment in {resource['State']} state"
        else:  # JOB_QUEUE
            config = {
                "State": resource["State"],
                "Status": resource["Status"],
                "StatusReason": resource.get("StatusReason"),
                "Priority": resource["Priority"],
                "ComputeEnvironmentOrder": resource["ComputeEnvironmentOrder"],
            }
            description = f"Batch job queue with priority {resource['Priority']}"

        return Resource(
            workspace_id=self.workspace_id,
            name=resource["JobQueueName"]
            if resource_type == "JOB_QUEUE"
            else resource["ComputeEnvironmentName"],
            region=self.region if self.region else "",
            type=AWSResourceType.BATCH,
            resource_id=resource["JobQueueArn"]
            if resource_type == "JOB_QUEUE"
            else resource["ComputeEnvironmentArn"],
            tags=resource["Tags"],
            description=description,
            configurations=config,
            status=self.state_mapper.map_state(resource["State"]),
            provider=CloudProvider.AWS,
            category=RESOURCE_TYPE_MAPPINGS[CloudProvider.AWS][
                AWSResourceType.BATCH
            ].category,
        )

    @aws_retry()
    def crawl_resources_and_metrics(self, session: Session) -> list[Resource]:
        """Crawl all AWS Batch resources."""
        all_resources = []

        # Crawl compute environments
        compute_environments = self._crawl_compute_environments()
        if compute_environments:
            resources = [
                self._map_batch_to_resource(ce, "COMPUTE_ENVIRONMENT")
                for ce in compute_environments
            ]
            for resource in resources:
                db_resource = ResourceRepository.create_or_update(session, resource)
                all_resources.append(db_resource)

        # Crawl job queues
        job_queues = self._crawl_job_queues()
        if job_queues:
            resources = [
                self._map_batch_to_resource(jq, "JOB_QUEUE") for jq in job_queues
            ]
            for resource in resources:
                db_resource = ResourceRepository.create_or_update(session, resource)
                all_resources.append(db_resource)

        session.commit()
        logger.info(f"Crawled {len(all_resources)} AWS Batch resources")
        return all_resources
