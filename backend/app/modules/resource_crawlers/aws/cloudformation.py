from typing import Any

from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    AWSResourceType,
    CloudProvider,
    Resource,
    ResourceStatus,
)
from app.repositories.resources import ResourceRepository

from .base_crawler import (
    BaseCrawler,
    ResourceStateMapper,
    aws_retry,
)


class CloudFormationStateMapper(ResourceStateMapper):
    """Maps CloudFormation stack states to ResourceStatus"""

    _STATE_MAPPING = {
        "create_complete": ResourceStatus.RUNNING,
        "create_in_progress": ResourceStatus.STARTING,
        "create_failed": ResourceStatus.STOPPED,
        "delete_complete": ResourceStatus.DELETED,
        "delete_failed": ResourceStatus.STOPPED,
        "delete_in_progress": ResourceStatus.DELETED,
        "review_in_progress": ResourceStatus.STARTING,
        "rollback_complete": ResourceStatus.STOPPED,
        "rollback_failed": ResourceStatus.STOPPED,
        "rollback_in_progress": ResourceStatus.STARTING,
        "update_complete": ResourceStatus.RUNNING,
        "update_complete_cleanup_in_progress": ResourceStatus.STARTING,
        "update_in_progress": ResourceStatus.STARTING,
        "update_rollback_complete": ResourceStatus.STOPPED,
        "update_rollback_complete_cleanup_in_progress": ResourceStatus.STARTING,
        "update_rollback_failed": ResourceStatus.STOPPED,
        "update_rollback_in_progress": ResourceStatus.STARTING,
    }


class CloudFormationCrawler(BaseCrawler):
    # Set of active stack statuses
    ACTIVE_STACK_STATUSES: set[str] = {
        "CREATE_COMPLETE",
        "UPDATE_COMPLETE",
        "UPDATE_ROLLBACK_COMPLETE",
        "UPDATE_IN_PROGRESS",
        "CREATE_IN_PROGRESS",
        "UPDATE_ROLLBACK_IN_PROGRESS",
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.state_mapper = CloudFormationStateMapper()

    def _is_active_stack(self, stack: dict[str, Any]) -> bool:
        """Check if the stack is active and not being deleted."""
        return (
            not stack.get("RootId")  # Not a nested stack
            and stack["StackStatus"] in self.ACTIVE_STACK_STATUSES
        )

    def _crawl_active_main_stacks(self, region: str) -> list[dict[str, Any]]:
        """Crawl active main CloudFormation stacks in a specific region."""
        cfn_client = self.session.client("cloudformation", region_name=region)
        active_stacks = []
        try:
            paginator = cfn_client.get_paginator("list_stacks")
            for page in paginator.paginate():
                # Filter for active main stacks
                stacks = [
                    stack
                    for stack in page["StackSummaries"]
                    if self._is_active_stack(stack)
                ]
                active_stacks.extend(stacks)

            logger.info(f"Found {len(active_stacks)} active main stacks in {region}")
        except ClientError as e:
            logger.error(f"Error crawling CloudFormation stacks in {region}: {e}")
        return active_stacks

    def _get_stack_details(self, stack_name: str, region: str) -> dict[str, Any]:
        """Get detailed information for a specific CloudFormation stack."""
        cfn_client = self.session.client("cloudformation", region_name=region)
        try:
            response = cfn_client.describe_stacks(StackName=stack_name)
            return response["Stacks"][0]
        except ClientError as e:
            logger.error(f"Error getting details for stack {stack_name}: {e}")
            return {}

    def _map_stack_to_resource(self, stack: dict[str, Any], region: str) -> Resource:
        """Map CloudFormation stack data to a Resource object."""
        # Get detailed stack information
        stack_details = self._get_stack_details(stack["StackName"], region)

        tags = {tag["Key"]: tag["Value"] for tag in stack_details.get("Tags", [])}
        resource = Resource(
            workspace_id=self.workspace_id,
            name=stack["StackName"],
            region=region,
            type=AWSResourceType.CLOUDFORMATION,
            resource_id=stack["StackId"],
            tags=tags,
            description=f"CloudFormation main stack in {stack['StackStatus']} state",
            configurations={
                "creation_time": stack["CreationTime"].isoformat(),
                "status": stack["StackStatus"],
                "status_reason": stack.get("StackStatusReason", ""),
                "template_description": stack_details.get("Description", ""),
                "parameters": stack_details.get("Parameters", []),
                "capabilities": stack_details.get("Capabilities", []),
                "last_updated": stack_details.get("LastUpdatedTime", "").isoformat()
                if stack_details.get("LastUpdatedTime")
                else None,
                "enable_termination_protection": stack_details.get(
                    "EnableTerminationProtection", False
                ),
                "rollback_configuration": stack_details.get(
                    "RollbackConfiguration", {}
                ),
                "drift_information": stack_details.get("DriftInformation", {}),
            },
            status=self.state_mapper.map_state(stack["StackStatus"]),
            provider=CloudProvider.AWS,
            category=RESOURCE_TYPE_MAPPINGS[CloudProvider.AWS][
                AWSResourceType.CLOUDFORMATION
            ].category,
        )
        return resource

    @aws_retry()
    def crawl_resources_and_metrics(self, session: Session) -> list[Resource]:
        """Crawl all active main CloudFormation stacks."""
        all_resources = []

        # Validate region is available
        if not self.region:
            logger.error("No region specified for CloudFormation crawler")
            return all_resources

        active_stacks = self._crawl_active_main_stacks(self.region)

        for stack in active_stacks:
            # Create or update resource
            resource = self._map_stack_to_resource(stack, self.region)
            db_resource = ResourceRepository.create_or_update(session, resource)
            all_resources.append(db_resource)

        session.commit()

        logger.info(f"Crawled {len(all_resources)} active main CloudFormation stacks")
        return all_resources
