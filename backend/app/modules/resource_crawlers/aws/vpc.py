import time
from typing import Any

from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    AWSResourceType,
    CloudProvider,
    Resource,
    ResourceStatus,
)
from app.repositories.resources import ResourceRepository

from .base_crawler import (
    BaseCrawler,
    ResourceStateMapper,
    aws_retry,
)


class VPCStateMapper(ResourceStateMapper):
    """Maps VPC states to ResourceStatus"""

    _STATE_MAPPING = {
        "pending": ResourceStatus.STARTING,
        "available": ResourceStatus.RUNNING,
        "deleting": ResourceStatus.DELETED,
        "deleted": ResourceStatus.DELETED,
    }


class VPCCrawler(BaseCrawler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.state_mapper = VPCStateMapper()
        self.vpc_client = self.session.client("ec2", region_name=self.region)

    def _crawl_vpcs(self, region: str) -> list[dict[str, Any]]:
        """
        Crawl VPCs in a specific region.

        :param region: AWS region to crawl
        :return: List of VPC data
        """
        vpc_client = self.session.client("ec2", region_name=region)
        vpcs = []

        for attempt in range(self.max_retries):
            try:
                paginator = vpc_client.get_paginator("describe_vpcs")
                for page in paginator.paginate():
                    # Get full VPC details including associated resources
                    for vpc in page["Vpcs"]:
                        vpc_id = vpc["VpcId"]

                        # Get subnets
                        subnets = vpc_client.describe_subnets(
                            Filters=[{"Name": "vpc-id", "Values": [vpc_id]}]
                        )["Subnets"]

                        # Get route tables
                        route_tables = vpc_client.describe_route_tables(
                            Filters=[{"Name": "vpc-id", "Values": [vpc_id]}]
                        )["RouteTables"]

                        # Get internet gateways
                        igws = vpc_client.describe_internet_gateways(
                            Filters=[{"Name": "attachment.vpc-id", "Values": [vpc_id]}]
                        )["InternetGateways"]

                        vpc.update(
                            {
                                "Subnets": subnets,
                                "RouteTables": route_tables,
                                "InternetGateways": igws,
                            }
                        )
                        vpcs.append(vpc)

                return vpcs

            except ClientError as e:
                if attempt == self.max_retries - 1:
                    logger.exception(
                        f"Error crawling VPCs in {region} after {self.max_retries} attempts: {e}"
                    )
                    return []
                time.sleep(self.retry_delay)
        return []

    def _map_vpc_to_resource(self, vpc: dict[str, Any], region: str) -> Resource:
        """
        Map VPC data to a Resource object.

        :param vpc: VPC data
        :param region: AWS region of the VPC
        :return: Resource object
        """
        tags = {tag["Key"]: tag["Value"] for tag in vpc.get("Tags", [])}

        config = {
            "CidrBlock": vpc["CidrBlock"],
            "DhcpOptionsId": vpc.get("DhcpOptionsId"),
            "State": vpc["State"],
            "InstanceTenancy": vpc["InstanceTenancy"],
            "IsDefault": vpc.get("IsDefault", False),
            "EnableDnsHostnames": vpc.get("EnableDnsHostnames", False),
            "EnableDnsSupport": vpc.get("EnableDnsSupport", True),
            "Subnets": [
                {
                    "SubnetId": subnet["SubnetId"],
                    "CidrBlock": subnet["CidrBlock"],
                    "AvailabilityZone": subnet["AvailabilityZone"],
                    "State": subnet["State"],
                }
                for subnet in vpc.get("Subnets", [])
            ],
            "RouteTables": [
                {
                    "RouteTableId": rt["RouteTableId"],
                    "Routes": [
                        {
                            "DestinationCidrBlock": route.get("DestinationCidrBlock"),
                            "GatewayId": route.get("GatewayId"),
                            "State": route.get("State"),
                        }
                        for route in rt.get("Routes", [])
                    ],
                }
                for rt in vpc.get("RouteTables", [])
            ],
            "InternetGateways": [
                igw["InternetGatewayId"] for igw in vpc.get("InternetGateways", [])
            ],
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=vpc["VpcId"],
            type=AWSResourceType.VPC,
            resource_id=f"arn:aws:ec2:{region}:{self.aws_access_account_id}:vpc/{vpc['VpcId']}",
            region=region if region else "",
            tags=tags,
            configurations=config,
            description=f"VPC with CIDR {vpc['CidrBlock']}",
            status=self.state_mapper.map_state(vpc["State"]),
            provider=CloudProvider.AWS,
            category=RESOURCE_TYPE_MAPPINGS[CloudProvider.AWS][
                AWSResourceType.VPC
            ].category,
        )

    @aws_retry()
    def crawl_resources_and_metrics(self, session: Session) -> list[Resource]:
        """
        Crawl all VPC resources.

        :param db: SQLAlchemy database session
        :return: List of Resource objects
        """
        all_resources = []

        # Validate region is available
        if not self.region:
            logger.error("No region specified for VPC crawler")
            return all_resources

        vpcs = self._crawl_vpcs(self.region)

        if vpcs:
            resources = [self._map_vpc_to_resource(vpc, self.region) for vpc in vpcs]
            for resource in resources:
                db_resource = ResourceRepository.create_or_update(session, resource)
                all_resources.append(db_resource)

        session.commit()
        logger.info(f"Crawled {len(all_resources)} VPC resources")
        return all_resources
