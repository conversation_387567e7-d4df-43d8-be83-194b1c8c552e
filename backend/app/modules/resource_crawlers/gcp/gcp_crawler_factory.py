import uuid

from app.models import GCPResourceType

from .base_gcp_crawler import BaseGCPCrawler


class GCPCrawlerFactory:
    """Factory for creating GCP resource crawlers"""

    @staticmethod
    def get_crawler(
        workspace_id: uuid.UUID,
        scan_type: GCPResourceType,
        project_id: str,
        service_account_key: str | None = None,
        region: str | None = None,
        max_retries: int = 3,
        retry_delay: int = 5,
    ) -> BaseGCPCrawler:
        """Create a GCP resource crawler based on the scan type"""
        # TODO: Implement specific GCP crawlers
        raise NotImplementedError(f"GCP crawler for {scan_type} not yet implemented")
