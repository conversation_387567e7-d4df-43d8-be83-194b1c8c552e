import uuid
from abc import abstractmethod

from sqlmodel import Session

from app.logger import logger
from app.models import Resource

from ..base_cloud_crawler import BaseCloudCrawler


class BaseGCPCrawler(BaseCloudCrawler):
    """GCP-specific base crawler implementation"""

    def __init__(
        self,
        workspace_id: uuid.UUID,
        project_id: str,
        service_account_key: str | None = None,
        region: str | None = None,
        max_retries: int = 3,
        retry_delay: int = 5,
    ):
        super().__init__(workspace_id, region, max_retries, retry_delay)
        self.project_id = project_id
        self.service_account_key = service_account_key

    def get_provider_name(self) -> str:
        """Return GCP as the provider name"""
        return "gcp"

    def validate_credentials(self) -> bool:
        """Validate GCP credentials - placeholder implementation"""
        # TODO: Implement GCP credential validation
        logger.warning("GCP credential validation not yet implemented")
        return True

    @abstractmethod
    def crawl_resources_and_metrics(self, db: Session) -> list[Resource]:
        pass
