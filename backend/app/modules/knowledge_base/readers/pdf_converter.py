"""PDF to image conversion utilities using pdf2image."""

import asyncio
import base64
from io import BytesIO

from pdf2image import convert_from_path

from app.logger import logger


async def convert_pdf_to_images(
    pdf_path: str, dpi: int = 300, format: str = "PNG"
) -> list[tuple[bytes, str]]:
    try:
        # Run the conversion in a thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        images = await loop.run_in_executor(
            None,
            lambda: convert_from_path(
                pdf_path, dpi=dpi, fmt=format.lower(), thread_count=4
            ),
        )

        image_data = []
        for i, image in enumerate(images):
            # Convert PIL Image to bytes
            buffer = BytesIO()
            image.save(buffer, format=format, optimize=True)
            image_bytes = buffer.getvalue()
            buffer.close()

            page_info = f"page_{i + 1}"
            image_data.append((image_bytes, page_info))

        logger.info(
            f"Successfully converted PDF {pdf_path} to {len(image_data)} images"
        )
        return image_data

    except Exception as e:
        logger.error(f"Error converting PDF {pdf_path} to images: {e}")
        raise


def image_to_base64(image_bytes: bytes) -> str:
    return base64.b64encode(image_bytes).decode("utf-8")


async def convert_pdf_to_base64_images(
    pdf_path: str, dpi: int = 300, format: str = "PNG"
) -> list[tuple[str, str]]:
    image_data = await convert_pdf_to_images(pdf_path, dpi, format)

    base64_images = []
    for image_bytes, page_info in image_data:
        base64_image = image_to_base64(image_bytes)
        base64_images.append((base64_image, page_info))

    return base64_images
