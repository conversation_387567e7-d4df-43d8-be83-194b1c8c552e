"""PDF reader with multimodal support - converts PDFs to images and embeds them."""

import asyncio
import base64
import tempfile
import uuid
from pathlib import Path

from llama_index.core.schema import Document

from app.core.config import settings
from app.logger import logger
from app.models import DocumentKB
from app.modules.knowledge_base.readers.pdf_converter import convert_pdf_to_images
from app.repositories.object_storage.base import BaseStorageRepository
from app.repositories.object_storage.provider import get_object_storage_repository


async def read_pdf_as_multimodal(
    docs_to_ingest: list[DocumentKB], include_images: bool = True, dpi: int = 300
) -> tuple[list[Document], list[DocumentKB]]:
    """
    Process PDF documents by converting them to images and optionally extracting text.

    Args:
        docs_to_ingest: List of PDF documents to process
        include_images: Whether to include image representation of pages
        dpi: Resolution for PDF to image conversion

    Returns:
        Tuple of (processed_documents, successful_docs)
    """
    if not docs_to_ingest:
        return [], []

    # Filter for PDF files only
    pdf_docs = [
        doc
        for doc in docs_to_ingest
        if doc.file_type and ("pdf" in doc.file_type.lower())
    ]
    if not pdf_docs:
        logger.warning("No PDF documents found to process")
        return [], []

    tmp_dir = tempfile.mkdtemp()
    try:
        # Download PDF files
        download_tasks = [_download_document(doc, tmp_dir) for doc in pdf_docs]
        download_results = await asyncio.gather(*download_tasks, return_exceptions=True)

        successful_docs = []
        all_documents = []

        for doc, result in zip(pdf_docs, download_results, strict=False):
            if isinstance(result, Exception):
                logger.error(f"Failed to download {doc.object_name}: {result}")
                continue

            local_path = str(result)
            successful_docs.append(doc)

            try:
                # Convert PDF to images
                if include_images:
                    image_docs = await _process_pdf_as_images(doc, local_path, dpi)
                    all_documents.extend(image_docs)

            except Exception as e:
                logger.error(f"Error processing PDF {doc.object_name}: {e}")
                # Remove from successful docs if processing failed
                if doc in successful_docs:
                    successful_docs.remove(doc)

        return all_documents, successful_docs

    except Exception as e:
        logger.error(f"Error in PDF multimodal processing: {e}")
        return [], []

    finally:
        # Clean up temp directory
        import shutil

        try:
            shutil.rmtree(tmp_dir)
        except OSError:
            logger.warning(f"Failed to clean up temp directory: {tmp_dir}")


async def _download_document(doc: DocumentKB, tmp_dir: str) -> str:
    if not doc.object_name:
        raise ValueError(f"Document {doc.id} has no object_name")

    local_path = Path(tmp_dir) / Path(doc.object_name).name
    ob_repo: BaseStorageRepository = get_object_storage_repository()
    await ob_repo.download_file(
        doc.object_name,
        file_path=str(local_path),
        bucket_name=settings.KB_BUCKET,
    )
    return str(local_path)


async def _process_pdf_as_images(
    doc: DocumentKB, pdf_path: str, dpi: int = 300
) -> list[Document]:
    """Convert PDF pages to image documents."""
    try:
        # Convert PDF to images (as bytes)
        image_data = await convert_pdf_to_images(pdf_path, dpi=dpi)

        documents = []
        for image_bytes, page_info in image_data:
            # Create document with image content
            doc_content = f"PDF Page Image: {doc.file_name} - {page_info}"

            # Create regular Document with image metadata
            # We'll store the image as base64 in metadata for embedding
            image_base64 = base64.b64encode(image_bytes).decode("utf-8")

            # Generate a proper UUID for the image document
            image_doc_id = str(uuid.uuid4())

            document = Document(
                text=doc_content,
                metadata={
                    "kb_id": str(doc.kb_id),
                    "document_id": str(doc.id),
                    "object_name": doc.object_name,
                    "file_name": doc.file_name,
                    "file_type": doc.file_type,
                    "page_info": page_info,
                    "content_type": "image",
                    "image_base64": image_base64,
                    "dpi": dpi,
                    "parent_document_id": str(
                        doc.id
                    ),  # Keep reference to original document
                },
            )
            document.doc_id = image_doc_id
            documents.append(document)

        logger.info(
            f"Converted PDF {doc.file_name} to {len(documents)} image documents"
        )
        return documents

    except Exception as e:
        logger.error(f"Error converting PDF {doc.file_name} to images: {e}")
        raise
