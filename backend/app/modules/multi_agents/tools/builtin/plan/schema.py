from datetime import datetime
from typing import Literal

from pydantic import BaseModel, Field


class Task(BaseModel):
    """A single task in a plan."""

    id: int = Field(
        description="Unique identifier for the task. MUST be sequential (1, 2, 3, ...) to enforce execution order."
    )
    content: str = Field(description="Brief description of the task.")
    status: Literal["pending", "in_progress", "completed", "cancelled"] = Field(
        description="Status of the task. Default: pending"
    )
    notes: str | None = Field(default=None, description="Notes for the task. Optional.")


class PlanInput(BaseModel):
    command: Literal["create", "update"] = Field(
        description="Command to execute. create: Create a new plan and overwrite the existing one. update: Update an existing plan."
    )
    tasks: list[Task] = Field(description="Sequential tasks to complete.")

    class Config:
        """Pydantic model configuration."""

        extra = "ignore"
        validate_assignment = True
        arbitrary_types_allowed = True
        use_enum_values = True
        validate_default = True
        populate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
