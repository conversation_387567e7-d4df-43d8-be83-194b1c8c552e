from typing import Literal

from langchain_core.tools import ArgsSchema, BaseTool

from .model import PlanManager
from .schema import PlanInput, Task

_PLANNING_TOOL_DESCRIPTION = """
Use this tool to create and manage a structured task list. This helps you track progress, organize complex tasks, and demonstrate thoroughness to the customer.
It also helps the user understand the progress of the task and overall progress of their requests.

## CRITICAL: Sequential Task Execution Rules
MUST FOLLOW THESE RULES STRICTLY:
1. Tasks MUST be executed in the exact order specified by their ID numbers (1, 2, 3, etc.)
2. You CANNOT start a task until ALL previous tasks are marked as "completed"
3. Only ONE task can be "in_progress" at any time
4. You MUST complete the current task before moving to the next one
5. If a task fails or is blocked, you MUST resolve it before proceeding to subsequent tasks

## When to Use This Tool
Use this tool proactively in these scenarios:

1. Complex multi-step tasks - When a task requires 2 or more distinct steps or actions
2. Non-trivial and complex tasks - Tasks that require careful planning or multiple operations
3. Customer provides multiple tasks - When customers provide a list of things to be done (numbered or comma-separated)
4. After completing a task - Mark it complete and add any new follow-up tasks
5. When you start working on a new task, mark the todo as in_progress. Ideally you should only have one todo as in_progress at a time. Complete existing tasks before starting new ones.

## Task Execution Workflow
1. Create tasks with sequential IDs (1, 2, 3, ...)
2. Start with task ID 1, mark it "in_progress"
3. Complete task 1, mark it "completed"
4. Move to task ID 2, mark it "in_progress"
5. Repeat until all tasks are completed
6. NEVER skip tasks or work on them out of order
7. If a task fails or is blocked, you MUST resolve it before proceeding to subsequent tasks

## Efficient Task Updates
When using the "update" command:
- ONLY include tasks that have actually changed (status, content, or notes)
- DO NOT include unchanged tasks in the update payload
- Tasks are uniquely identified by ID, so you can update multiple tasks in one batch
- Examples of when to update:
  * Task status changed: pending → in_progress → completed
  * Task content modified or clarified
  * Notes added or updated
- DO NOT send the entire task list just to update one task's status

## When NOT to Use This Tool

Skip using this tool when:
1. There is only a single, straightforward task
2. The task is trivial and tracking it provides no organizational benefit
3. The task can be completed in less than 2 trivial steps
4. The task is purely conversational or informational

NOTE that you should not use this tool if there is only one trivial task to do. In this case you are better off just doing the task directly.
"""


class PlanningTool(BaseTool):
    """A tool for managing multi-step tasks and tracking their progress."""

    name: str = "planning"
    description: str = _PLANNING_TOOL_DESCRIPTION
    args_schema: ArgsSchema = PlanInput
    plan_manager: PlanManager

    def _run(  # type: ignore
        self,
        command: Literal["create", "update"],
        tasks: list[Task],
    ) -> str:
        if command == "create":
            return self._create_plan(tasks)
        elif command == "update":
            return self._update_plan(tasks)
        else:
            return (
                f"Unrecognized command {command}. Allowed commands are: create, update"
            )

    def _create_plan(self, tasks: list[Task]) -> str:
        """Create a new plan"""
        self.plan_manager.set_state(tasks)
        return "Plan created successfully"

    def _update_plan(self, tasks: list[Task]) -> str:
        """Update an existing plan with new tasks."""
        plan_tasks_dict = self.plan_manager.get_plan_tasks_dict()

        if plan_tasks_dict:
            for task in tasks:
                if task.id not in plan_tasks_dict:
                    return f"Task {task.id} not found in plan"

                plan_tasks_dict[task.id] = task

            self.plan_manager.set_state(list(plan_tasks_dict.values()))
        return "Plan updated successfully"

    def get_plan_prompt(self) -> str:
        """Get the plan prompt."""
        return self.plan_manager.get_plan_string()
