from pydantic import BaseModel

from .schema import Task


class PlanManager(BaseModel):
    """Manages the state of a single plan and its progress.

    This class handles the storage and retrieval of a plan, as well as managing
    its state. It provides methods for serializing and deserializing
    the plan state for persistence.
    """

    plan_tasks: list[Task] | None = None

    def get_plan_tasks_dict(self) -> dict[int, Task] | None:
        """Get the current state of the plan as a dictionary of task ids to task objects."""
        return {task.id: task for task in self.plan_tasks} if self.plan_tasks else None

    def set_state(self, plan_tasks: list[Task]) -> None:
        """Set the current state of the plan."""
        self.plan_tasks = plan_tasks

    def get_plan_string(self) -> str:
        """Get a string representation of the current plan."""
        if not self.plan_tasks:
            return "<current_plan>No plan tasks available</current_plan>"

        plan_str = []
        # Sort tasks by id before generating string representation
        sorted_tasks = sorted(self.plan_tasks, key=lambda x: x.id)
        for task in sorted_tasks:
            status_str = f"[{task.status}]"
            task_str = f"{task.id}. {status_str} {task.content}"
            if task.notes:
                task_str += f"\n   Notes: {task.notes}"
            plan_str.append(task_str)

        return f"<current_plan>\n{'\n'.join(plan_str)}\n</current_plan>"

    def get_plan_dict(self) -> list[dict]:
        """Get a dictionary mapping task IDs to their status types.

        Returns:
            A dictionary with task IDs as keys and status types as values.
        """
        if not self.plan_tasks:
            return []

        return [task.model_dump() for task in self.plan_tasks]
