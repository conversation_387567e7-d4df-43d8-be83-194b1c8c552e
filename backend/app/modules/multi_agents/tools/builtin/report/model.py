from pydantic import BaseModel


class ReportManager(BaseModel):
    """Manages the state of reports and their progress.

    This class handles the storage and retrieval of reports, as well as managing
    the active report state. It provides methods for serializing and deserializing
    the report state for persistence.
    """

    report: dict = {}

    def get_state(self) -> dict:
        return self.report

    def set_state(self, report: dict):
        self.report = report
