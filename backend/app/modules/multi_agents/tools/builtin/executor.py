import uuid

import aiohttp
from langchain_core.runnables import RunnableConfig

from app.core.config import settings


async def call_executor(script: str, config: RunnableConfig | None = None):
    api_url = settings.EXECUTOR_HOST + "/api/v1/script/run"

    # Default request body
    body = {
        "script": script,
        "workspace_id": str(uuid.uuid4()),  # Default workspace ID
        "cloud_credentials": None,
        "k8s_credentials": None,
    }

    if config:
        from app.modules.multi_agents.core import Configuration

        configuration = Configuration.from_runnable_config(config)
        workspace = configuration.workspace_config

        if workspace.workspace_id:
            body["workspace_id"] = str(workspace.workspace_id)
        else:
            raise ValueError("Workspace ID is required")

        if workspace.provider:
            provider = str(workspace.provider.value)
            if provider == "AWS":
                if any(
                    x is None
                    for x in [
                        workspace.aws_access_key_id,
                        workspace.aws_secret_access_key,
                        workspace.aws_default_region,
                    ]
                ):
                    body["cloud_credentials"] = None
                else:
                    body["cloud_credentials"] = {
                        "provider": provider,
                        "aws_access_key_id": str(workspace.aws_access_key_id),
                        "aws_secret_access_key": str(workspace.aws_secret_access_key),
                        "aws_default_region": str(workspace.aws_default_region),
                    }
            elif provider == "GCP":
                if workspace.gcp_key is None:
                    body["cloud_credentials"] = None
                else:
                    body["cloud_credentials"] = {
                        "provider": provider,
                        "service_account_key": str(workspace.gcp_key),
                    }
            elif provider == "AZURE":
                if any(
                    x is None
                    for x in [
                        workspace.azure_username,
                        workspace.azure_password,
                        workspace.azure_tenant,
                    ]
                ):
                    body["cloud_credentials"] = None
                else:
                    body["cloud_credentials"] = {
                        "provider": provider,
                        "username": str(workspace.azure_username),
                        "password": str(workspace.azure_password),
                        "tenant": str(workspace.azure_tenant),
                    }
            else:
                raise ValueError(f"Unsupported provider: {provider}")

    if workspace.kubeconfig:
        body["k8s_credentials"] = {"kubeconfig": str(workspace.kubeconfig)}

    headers = {"accept": "application/json", "Content-Type": "application/json"}

    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                api_url,
                headers=headers,
                json=body,
                timeout=aiohttp.ClientTimeout(total=30.0),
            ) as response:
                response.raise_for_status()
                result = await response.json()
                output = result.get("result", {})

                return {
                    "return_code": output.get("return_code", 1),
                    "stdout": output.get("stdout", ""),
                    "stderr": output.get("stderr", ""),
                }
        except TimeoutError:
            return {
                "return_code": 1,
                "stdout": "",
                "stderr": "API request timed out after 30 seconds",
            }
        except Exception as e:
            return {
                "return_code": 1,
                "stdout": "",
                "stderr": f"Unexpected error: {str(e)}",
            }
