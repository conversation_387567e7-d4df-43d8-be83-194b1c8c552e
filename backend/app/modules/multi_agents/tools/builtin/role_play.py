from pydantic import BaseModel, Field


class RolePlayingOutput(BaseModel):
    """Output for role playing game.

    This class represents the output format for the role playing game where agents select roles
    from available participants based on the conversation context and last customer message.
    If the last customer message is satisfied, returns "END" as the role.
    """

    thought: str = Field(
        ...,
        description="The reasoning behind selecting this role based on conversation context",
    )
    role: str = Field(
        ..., description="The selected role or 'END' if customer request is satisfied"
    )
