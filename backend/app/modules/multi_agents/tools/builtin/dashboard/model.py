from pydantic import BaseModel


class DashboardManager(BaseModel):
    """Manages the state of dashboards and their progress.

    This class handles the storage and retrieval of dashboards, as well as managing
    the active dashboard state. It provides methods for serializing and deserializing
    the dashboard state for persistence.
    """

    dashboard: dict = {}

    def get_state(self) -> dict:
        return self.dashboard

    def set_state(self, dashboard: dict):
        self.dashboard = dashboard
