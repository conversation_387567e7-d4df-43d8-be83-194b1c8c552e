from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from langgraph.graph.state import CompiledStateGraph
from langgraph.store.postgres import AsyncPostgresStore
from psycopg_pool import AsyncConnectionPool

from app.core.config import settings

from .base import BaseAgent


class AgentFactory:
    """Global Agent Factory for managing and accessing agents."""

    _instance = None
    _pre_compiled_agents: dict[str, CompiledStateGraph] = {}
    _agents: dict[str, BaseAgent] = {}
    _pool: AsyncConnectionPool | None = None
    _checkpointer: AsyncPostgresSaver | None = None
    _store: AsyncPostgresStore | None = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    async def initialize(cls) -> None:
        """Initialize the PostgreSQL connection pool and checkpointer."""
        if cls._pool is not None:
            return  # Already initialized

        connection_kwargs = {
            "autocommit": True,
            "prepare_threshold": 0,
            "host": settings.POSTGRES_SERVER,
            "port": settings.POSTGRES_PORT,
            "user": settings.POSTGRES_USER,
            "password": settings.POSTGRES_PASSWORD,
            "dbname": settings.POSTGRES_DB,
            "options": f"-c search_path={settings.LANGGRAPH_SCHEMA_NAME}",
        }

        cls._pool = AsyncConnectionPool(
            max_size=20,
            kwargs=connection_kwargs,
        )
        await cls._pool.open()

        cls._checkpointer = AsyncPostgresSaver(cls._pool)
        cls._store = AsyncPostgresStore(
            cls._pool,
            index={
                "dims": settings.EMBEDDING_MODEL_DIMS,
                "embed": settings.EMBEDDING_MODEL,
            },
        )

        # Create schema if not exists
        async with cls._pool.connection() as conn:
            await conn.execute(
                f"CREATE SCHEMA IF NOT EXISTS {settings.LANGGRAPH_SCHEMA_NAME};"
            )

        await cls._checkpointer.setup()

        # Auto-register all graphs
        await cls._register_all_graphs()

    @classmethod
    async def _register_all_graphs(cls) -> None:
        from app.modules.multi_agents.agents import CoordinatorAgent

        cls.register_agent("coordinator_agent", CoordinatorAgent)

    @classmethod
    async def cleanup(cls) -> None:
        """Cleanup resources when shutting down."""
        if cls._pool:
            await cls._pool.close()
            cls._pool = None
            cls._checkpointer = None
            cls._store = None
            cls._pre_compiled_agents.clear()

    @classmethod
    def register_agent(
        cls,
        name: str,
        builder: BaseAgent,
    ) -> None:
        """Register a new agent with the factory.

        Args:
            name: Name to register the agent under
            builder: Agent builder instance

        Raises:
            ValueError: If a graph with the same name exists
            RuntimeError: If provider not initialized
        """
        if name in cls._agents:
            raise ValueError(f"Graph {name} already registered")

        if cls._checkpointer is None:
            raise RuntimeError("Provider not initialized. Call initialize() first")

        cls._agents[name] = builder()
        cls._pre_compiled_agents[name] = cls._agents[name].compile(
            checkpointer=cls._checkpointer, store=cls._store
        )

    @classmethod
    def get_pre_compiled_graph(cls, name: str) -> CompiledStateGraph | None:
        """Get a registered graph by name."""
        return cls._pre_compiled_agents.get(name)
