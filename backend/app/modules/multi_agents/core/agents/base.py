"""Base class for all agents in the multi-agent system."""

from abc import ABC, abstractmethod

from langgraph.graph import StateGraph


class BaseAgent(ABC):
    """Abstract base class for all agents in the multi-agent system."""

    def __init__(self) -> None:
        """Initialize the base agent."""
        self.build_graph()

    @abstractmethod
    def build_graph(self) -> None:
        """Build the workflow graph for the agent.

        This method should be implemented by concrete agent classes to define
        their specific workflow structure.

        Args:
            agents_config: Configuration for all agents
        """
        pass

    @abstractmethod
    def get_graph(self) -> StateGraph:
        """Get the agent's workflow graph."""
        pass

    @abstractmethod
    def compile(self, **kwargs):
        """Compile the agent's workflow graph."""
        pass
