"""Networking agent."""

import random

from langchain_core.messages import HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph import END, StateGraph
from langgraph.store.base import BaseStore
from langgraph.types import Command

from app.core.config import settings
from app.modules.multi_agents.agents.conversation_agent import ConversationalAgent
from app.modules.multi_agents.common import (
    BaseAgentToolNode,
    BaseConversationalAgentNode,
)
from app.modules.multi_agents.core import (
    BaseAgent,
    Configuration,
    GlobalState,
    InputState,
)
from app.modules.multi_agents.core.utils import load_chat_model
from app.modules.multi_agents.prompts.prompt_manager import prompt_manager
from app.modules.multi_agents.tools.builtin import RolePlayingOutput


class CoordinatorAgent(BaseAgent, BaseAgentToolNode, BaseConversationalAgentNode):
    """Coordinator agent."""

    async def set_up_state(self, state: GlobalState, config: RunnableConfig):
        """Set up initial state for the networking agent."""
        configuration = Configuration.from_runnable_config(config)

        # Initialize state if needed
        if not state.init_state:
            state = GlobalState.from_config(configuration.agents_config, state.messages)
            state.init_state = True
            state.target_group_chat_member = None
            state.name = None

        # Initialize group chat
        if state.messages:
            state.group_chat = state.group_chat.add_message(state.messages, "Customer")
        return Command(goto="coordinator_agent", update=state)

    async def append_group_chat_prompt(self, state, config):
        """Append a HumanMessage with the group chat prompt to the selected agent's messages."""
        # Get the raw user prompt from config or state
        configuration = Configuration.from_runnable_config(config)
        attachment_content = configuration.attachment_content

        # Get available memories
        payload = [
            {
                "type": "text",
                "text": await prompt_manager.get_group_chat_prompt(state, config),
            }
        ]
        if attachment_content:
            payload.extend(attachment_content)

        state.instance_states[state.name].messages.append(HumanMessage(payload))

    async def handle_role_playing(
        self,
        state: GlobalState,
        config: RunnableConfig,
    ) -> str:
        """Handle role playing by selecting the next agent to respond."""
        role_playing_prompt = (
            await prompt_manager.get_coordination_agent_role_playing_prompt(
                state, config
            )
        )
        messages = [{"role": "user", "content": role_playing_prompt}]

        model = load_chat_model(settings.NETWORKING_MODEL).with_structured_output(
            RolePlayingOutput, strict=True
        )
        response: RolePlayingOutput = await model.ainvoke(messages, config)
        role = response.role.strip()
        if role in state.instance_states:
            state.name = role
            return role
        else:
            state.name = random.choice(list(state.instance_states.keys()))
            return state.name

    async def coordinator_agent(
        self, state: GlobalState, config: RunnableConfig, store: BaseStore
    ):
        """Coordinating agent (refactored for clarity and maintainability)."""
        configuration = Configuration.from_runnable_config(config)
        active_agents = configuration.agents_config.active_agents

        # Handle direct target group chat member
        if state.target_group_chat_member:
            if state.target_group_chat_member == "Customer":
                state.target_group_chat_member = None
                state.name = None
                return Command(goto=END, update=state)

            # Target is another agent
            state.name = state.target_group_chat_member
            state.target_group_chat_member = None
            await self.append_group_chat_prompt(state, config)
            return Command(goto="conversational_agent", update=state)

        # Decide next agent to handle the message
        if len(active_agents) > 1:
            state.name = await self.handle_role_playing(state, config)
        else:
            state.name = active_agents[0]

        await self.append_group_chat_prompt(state, config)
        return Command(goto="conversational_agent", update=state)

    def build_graph(self) -> None:
        """Build the graph."""
        self.graph = StateGraph(
            GlobalState, input=InputState, config_schema=Configuration
        )

        # Add nodes
        self.graph.add_node("set_up_state", self.set_up_state)
        self.graph.add_node("coordinator_agent", self.coordinator_agent)
        self.graph.add_node("conversational_agent", ConversationalAgent().compile())

        # Add edges
        self.graph.add_edge("conversational_agent", "coordinator_agent")
        self.graph.add_edge("__start__", "set_up_state")

    def get_graph(self) -> StateGraph:
        """Get the graph."""
        return self.graph

    def compile(self, **kwargs):
        """Compile the graph."""
        return self.graph.compile(**kwargs)
