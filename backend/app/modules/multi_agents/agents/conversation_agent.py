from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph
from langgraph.types import Command, StreamWriter

from app.modules.multi_agents.common import (
    BaseAgentToolNode,
    BaseConversationalAgentNode,
)
from app.modules.multi_agents.core import BaseAgent, Configuration, GlobalState
from app.modules.multi_agents.prompts.prompt_manager import prompt_manager


class ConversationalAgent(BaseAgent, BaseAgentToolNode, BaseConversationalAgentNode):
    """Graph builder for the conversational agent system."""

    async def reasoning_agent(
        self,
        state: GlobalState,
        config: Run<PERSON>bleConfig,
        writer: StreamWriter,
    ) -> dict[str, list[AIMessage]]:
        """Call the LLM powering our agent."""

        writer({"type": "on_agent_start", "content": state.name})

        # Get the instance state and configuration
        instance_state = state.instance_states[state.name]
        configuration = Configuration.from_runnable_config(config)
        agent_config = configuration.agents_config.agents[state.name]
        tool_manager = configuration.tool_manager

        # Prepare tools
        tools = await tool_manager.get_tools(agent_config=agent_config, state=state)
        model = configuration.model.bind_tools(tools)

        # Prepare base messages with summary if available
        base_messages = []
        if instance_state.summary:
            base_messages.append(
                {
                    "role": "user",
                    "content": f"Previous conversation summary:\n{instance_state.summary}",
                }
            )

        # Prepare messages
        instance_state.messages = await self.prepare_messages(instance_state)

        system_prompt = await prompt_manager.get_conversation_agent_system_prompt(
            agent_config=agent_config, state=state, config=config
        )

        messages = [
            {
                "role": "system",
                "content": [
                    {
                        "type": "text",
                        "text": system_prompt,
                        "cache_control": {"type": "ephemeral"},
                    }
                ],
            },
            *base_messages,
            *instance_state.messages,
        ]

        config["run_name"] = "Conversational Agent"

        response = await model.ainvoke(messages, config)

        # Check for tool calls
        has_tool_calls = (
            hasattr(response, "tool_calls")
            and isinstance(response.tool_calls, list)
            and len(response.tool_calls) > 0
        )

        if has_tool_calls:
            # TODO: Try to get the first tool call if multiple exist
            response.tool_calls = [response.tool_calls[0]]

        instance_state.messages.append(response)
        if not has_tool_calls:
            # Should end with a group chat tool call
            state.instance_states[state.name].messages.append(
                HumanMessage(content="PLEASE END WITH A GROUP CHAT TOOL CALL")
            )
            return Command(goto="reasoning_agent", update=state)

        # Check if recommendation tool is used
        if any(
            tool_call["name"] == "TaskBase" or tool_call["name"] == "create_chart"
            for tool_call in response.tool_calls
        ):
            return Command(goto="structured_tools", update=state)

        # All other tools including recommendations go through normal tool flow
        return Command(goto="check_permissions", update=state)

    def build_graph(self) -> None:
        """Build the agent system graph structure."""
        self.graph = StateGraph(GlobalState)
        self.graph.add_node("reasoning_agent", self.reasoning_agent)
        self.graph.add_node("tools", self.tool_node)
        self.graph.add_node("check_permissions", self.check_permissions)
        self.graph.add_node("summarize", self.summarize_conversation)
        self.graph.add_node("structured_tools", self.structured_tools)

        self.graph.add_edge("__start__", "summarize")
        self.graph.add_edge("summarize", "reasoning_agent")
        self.graph.add_edge("structured_tools", "reasoning_agent")

    def get_graph(self) -> StateGraph:
        """Return the final graph."""
        return self.graph

    def compile(self, **kwargs):
        return self.graph.compile(**kwargs)
