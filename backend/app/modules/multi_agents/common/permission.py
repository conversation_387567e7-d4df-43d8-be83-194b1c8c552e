from langchain_core.messages import AnyMessage, HumanMessage
from langgraph.types import interrupt

from app.modules.multi_agents.prompts.prompts import TOOL_PERMISSION_REQUEST


def count_conversation_rounds(messages: list[AnyMessage]) -> int:
    """Count the number of conversation rounds by counting Human messages except the latest."""
    if not messages:
        return 0
    previous_messages = messages[:-1]
    return sum(1 for msg in previous_messages if isinstance(msg, HumanMessage))


async def get_user_permission(
    tool_name: str, tool_args: dict | None = None
) -> tuple[bool, str | None]:
    """Get permission from user for specific operations."""
    request = TOOL_PERMISSION_REQUEST.format(
        tool_name=tool_name,
        tool_args=tool_args if tool_args else "No additional details provided.",
    )
    user_response = interrupt(request)
    return bool(user_response.get("approve", False)), user_response.get(
        "approve_message", None
    )
