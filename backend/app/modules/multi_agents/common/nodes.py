"""Common utilities and base classes for multi-agent system."""

import copy
import json
from typing import Any, Sequence

from langchain_core.messages import AIMessage, AnyMessage, HumanMessage, ToolMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph import <PERSON><PERSON>
from langgraph.types import Command, StreamWriter

from app.core.langfuse import langfuse_handler
from app.logger import logger
from app.services.attachment_service import AttachmentService

from ..core import Configuration, GlobalState, StateBase
from ..prompts.prompts import (
    INVALID_TOOL_NAME_ERROR_TEMPLATE,
    TOOL_CALL_ERROR_TEMPLATE,
    TOOL_PERMISSION_DENIED,
)
from .permission import count_conversation_rounds, get_user_permission


class BaseAgentToolNode:
    """Base class containing shared node functionality for agents."""

    async def prepare_messages(self, instance_state: StateBase) -> Sequence[AnyMessage]:
        """Prepare messages for the agent."""

        # Get latest assistant message
        latest_assistant_message, latest_assistant_message_index = None, None
        for index, message in enumerate(reversed(instance_state.messages)):
            if isinstance(message, AIMessage):
                latest_assistant_message = message
                latest_assistant_message_index = (
                    len(instance_state.messages) - index - 1
                )
                break

        if latest_assistant_message:
            # Check if the latest assistant message has tool calls and if not have tool message append tool messages to base message
            has_tool_calls = (
                hasattr(latest_assistant_message, "tool_calls")
                and isinstance(latest_assistant_message.tool_calls, list)
                and len(latest_assistant_message.tool_calls) > 0
            )

            if has_tool_calls and latest_assistant_message_index is not None:
                next_tool_message = instance_state.messages[
                    latest_assistant_message_index + 1
                ]
                if not isinstance(next_tool_message, ToolMessage):
                    # Insert a tool message with empty content and name
                    insert_tool_message = ToolMessage(
                        content="Unexpected error occurred",
                        tool_call_id=latest_assistant_message.tool_calls[0]["id"],
                        name=latest_assistant_message.tool_calls[0]["name"],
                    )
                    instance_state.messages.insert(
                        latest_assistant_message_index + 1, insert_tool_message
                    )
        return instance_state.messages

    async def tool_node(
        self, state: GlobalState, config: RunnableConfig, writer: StreamWriter
    ):
        """Execute tool calls and handle errors."""

        # Get the configuration
        configuration = Configuration.from_runnable_config(config)
        tool_manager = configuration.tool_manager
        agent_config = configuration.agents_config.agents[state.name]

        # Get the tools
        tools = await tool_manager.get_tools(agent_config=agent_config, state=state)

        tools_by_name = {tool.name: tool for tool in tools}

        # Process each tool call
        for tool_call in state.instance_states[state.name].messages[-1].tool_calls:
            try:
                # Check if tool exists
                if tool_call["name"] not in tools_by_name:
                    error_msg = INVALID_TOOL_NAME_ERROR_TEMPLATE.format(
                        requested_tool=tool_call["name"]
                    )
                    state.instance_states[state.name].messages.append(
                        ToolMessage(
                            content=error_msg,
                            tool_call_id=tool_call["id"],
                            name=tool_call["name"],
                            status="error",
                        )
                    )
                    continue

                tool = tools_by_name[tool_call["name"]]
                tool_args = copy.deepcopy(tool_call["args"])

                try:
                    response = await tool.ainvoke(tool_args)
                    tool_content = response
                    if tool_call["name"] == "group_chat":
                        state.target_group_chat_member = tool_args["target_member"]
                        state.group_chat.messages.append(
                            (state.name, tool_args["message"])
                        )
                        state.instance_states[state.name].messages.append(
                            ToolMessage(
                                content=response,
                                tool_call_id=tool_call["id"],
                                name=tool_call["name"],
                            )
                        )
                        return Command(goto=END, update=state)
                    elif tool_call["name"] == "planning":
                        writer(
                            {
                                "type": "planning",
                                "content": {
                                    "agent_name": state.name,
                                    "planning": state.plan_manager[
                                        state.name
                                    ].get_plan_dict(),
                                },
                            }
                        )
                    elif tool_call["name"] == "report":
                        response = json.loads(response)
                        tool_content = {
                            "status": response["status"],
                            "message": response["message"],
                        }
                        report = state.report_manager[state.name].get_state()
                        writer(
                            {
                                "type": "on_report_generation_response",
                                "content": report,
                            }
                        )
                    elif tool_call["name"] == "dashboard":
                        response = json.loads(response)
                        tool_content = {
                            "status": response["status"],
                            "message": response["message"],
                        }
                        dashboard = state.dashboard_manager[state.name].get_state()
                        writer(
                            {
                                "type": "on_dashboard_generation_response",
                                "content": dashboard,
                            }
                        )
                    elif tool_call["name"] == "recommendation":
                        tool_content = response
                        command = tool_args["command"]
                        if command == "create":
                            tool_call_result = tool_call["args"]["new_recommendations"][
                                "recommendations"
                            ]
                            resource_id = config["metadata"].get("resource_id")
                            writer(
                                {
                                    "type": "on_recommendation_generation_response",
                                    "content": tool_call_result,
                                    "metadata": {
                                        "resource_id": str(resource_id)
                                        if resource_id is not None
                                        else None,
                                        "type": "recommendation",
                                    },
                                }
                            )

                    state.instance_states[state.name].messages.append(
                        ToolMessage(
                            content=tool_content,
                            tool_call_id=tool_call["id"],
                            name=tool_call["name"],
                        )
                    )
                except Exception as e:
                    error_msg = TOOL_CALL_ERROR_TEMPLATE.format(error=str(e))
                    state.instance_states[state.name].messages.append(
                        ToolMessage(
                            content=error_msg,
                            tool_call_id=tool_call["id"],
                            name=tool_call["name"],
                            status="error",
                        )
                    )
            except Exception as e:
                # Catch any other unexpected errors
                state.instance_states[state.name].messages.append(
                    ToolMessage(
                        content=f"Unexpected error: {str(e)}",
                        tool_call_id=tool_call["id"],
                        name=tool_call.get("name", "unknown"),
                        status="error",
                    )
                )
        return Command(goto="reasoning_agent", update=state)


class BaseConversationalAgentNode:
    """Base class containing shared node functionality for agents."""

    def get_reasoning_agent_name(self) -> str:
        """Get the name of the reasoning agent node.

        This method can be overridden by subclasses to provide a different reasoning agent name.
        """
        return "reasoning_agent"

    async def check_permissions(self, state: GlobalState, config: RunnableConfig):
        """Check if user has required permissions for operations."""

        # Get the instance state
        instance_state = state.instance_states[state.name]

        # Get the configuration
        configuration = Configuration.from_runnable_config(config)
        agent_configuration = configuration.agents_config.agents[state.name]

        # Get the last message
        last_message = instance_state.messages[-1]

        # Get the permission tool
        permission_tool = next(
            (
                tool_call
                for tool_call in last_message.tool_calls
                if tool_call["name"] in agent_configuration.tool_config.tool_permissions
            ),
            None,
        )
        if permission_tool:
            permission_granted, approve_message = await get_user_permission(
                permission_tool["name"], permission_tool.get("args", {})
            )
            if permission_granted:
                return Command(goto="tools")
            else:
                instance_state.messages.append(
                    ToolMessage(
                        content=TOOL_PERMISSION_DENIED.format(
                            tool_name=permission_tool["name"],
                            approve_message=approve_message,
                        ),
                        tool_call_id=permission_tool["id"],
                    )
                )
                return Command(goto=self.get_reasoning_agent_name(), update=state)
        else:
            return Command(goto="tools", update=state)

    async def summarize_conversation(self, state: GlobalState, config: RunnableConfig):
        """Summarize the conversation and maintain a running summary."""

        # Get the instance state
        instance_state = state.instance_states[state.name]

        # Get the configuration
        configuration = Configuration.from_runnable_config(config)
        model = configuration.model

        conversation_rounds = count_conversation_rounds(instance_state.messages)

        if conversation_rounds >= configuration.max_conversation_rounds:
            # The latest message is the user message
            # The second latest message is the assistant message
            # If the second latest message is a tool message, we need to keep the assistant message
            # Must check instance_state.messages has at least 2 messages
            if len(instance_state.messages) >= 2:
                if isinstance(instance_state.messages[-2], ToolMessage):
                    messages_to_keep = instance_state.messages[-3:]
                else:
                    messages_to_keep = instance_state.messages[-2:]
            else:
                return Command(goto=self.get_reasoning_agent_name(), update=state)

            messages_to_summarize = instance_state.messages
            message_history = "\n".join(
                f"{'User' if isinstance(m, HumanMessage) else 'Assistant'}: {AttachmentService.format_message_content_for_summary(m.content)}"
                for m in messages_to_summarize
            )

            if instance_state.summary:
                summary_prompt = f"""Extend the existing conversation summary with these messages:

    <existing_summary>
    {instance_state.summary}
    </existing_summary>

    <conversation_history>
    {message_history}
    </conversation_history>

    Create a concise summary that captures all key points."""
            else:
                summary_prompt = f"""Create a concise summary of this conversation:

    <conversation_history>
    {message_history}
    </conversation_history>

    Focus on capturing the key points and context."""

            config["run_name"] = "Summarize Conversation"
            config["callbacks"] = [langfuse_handler]

            response = await model.ainvoke(
                [
                    {
                        "role": "system",
                        "content": "You are a precise conversation summarizer. Create clear, concise summaries that maintain context.",
                    },
                    {"role": "user", "content": summary_prompt},
                ],
                config,
            )

            instance_state.summary = response.content
            instance_state.messages = messages_to_keep

        return Command(goto=self.get_reasoning_agent_name(), update=state)

    async def structured_tools(
        self,
        state: GlobalState,
        config: RunnableConfig,
        writer: StreamWriter,
    ) -> dict[str, Any]:
        # Get the instance state
        instance_state = state.instance_states[state.name]

        last_message = instance_state.messages[-1]
        tool_call = last_message.tool_calls[0]

        # Validate that we have tool calls and they're properly structured
        if not hasattr(last_message, "tool_calls") or not last_message.tool_calls:
            logger.error("No tool calls found in message")
            return state

        tool_message = ToolMessage(
            content="Chart component created successfully",
            tool_call_id=tool_call.get("id", "unknown"),
            name=tool_call["name"],
        )

        if tool_call["name"] == "create_chart":
            writer(
                {"type": "on_chart_generation_response", "content": tool_call["args"]}
            )

        # Add the tool message to state
        instance_state.messages.append(tool_message)
        return state
