from pydantic import BaseModel, Field

from .tools import ToolConfig


class AgentConfig(BaseModel):
    """Configuration for a single agent."""

    role: str = Field(..., description="Role of the agent")
    goal: str = Field(..., description="Goal of the agent")
    instructions: str = Field(..., description="Instructions for the agent")
    resource_context: str = Field(
        default="No context provided", description="Context for the agent"
    )
    agent_id: str = Field(default="", description="ID of the agent")
    agent_context: str = Field(
        default="No agent context provided", description="Agent context for the agent"
    )
    tool_config: ToolConfig = Field(
        ..., description="Tools configuration for the agent"
    )
    region_constraints: str | None = Field(
        default=None, description="Region constraints for the agent"
    )
    mcp_connections: list[str] | None = Field(
        default=None, description="MCP connections for the agent"
    )
    connections: list[str] | None = Field(
        default=None, description="Connections for the agent"
    )
