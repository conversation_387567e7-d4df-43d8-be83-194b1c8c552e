"""Multi-agent system configuration."""

import uuid

from langchain_core.tools import BaseTool
from pydantic import BaseModel, <PERSON>
from sqlmodel import SQLModel

from app.models import CloudProvider, ConnectionBase

from .agent import AgentConfig


class WorkspaceConfigSchema(SQLModel):
    workspace_id: uuid.UUID
    provider: CloudProvider
    aws_access_key_id: str | None = None
    aws_secret_access_key: str | None = None
    aws_default_region: str | None = None
    gcp_key: str | None = None
    azure_username: str | None = None
    azure_password: str | None = None
    azure_tenant: str | None = None
    kubeconfig: str | None = None


class MultiAgentConfig(BaseModel):
    """Multi-agent configuration."""

    agents: dict[str, AgentConfig] = Field(
        default_factory=dict, description="Map of agent configurations"
    )
    all_mcp_connections: list[ConnectionBase] | None = Field(
        default=None, description="MCP configuration"
    )
    all_connections: dict[str, list[BaseTool]] | None = Field(
        default=None, description="All connections"
    )
    active_agents: list[str] = Field(default=[], description="List of active agents")
