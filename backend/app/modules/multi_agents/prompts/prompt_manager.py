from langchain_core.messages import HumanMessage
from langchain_core.runnables import RunnableConfig

from app.modules.multi_agents.config import AgentConfig
from app.modules.multi_agents.core import Configuration, GlobalState
from app.modules.multi_agents.tools.builtin import PlanningTool
from app.services.memory.memory_service import MemoryService

from .prompts import (
    AGENT_ROLE_DESCRIPTION,
    CUSTOMER_ROLE_DESCRIPTION,
    DASHBOARD_PROMPT,
    GROUP_CHAT_PROMPT,
    NETWORKING_PROMPT,
    REGION_PROMPT,
    REPORT_PROMPT,
    ROLE_PLAY_PROMPT,
    ROLE_PLAYING_PROMPT,
    THINKING_PROMPT,
)


class PromptManager:
    async def prepare_agent_list(
        self, state: GlobalState, config: RunnableConfig
    ) -> tuple[list[str], list[str]]:
        """Prepare messages for the conversational agent."""
        # Get the configuration
        configuration = Configuration.from_runnable_config(config)
        available_agents_list: list[str] = []
        unavailable_agents_list: list[str] = []

        for agent_name, agent_config in configuration.agents_config.agents.items():
            if agent_name in configuration.agents_config.active_agents:
                available_agents_list.append(
                    AGENT_ROLE_DESCRIPTION.format(
                        role_name=agent_name,
                        role_description=agent_config.role,
                        role_goal=agent_config.goal,
                    )
                )
            else:
                unavailable_agents_list.append(
                    AGENT_ROLE_DESCRIPTION.format(
                        role_name=agent_name,
                        role_description=agent_config.role,
                        role_goal=agent_config.goal,
                    )
                )

        return available_agents_list, unavailable_agents_list

    async def get_networking_system_prompt(
        self, state: GlobalState, config: RunnableConfig
    ) -> str:
        """Get the system prompt for the networking agent."""
        available_agents_list, _ = await self.prepare_agent_list(state, config)
        return NETWORKING_PROMPT.format(
            available_agents_list="\n".join(available_agents_list)
        )

    async def get_plan_system_prompt(
        self, state: GlobalState, config: RunnableConfig
    ) -> str:
        """Get the system prompt for the plan agent."""
        configuration = Configuration.from_runnable_config(config)
        tool_manager = configuration.tool_manager
        tools = await tool_manager.get_builtin_tools(["planning"], state)
        plan_tool: PlanningTool | None = tools[0] if tools else None

        plan_prompt = plan_tool.get_plan_prompt() if plan_tool else ""
        return plan_prompt

    async def get_tool_prompt(self, state: GlobalState, config: RunnableConfig) -> str:
        """Get the tool prompt for the conversation agent."""
        instance_state = state.instance_states[state.name]
        configuration = Configuration.from_runnable_config(config)
        agent_config = configuration.agents_config.agents[state.name]

        # Get the latest user prompt
        latest_user_prompt = ""
        for message in reversed(instance_state.messages):
            if isinstance(message, HumanMessage):
                latest_user_prompt = str(message.content[-1])
                break

        # Get the tool prompt
        prompt_mappings = {
            "#report": ("report", "REPORT_PROMPT"),
            "#dashboard": ("dashboard", "DASHBOARD_PROMPT"),
        }

        tool_prompt = ""
        for keyword, (tool_name, prompt_name) in prompt_mappings.items():
            if (
                tool_name in agent_config.tool_config.builtin
                and keyword in latest_user_prompt
            ):
                if prompt_name == "REPORT_PROMPT":
                    tool_prompt += REPORT_PROMPT
                elif prompt_name == "DASHBOARD_PROMPT":
                    tool_prompt += DASHBOARD_PROMPT

        return tool_prompt

    async def get_conversation_agent_system_prompt(
        self,
        agent_config: AgentConfig,
        state: GlobalState,
        config: RunnableConfig,
    ) -> str:
        """Get the system prompt for the conversation agent."""
        networking_system_prompt = await self.get_networking_system_prompt(
            state, config
        )
        plan_system_prompt = await self.get_plan_system_prompt(state, config)
        tool_prompt = await self.get_tool_prompt(state, config)

        system_prompt = (
            THINKING_PROMPT
            + "\n\n"
            + ROLE_PLAY_PROMPT.format(
                name=state.name,
                role=agent_config.role,
                goal=agent_config.goal,
                instructions=agent_config.instructions,
                resource_context=agent_config.resource_context,
                agent_context=agent_config.agent_context,
            )
        )
        if agent_config.region_constraints:
            system_prompt += "\n\n" + REGION_PROMPT.format(
                region_constraints=agent_config.region_constraints
            )
        system_prompt += (
            "\n\n"
            + networking_system_prompt
            + "\n\n"
            + plan_system_prompt
            + "\n\n"
            + tool_prompt
        )
        return system_prompt

    async def get_coordination_agent_role_playing_prompt(
        self, state: GlobalState, config: RunnableConfig
    ) -> str:
        """Get the role playing prompt for the coordination agent."""
        configuration = Configuration.from_runnable_config(config)
        available_agents_list, _ = await self.prepare_agent_list(state, config)

        role_playing_prompt = ROLE_PLAYING_PROMPT.format(
            name=state.name,
            role_descriptions="\n".join(available_agents_list),
            customer_role_description=CUSTOMER_ROLE_DESCRIPTION,
            group_chat=state.group_chat.get_messages(),
            last_group_chat_message=f"{state.group_chat.messages[-1][0]}: {state.group_chat.messages[-1][1]}",
            last_message=state.group_chat.last_message,
            participants=",".join(configuration.agents_config.active_agents),
        )

        return role_playing_prompt

    async def get_group_chat_prompt(
        self, state: GlobalState, config: RunnableConfig
    ) -> str:
        """Get the group chat prompt for the networking agent."""
        configuration = Configuration.from_runnable_config(config)
        user_prompt = configuration.user_prompt

        memory_service = MemoryService()
        available_memories_prompt = await memory_service.search_memory(
            user_prompt, state.name, configuration.workspace_id
        )

        group_chat_prompt = GROUP_CHAT_PROMPT.format(
            group_chat=state.group_chat.get_messages(),
            last_message=state.group_chat.last_message,
            name=state.name,
            kb_prompt=configuration.kb_prompt,
            available_memories_prompt=available_memories_prompt,
        )

        return group_chat_prompt


prompt_manager = PromptManager()
