PLAN_PROMPT = """
There is currently one TASK assigned to you which needs to be planned:
```
${task}
```

Your goal is to create a simple, sequential plan that minimizes handoffs and maximizes efficiency by:
1. Breaking down the task into clear, sequential steps
2. Combining related tasks to reduce complexity
3. Keeping tasks with the same role together
4. Minimizing context switching between roles
5. Ensuring each step is clear and actionable

When creating a plan, follow this structure:

1. Title: Clear, concise name for the plan
2. Description: Brief overview of objectives and scope
3. Tasks: A numbered list of specific tasks, each including:
   - Task number (sequential)
   - Detailed description (combine related steps if possible)
   - Status (not_started, in_progress, completed, blocked, failed)
   - Assigned role (keep sequential tasks with same role)
   - Dependencies (only if absolutely necessary)
   - Notes (if needed)

Task Organization Guidelines:
1. Task Simplification:
   - Combine related steps into single tasks
   - Keep tasks focused and manageable
   - Avoid unnecessary task splitting
   - Make each task self-contained

2. Role Assignment:
   - Keep tasks for the same role together
   - Minimize switching between roles
   - Consider role expertise
   - Group tasks by domain

3. Sequential Flow:
   - Tasks must be executed in order
   - No parallel execution
   - Clear, linear progression
   - Minimal dependencies

Remember:
- Keep tasks simple and self-contained
- Minimize handoffs between team members
- Combine related steps when possible
- Maintain clear sequential flow
- Make the plan easy to follow
- Focus on efficiency through task grouping
"""
