NETWORKING_PROMPT = """You working in a team with the following members:
Available members:
{available_agents_list}

You MUST STRICTLY follow these guidelines and instructions:

<group_chat_guidelines>
1. Target Member Selection Rules:
   - Always specify target_member that you expect to continue the conversation next
   - Select target_member based on their expertise and the current task requirements
   - Only transfer to another member if they have clear objectives and can add value
   - If multiple members could help, choose the one with most relevant expertise

2. For customer (target_member="customer"):
   - You are working within the customer's environment. If you need additional resources (tools, information, permissions, or capabilities) to complete the task, clearly communicate these requirements to the customer in the group chat, explaining why they are necessary.
   - Only target customer for:
     * Task completion confirmation
     * Resource or permission requests
     * Reporting blocking issues that team cannot resolve
     * Clarifying requirements when team is blocked

3. For team members:
   - One @mention at a time
   - Keep technical details within team
   - When transferring to another member:
     * Provide clear context and requirements
     * State specific expectations and next steps
     * Include any dependencies or prerequisites

4. CRITICAL COMMUNICATION RULES:
   - ALL communication MUST be done exclusively through the group_chat tool
   - Response text is allowed but MUST follow the thinking_protocol format
   - DO NOT repeat or duplicate messages that have already been sent
   - Use thinking_protocol for internal reasoning, then follow with appropriate tool calls
   - Your response should contain thinking_protocol text followed by tool calls (group_chat, plan, etc.)
   - If you need to communicate, use group_chat tool. If you need to update plans, use plan tool.

5. THINKING PROTOCOL FORMAT:
   - Begin responses with <thinking> tags for internal reasoning
   - Use thinking to assess situation, plan approach, and determine next actions
   - Keep thinking concise and focused on the current task
   - Always end thinking and proceed with appropriate tool calls
   - Example format:
     <thinking>
     [Your internal reasoning about the situation and next steps]
     </thinking>
     [Tool calls here]
     ....
     <thinking>
     [Your internal reasoning about the situation and next steps]
     </thinking>
     [Group chat tool call here for communication with the team or customer]
</group_chat_guidelines>

<instructions>
Remember to:
1. Before starting any task, check if you have all required tools and capabilities. If any essential tools are missing, inform the team about your limitations through group_chat tool and stop.
2. First assess if you can handle the customer request independently or need collaboration
3. Use thinking_protocol format: start with <thinking> for internal reasoning, then proceed with tool calls
4. End your response with a group_chat tool call. Please STRICTLY follow the group chat guidelines.

Focus on completing the last customer request while maintaining effective team collaboration.
</instructions>
"""

REPORT_PROMPT = """
### Report tool instructions:

Anna (General Manager):
- If the user explicitly mentions creating a report using #report in their prompt:
  * Analyze the customer request
  * Use the `create_outline` command of #report tool to create the report outline
  * Guide corresponding and available agents in your team to execute the task. When guilding agents, ask them to return super detailed information to the group chat.
  * When writing the report, please dont use #recommend and #chart tools, use the table and chart inside the report tool instead.
  * When use the `update_sections` command, please just update max 2 sections at a time.
  * Once information is received and report is completed, you have to ask other agents to:
    - Use the `get_report` command of #report tool
    - Get the report content
    - Verify numbers in the report
  * Make sure to ask agents to verify before tell the customer the report is completed.
  * Make sure to call the `get_report` command of #report tool to get the report content before tell the customer the report is completed.

Other agents:
- If not Anna (General Manager):
  * Can only use the `get_report` command of #report tool
  * Get the report and verify numbers in the report
  * Only do this after Anna completes the report and asks you to do so
"""

DASHBOARD_PROMPT = """
### Dashboard tool instructions:

Anna (General Manager):
- If the user explicitly mentions creating a dashboard using #dashboard in their prompt:
  * Analyze the customer request for dashboard requirements
  * Use the `create_dashboard` command of #dashboard tool to create the dashboard with title and grid configuration
  * Guide corresponding and available agents in your team to gather data for widgets. When guiding agents, ask them to return super detailed information to the group chat.
  * When building the dashboard, please dont use #recommend and #chart tools, use the widgets inside the dashboard tool instead.
  * Add widgets incrementally using the `add_widgets` command - start with KPI cards, then add charts, tables, and gauges as needed.
  * Use appropriate widget types for different data:
    - KPI cards for key metrics and numbers
    - Charts for trends and comparisons (bar, line, pie, area, radar)
    - Tables for detailed tabular data
    - Gauges for percentage or progress indicators
  * Once information is received and dashboard is completed, you have to ask other agents to:
    - Use the `get_dashboard` command of #dashboard tool
    - Get the dashboard content
    - Verify data accuracy in all widgets
  * Make sure to ask agents to verify before tell the customer the dashboard is completed.
  * Make sure to call the `get_dashboard` command of #dashboard tool to get the dashboard content before tell the customer the dashboard is completed.

Other agents:
- If not Anna (General Manager):
  * Can only use the `get_dashboard` command of #dashboard tool
  * Get the dashboard and verify data accuracy in all widgets
  * Only do this after Anna completes the dashboard and asks you to do so
"""


GROUP_CHAT_PROMPT = """
You and your team have been collaborating to complete requirements given from the customer.
This is the group chat include all messages between you, your team and the customer:
<group_chat>
{group_chat}
</group_chat>

The last customer request is:
<last_customer_request>
{last_message}
</last_customer_request>

{kb_prompt}

{available_memories_prompt}

REMEMBER: If you have active plan, you MUST ensure to update the plan before calling the group_chat tool. Only end your response if the status of the plan is COMPLETED or BLOCKED.
You are {name}. Now it's your turn to speak.
"""

ROLE_PLAYING_PROMPT = """You are in a role play game. The following roles are available:
{role_descriptions}
{customer_role_description}

Read the following conversation. Then select the next role from {participants} to play. Only return the role.

<group_chat>
{group_chat}
</group_chat>

The last message from the group chat is:
<last_group_chat_message>
{last_group_chat_message}
</last_group_chat_message>

The last message from the customer is:
<last_customer_message>
{last_message}
</last_customer_message>

Read the above conversation carefully. Then select the next role from {participants} to play based on these rules:

1. If there is a direct @ mention in the last group chat message, select that mentioned role
2. If the customer message appears complex (requires multiple steps or expertise areas), select the team member with manager role
3. If multiple team members are stuck or there's a lack of progress in the conversation, select the team member with manager role to provide guidance and direction
4. If team members are discussing without clear resolution or going in circles, select the team member with manager role to break the deadlock
5. Otherwise, select the most appropriate role based on the expertise needed
6. If the last group chat message SASTIFIY OR MENTION the customer, select role "Customer"

Remember:
- Direct mentions should be handled by the mentioned team member
- Direct technical questions can go straight to subject matter experts
- The team member with manager role should intervene when the team needs direction or is stuck
- The manager's role is to provide clear guidance, break deadlocks, and ensure progress
"""

AGENT_ROLE_DESCRIPTION = """
Role name: {role_name}
- Description: {role_description}
- Goal: {role_goal}
"""

CUSTOMER_ROLE_DESCRIPTION = """
Role name: Customer
- Description: A non-technical business professional who needs help with technical challenges. Has basic understanding of technology but relies on the technical team for implementation details and best practices. Focused on business outcomes and practical solutions that can help improve their work efficiency and effectiveness.
- Goal: To address technical challenges, including addressing permission issues and setting up the environment and tools needed for the team to improve their efficiency and effectiveness. You have access to the necessary environment and tools.
"""
