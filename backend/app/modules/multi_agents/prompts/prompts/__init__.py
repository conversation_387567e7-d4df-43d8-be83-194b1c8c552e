from .errors import (
    INVALID_TOOL_NAME_ERROR_TEMPLATE,
    TOOL_CALL_ERROR_TEMPLATE,
)
from .networking import (
    AGENT_ROLE_DESCRIPTION,
    CUSTOMER_ROLE_DESCRIPTION,
    DASHBOARD_PROMPT,
    G<PERSON>UP_CHAT_PROMPT,
    NETWORKING_PROMPT,
    REPORT_PROMPT,
    ROLE_PLAYING_PROMPT,
)
from .permissions import (
    TOOL_PERMISSION_DENIED,
    TOOL_PERMISSION_REQUEST,
)
from .role import (
    REGION_PROMPT,
    ROLE_PLAY_PROMPT,
)
from .thinking import THINKING_PROMPT

__all__ = [
    "THINKING_PROMPT",
    "ROLE_PLAY_PROMPT",
    "REGION_PROMPT",
    "TOOL_PERMISSION_DENIED",
    "TOOL_PERMISSION_REQUEST",
    "INVALID_TOOL_NAME_ERROR_TEMPLATE",
    "TOOL_CALL_ERROR_TEMPLATE",
    "GRO<PERSON>_CHAT_PROMPT",
    "ROLE_PLAYING_PROMPT",
    "NETWORKING_PROMPT",
    "R<PERSON>E_PLAYING_PROMPT",
    "CUSTOMER_ROLE_DESCRIPTION",
    "AGENT_ROLE_DESCRIPTION",
    "REPORT_PROMPT",
    "DASHBOARD_PROMPT",
]
