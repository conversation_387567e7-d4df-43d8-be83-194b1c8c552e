from typing import Any

from langchain_core.tools import BaseTool, StructuredTool
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_mcp_adapters.tools import _list_all_tools
from mcp.types import (
    CallToolResult,
    EmbeddedResource,
    ImageContent,
    TextContent,
)
from mcp.types import (
    Tool as MCPTool,
)

NonTextContent = ImageContent | EmbeddedResource
MAX_ITERATIONS = 1000


def _convert_call_tool_result(
    call_tool_result: CallToolResult,
) -> tuple[str | list[str], list[NonTextContent] | None]:
    text_contents: list[TextContent] = []
    non_text_contents = []
    for content in call_tool_result.content:
        if isinstance(content, TextContent):
            text_contents.append(content)
        else:
            non_text_contents.append(content)

    tool_content: str | list[str] = [content.text for content in text_contents]
    if not text_contents:
        tool_content = ""
    elif len(text_contents) == 1:
        tool_content = tool_content[0]

    return tool_content, non_text_contents or None


def convert_mcp_tool_to_langchain_tool(
    client: MultiServerMCPClient,
    tool: MCPTool,
    server_name: str,
) -> BaseTool:
    """Convert an MCP tool to a LangChain tool."""

    async def call_tool(
        **arguments: dict[str, Any],
    ) -> tuple[str | list[str], list[NonTextContent] | None]:
        try:
            async with client.session(server_name) as session:
                call_tool_result = await session.call_tool(tool.name, arguments)
        except Exception as e:
            return str(e), None
        return _convert_call_tool_result(call_tool_result)

    return StructuredTool(
        name=tool.name,
        description=tool.description or "",
        args_schema=tool.inputSchema,
        coroutine=call_tool,
        response_format="content_and_artifact",
        metadata=tool.annotations.model_dump() if tool.annotations else None,
    )


async def load_mcp_tools(
    client: MultiServerMCPClient,
    server_name: str,
) -> list[BaseTool]:
    """Load all available MCP tools and convert them to LangChain tools.

    Returns:
        list of LangChain tools. Tool annotations are returned as part
        of the tool metadata object.
    """
    async with client.session(server_name) as session:
        tools = await _list_all_tools(session)

    converted_tools = [
        convert_mcp_tool_to_langchain_tool(client, tool, server_name) for tool in tools
    ]
    return converted_tools
