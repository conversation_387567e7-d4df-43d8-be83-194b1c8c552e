import copy
from typing import Any

from langchain_core.tools import BaseTool
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_mcp_adapters.sessions import SSEConnection, StreamableHttpConnection
from mcp.types import Tool

from app.core.config import settings
from app.exceptions.legacy import MCPClientError
from app.logger import logger
from app.models import ConnectionBase, ConnectionTransport

from .custom import load_mcp_tools


class MCPClientConnector:
    """
    Enhanced MCP Client Connector supporting multiple server connections

    This connector allows you to connect to multiple MCP servers simultaneously
    and manage their sessions and tools in a unified interface.
    """

    def __init__(self, mcp_servers: list[ConnectionBase] | None = None):
        """
        Initialize MCPClientConnector with optional server connections

        Args:
            connections: Dictionary mapping server names to their connection configs
        """
        self.mcp_servers: dict[str, ConnectionBase] = {
            server.name: copy.deepcopy(server) for server in mcp_servers or []
        }
        self.client = MultiServerMCPClient(
            connections={
                server.name: SSEConnection(
                    url=server.config.get("url", ""),
                    timeout=server.config.get("timeout", settings.DEFAULT_MCP_TIMEOUT),
                    sse_read_timeout=server.config.get(
                        "sse_read_timeout", settings.DEFAULT_MCP_SSE_READ_TIMEOUT
                    ),
                    headers=server.config.get("headers", {}),
                    transport="sse",
                    session_kwargs=None,
                    httpx_client_factory=None,
                )
                if server.transport_type == ConnectionTransport.SSE
                else StreamableHttpConnection(
                    url=server.config.get("url", ""),
                    timeout=server.config.get("timeout", settings.DEFAULT_MCP_TIMEOUT),
                    sse_read_timeout=server.config.get(
                        "sse_read_timeout", settings.DEFAULT_MCP_SSE_READ_TIMEOUT
                    ),
                    headers=server.config.get("headers", {}),
                    transport="streamable_http",
                    terminate_on_close=True,
                    session_kwargs=None,
                    httpx_client_factory=None,
                )
                for server in self.mcp_servers.values()
            }
        )
        self.connection_status: dict[str, dict] = {}
        for server in self.mcp_servers.values():
            self.connection_status[server.name] = {
                "status": None,
                "status_message": None,
            }
        self.cached_tools: dict[str, list[BaseTool]] = {}

    async def list_tools(
        self, server_name: str, filter_enabled: bool = True
    ) -> list[BaseTool]:
        """
        List tools from an MCP server
        """
        if server_name not in self.client.connections:
            self.connection_status[server_name]["status"] = "error"
            self.connection_status[server_name]["status_message"] = (
                f"Server {server_name} not found"
            )
            raise MCPClientError(f"Server {server_name} not found")

        if server_name in self.cached_tools:
            return self.cached_tools[server_name]

        try:
            tools = await load_mcp_tools(self.client, server_name)

            prefix_tools = []
            for tool in tools:
                tool.name = f"{self.mcp_servers[server_name].prefix}__{tool.name}"
                prefix_tools.append(tool)

            # Filter enabled tools
            if filter_enabled:
                tool_enabled = self.mcp_servers[server_name].tool_enabled
                prefix_tools = [
                    tool for tool in prefix_tools if tool.name in tool_enabled
                ]

            self.connection_status[server_name]["status"] = "connected"
            self.connection_status[server_name]["status_message"] = (
                "Tools listed successfully"
            )
            self.cached_tools[server_name] = prefix_tools
            return prefix_tools
        except Exception as e:
            logger.exception(f"Error listing tools for server {server_name}: {e}")
            self.connection_status[server_name]["status"] = "error"
            self.connection_status[server_name]["status_message"] = (
                f"Error listing tools for server {server_name}"
            )
            raise MCPClientError(f"Error listing tools for server {server_name}")

    async def get_tool_schemas(
        self, server_name: str
    ) -> tuple[list[dict[str, Any]], list[Tool]]:
        """
        Get tool schemas from an MCP server

        Returns:
            Dictionary mapping tool names to their JSON schemas
        """
        if server_name not in self.client.connections:
            self.connection_status[server_name]["status"] = "error"
            self.connection_status[server_name]["status_message"] = (
                f"Server {server_name} not found"
            )
            raise MCPClientError(f"Server {server_name} not found")

        try:
            async with self.client.session(server_name) as session:
                await session.initialize()

                tools = await session.list_tools()
                tool_schemas: list[dict[str, Any]] = [
                    tool.model_dump() for tool in tools.tools
                ]

                # Prefix tools name only not prefix the tool schemas
                tools = [
                    Tool(
                        name=f"{self.mcp_servers[server_name].prefix}__{tool_dict['name']}",
                        description=tool_dict.get("description", ""),
                        inputSchema=tool_dict.get("inputSchema", {}),
                        annotations=tool_dict.get("annotations"),
                    )
                    for tool_dict in tool_schemas
                ]

                self.connection_status[server_name]["status"] = "connected"
                self.connection_status[server_name]["status_message"] = (
                    "Tools listed successfully"
                )
                return tool_schemas, tools
        except Exception as e:
            logger.exception(
                f"Error getting tool schemas for server {server_name}: {e}"
            )
            self.connection_status[server_name]["status"] = "error"
            self.connection_status[server_name]["status_message"] = (
                f"Error getting tool schemas for server {server_name}"
            )
            raise MCPClientError(f"Error getting tool schemas for server {server_name}")

    async def list_all_tools_by_server_names(
        self, server_names: list[str]
    ) -> list[BaseTool]:
        """
        List all tools by server names
        """
        all_tools: list[BaseTool] = []
        for server_name in server_names:
            try:
                all_tools.extend(await self.list_tools(server_name))
            except MCPClientError:
                continue
        return all_tools

    def get_connection_status(self) -> dict[str, dict]:
        """
        Get the connection status for all servers

        Returns:
            Dictionary mapping server names to their connection status
        """
        return self.connection_status
