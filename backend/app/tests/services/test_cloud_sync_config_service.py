import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4

from app.models import (
    CloudProvider,
    CloudSyncConfig,
    CloudSyncConfigCreate,
    CloudSyncConfigUpdate,
    Connection,
    ConnectionType,
    ResourceTypesResponse,
)
from app.services.cloud_sync_config_service import CloudSyncConfigService


@pytest.mark.asyncio
class TestCloudSyncConfigService:
    """Test suite for CloudSyncConfigService"""

    @pytest.fixture
    def mock_session(self):
        """Mock async session"""
        return AsyncMock()

    @pytest.fixture
    def mock_repository(self):
        """Mock CloudSyncConfigRepository"""
        return AsyncMock()

    @pytest.fixture
    def mock_connection_repository(self):
        """Mock ConnectionRepository"""
        return AsyncMock()

    @pytest.fixture
    def service(self, mock_session, mock_repository, mock_connection_repository):
        """CloudSyncConfigService instance with mocked dependencies"""
        service = CloudSyncConfigService(mock_session)
        service.repository = mock_repository
        service.connection_repository = mock_connection_repository
        return service

    @pytest.fixture
    def sample_workspace_id(self):
        return uuid4()

    @pytest.fixture
    def sample_connection_id(self):
        return uuid4()

    @pytest.fixture
    def sample_config_id(self):
        return uuid4()

    @pytest.fixture
    def sample_connection(self, sample_connection_id, sample_workspace_id):
        """Sample cloud connection"""
        return Connection(
            id=sample_connection_id,
            workspace_id=sample_workspace_id,
            name="AWS Production",
            prefix="aws",
            type=ConnectionType.CLOUD,
            config={
                "env": {
                    "AWS_ACCESS_KEY_ID": "test_key",
                    "AWS_SECRET_ACCESS_KEY": "test_secret"
                }
            },
            is_active=True,
            is_installed=True,
        )

    @pytest.fixture
    def sample_config_create(self, sample_connection_id):
        """Sample CloudSyncConfigCreate"""
        return CloudSyncConfigCreate(
            connection_id=sample_connection_id,
            include_stopped_resources=True,
            refresh_interval=120,
            selected_resources=["EC2", "RDS", "S3"],
            is_enabled=True,
        )

    @pytest.fixture
    def sample_config(self, sample_config_id, sample_workspace_id, sample_connection_id):
        """Sample CloudSyncConfig"""
        return CloudSyncConfig(
            id=sample_config_id,
            workspace_id=sample_workspace_id,
            connection_id=sample_connection_id,
            include_stopped_resources=True,
            refresh_interval=120,
            selected_resources=["EC2", "RDS", "S3"],
            is_enabled=True,
        )

    async def test_get_resource_types_for_provider_aws_success(self, service):
        """Test successful retrieval of AWS resource types"""
        result = await service.get_resource_types_for_provider("AWS")
        
        assert isinstance(result, ResourceTypesResponse)
        assert result.cloud_provider == CloudProvider.AWS
        assert len(result.resource_types) > 0
        
        # Check that EC2 is in the results
        ec2_found = any(rt.resource_type == "EC2" for rt in result.resource_types)
        assert ec2_found

    async def test_get_resource_types_for_provider_gcp_success(self, service):
        """Test successful retrieval of GCP resource types"""
        result = await service.get_resource_types_for_provider("GCP")
        
        assert isinstance(result, ResourceTypesResponse)
        assert result.cloud_provider == CloudProvider.GCP
        assert len(result.resource_types) > 0

    async def test_get_resource_types_for_provider_azure_success(self, service):
        """Test successful retrieval of Azure resource types"""
        result = await service.get_resource_types_for_provider("AZURE")
        
        assert isinstance(result, ResourceTypesResponse)
        assert result.cloud_provider == CloudProvider.AZURE
        assert len(result.resource_types) > 0

    async def test_get_resource_types_for_provider_invalid_provider(self, service):
        """Test error handling for invalid cloud provider"""
        with pytest.raises(ValueError, match="Unsupported cloud provider"):
            await service.get_resource_types_for_provider("INVALID")

    async def test_get_resource_types_for_provider_case_insensitive(self, service):
        """Test that provider names are case insensitive"""
        result = await service.get_resource_types_for_provider("aws")
        assert result.cloud_provider == CloudProvider.AWS

    async def test_validate_connection_success(self, service, sample_connection, sample_workspace_id, sample_connection_id):
        """Test successful connection validation"""
        service.connection_repository.get_connection.return_value = sample_connection
        
        result = await service._validate_connection(sample_connection_id, sample_workspace_id)
        
        assert result == sample_connection
        service.connection_repository.get_connection.assert_called_once_with(
            conn_id=sample_connection_id, workspace_id=sample_workspace_id
        )

    async def test_validate_connection_not_found(self, service, sample_workspace_id, sample_connection_id):
        """Test connection validation when connection not found"""
        service.connection_repository.get_connection.return_value = None
        
        with pytest.raises(ValueError, match="Connection not found"):
            await service._validate_connection(sample_connection_id, sample_workspace_id)

    async def test_validate_connection_not_cloud_type(self, service, sample_connection, sample_workspace_id, sample_connection_id):
        """Test connection validation when connection is not cloud type"""
        sample_connection.type = ConnectionType.MCP
        service.connection_repository.get_connection.return_value = sample_connection
        
        with pytest.raises(ValueError, match="Connection must be a cloud connection"):
            await service._validate_connection(sample_connection_id, sample_workspace_id)

    async def test_validate_selected_resources_success(self, service, sample_connection):
        """Test successful resource validation"""
        selected_resources = ["EC2", "RDS", "S3"]
        
        # Should not raise any exception
        await service._validate_selected_resources(selected_resources, sample_connection)

    async def test_validate_selected_resources_empty_list(self, service, sample_connection):
        """Test validation with empty resource list"""
        selected_resources = []
        
        # Should not raise any exception
        await service._validate_selected_resources(selected_resources, sample_connection)

    async def test_validate_selected_resources_invalid_resources(self, service, sample_connection):
        """Test validation with invalid resource types"""
        selected_resources = ["EC2", "INVALID_RESOURCE", "RDS"]
        
        with pytest.raises(ValueError, match="Invalid resource types for AWS"):
            await service._validate_selected_resources(selected_resources, sample_connection)

    async def test_validate_selected_resources_unknown_provider(self, service, sample_connection):
        """Test validation with unknown provider prefix"""
        sample_connection.prefix = "unknown"
        selected_resources = ["EC2"]
        
        with pytest.raises(ValueError, match="Unknown cloud provider for connection prefix"):
            await service._validate_selected_resources(selected_resources, sample_connection)

    @patch('app.services.cloud_sync_config_service.scan_multicloud_resources_by_workspace')
    async def test_update_or_create_config_create_new(
        self, mock_scan_task, service, sample_workspace_id, sample_config_create, 
        sample_connection, sample_config
    ):
        """Test creating a new configuration"""
        service.connection_repository.get_connection.return_value = sample_connection
        service.repository.update_or_create.return_value = sample_config
        mock_scan_task.delay = MagicMock()
        
        result = await service.update_or_create_config(sample_workspace_id, sample_config_create)
        
        assert result.id == sample_config.id
        assert result.workspace_id == sample_workspace_id
        assert result.is_enabled == True
        
        # Verify scan was triggered
        mock_scan_task.delay.assert_called_once()

    @patch('app.services.cloud_sync_config_service.scan_multicloud_resources_by_workspace')
    async def test_update_or_create_config_disabled(
        self, mock_scan_task, service, sample_workspace_id, sample_config_create, 
        sample_connection, sample_config
    ):
        """Test creating a disabled configuration"""
        sample_config_create.is_enabled = False
        sample_config.is_enabled = False
        
        service.connection_repository.get_connection.return_value = sample_connection
        service.repository.update_or_create.return_value = sample_config
        mock_scan_task.delay = MagicMock()
        
        result = await service.update_or_create_config(sample_workspace_id, sample_config_create)
        
        assert result.is_enabled == False
        
        # Verify scan was NOT triggered
        mock_scan_task.delay.assert_not_called()

    async def test_update_or_create_config_invalid_connection(
        self, service, sample_workspace_id, sample_config_create
    ):
        """Test configuration creation with invalid connection"""
        service.connection_repository.get_connection.return_value = None
        
        with pytest.raises(ValueError, match="Connection not found"):
            await service.update_or_create_config(sample_workspace_id, sample_config_create)

    async def test_update_or_create_config_invalid_resources(
        self, service, sample_workspace_id, sample_config_create, sample_connection
    ):
        """Test configuration creation with invalid resources"""
        sample_config_create.selected_resources = ["INVALID_RESOURCE"]
        service.connection_repository.get_connection.return_value = sample_connection
        
        with pytest.raises(ValueError, match="Invalid resource types for AWS"):
            await service.update_or_create_config(sample_workspace_id, sample_config_create)

    async def test_get_config_by_workspace_success(
        self, service, sample_workspace_id, sample_config
    ):
        """Test successful retrieval of workspace configurations"""
        service.repository.get_by_workspace.return_value = [sample_config]
        
        result = await service.get_config_by_workspace(sample_workspace_id)
        
        assert len(result) == 1
        assert result[0].id == sample_config.id
        service.repository.get_by_workspace.assert_called_once_with(sample_workspace_id)

    async def test_get_config_by_workspace_empty(self, service, sample_workspace_id):
        """Test retrieval when no configurations exist"""
        service.repository.get_by_workspace.return_value = []
        
        result = await service.get_config_by_workspace(sample_workspace_id)
        
        assert len(result) == 0

    @patch('app.services.cloud_sync_config_service.scan_multicloud_resources_by_workspace')
    async def test_update_config_success(
        self, mock_scan_task, service, sample_workspace_id, sample_config_id, 
        sample_config, sample_connection
    ):
        """Test successful configuration update"""
        update_data = CloudSyncConfigUpdate(
            refresh_interval=180,
            is_enabled=True
        )
        updated_config = CloudSyncConfig(**sample_config.model_dump())
        updated_config.refresh_interval = 180
        
        service.repository.get_config_by_id.return_value = sample_config
        service.connection_repository.get_connection.return_value = sample_connection
        service.repository.update_config.return_value = updated_config
        mock_scan_task.delay = MagicMock()
        
        result = await service.update_config(sample_workspace_id, sample_config_id, update_data)
        
        assert result.refresh_interval == 180
        mock_scan_task.delay.assert_called_once()

    async def test_update_config_not_found(self, service, sample_workspace_id, sample_config_id):
        """Test updating non-existent configuration"""
        update_data = CloudSyncConfigUpdate(refresh_interval=180)
        service.repository.get_config_by_id.return_value = None
        
        with pytest.raises(ValueError, match="Configuration not found"):
            await service.update_config(sample_workspace_id, sample_config_id, update_data)

    async def test_update_config_wrong_workspace(
        self, service, sample_workspace_id, sample_config_id, sample_config
    ):
        """Test updating configuration from wrong workspace"""
        sample_config.workspace_id = uuid4()  # Different workspace
        update_data = CloudSyncConfigUpdate(refresh_interval=180)
        service.repository.get_config_by_id.return_value = sample_config
        
        with pytest.raises(ValueError, match="Configuration does not belong to this workspace"):
            await service.update_config(sample_workspace_id, sample_config_id, update_data)

    async def test_delete_config_success(
        self, service, sample_workspace_id, sample_config_id, sample_config
    ):
        """Test successful configuration deletion"""
        service.repository.get_config_by_id.return_value = sample_config
        service.repository.delete_config.return_value = True
        
        result = await service.delete_config(sample_workspace_id, sample_config_id)
        
        assert result == True
        service.repository.delete_config.assert_called_once_with(sample_config_id)

    async def test_delete_config_not_found(self, service, sample_workspace_id, sample_config_id):
        """Test deleting non-existent configuration"""
        service.repository.get_config_by_id.return_value = None
        
        result = await service.delete_config(sample_workspace_id, sample_config_id)
        
        assert result == False

    async def test_delete_config_wrong_workspace(
        self, service, sample_workspace_id, sample_config_id, sample_config
    ):
        """Test deleting configuration from wrong workspace"""
        sample_config.workspace_id = uuid4()  # Different workspace
        service.repository.get_config_by_id.return_value = sample_config
        
        with pytest.raises(ValueError, match="Configuration does not belong to this workspace"):
            await service.delete_config(sample_workspace_id, sample_config_id)

    @patch('app.services.cloud_sync_config_service.scan_multicloud_resources_by_workspace')
    async def test_trigger_initial_scan_aws(
        self, mock_scan_task, service, sample_workspace_id, sample_connection_id, sample_connection
    ):
        """Test triggering initial scan for AWS"""
        service.connection_repository.get_connection.return_value = sample_connection
        mock_scan_task.delay = MagicMock()
        
        await service._trigger_initial_scan(sample_workspace_id, sample_connection_id)
        
        mock_scan_task.delay.assert_called_once_with(
            workspace_id=sample_workspace_id,
            providers=["AWS"],
            types=None
        )

    @patch('app.services.cloud_sync_config_service.scan_multicloud_resources_by_workspace')
    async def test_trigger_initial_scan_connection_not_found(
        self, mock_scan_task, service, sample_workspace_id, sample_connection_id
    ):
        """Test triggering scan when connection not found"""
        service.connection_repository.get_connection.return_value = None
        mock_scan_task.delay = MagicMock()
        
        # Should not raise exception, just log warning
        await service._trigger_initial_scan(sample_workspace_id, sample_connection_id)
        
        mock_scan_task.delay.assert_not_called()

    @patch('app.services.cloud_sync_config_service.scan_multicloud_resources_by_workspace')
    async def test_trigger_initial_scan_unknown_provider(
        self, mock_scan_task, service, sample_workspace_id, sample_connection_id, sample_connection
    ):
        """Test triggering scan with unknown provider"""
        sample_connection.prefix = "unknown"
        service.connection_repository.get_connection.return_value = sample_connection
        mock_scan_task.delay = MagicMock()
        
        # Should not raise exception, just log warning
        await service._trigger_initial_scan(sample_workspace_id, sample_connection_id)
        
        mock_scan_task.delay.assert_not_called()