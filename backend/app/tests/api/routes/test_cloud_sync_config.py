import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch
from uuid import uuid4

from app.models import Connection, ConnectionType, CloudSyncConfig


class TestCloudSyncConfigAPI:
    """Integration tests for cloud sync configuration API endpoints"""

    @pytest.fixture
    def workspace_id(self):
        return uuid4()

    @pytest.fixture
    def connection_id(self):
        return uuid4()

    @pytest.fixture
    def config_id(self):
        return uuid4()

    def test_get_resource_types_aws_success(
        self, client: TestClient, superuser_token_headers: dict[str, str]
    ):
        """Test successful retrieval of AWS resource types"""
        response = client.get(
            "/api/v1/cloud-sync-config/resource-types?cloud_provider=AWS",
            headers=superuser_token_headers,
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["cloud_provider"] == "AWS"
        assert "resource_types" in data
        assert len(data["resource_types"]) > 0
        
        # Check structure of resource type
        resource_type = data["resource_types"][0]
        assert "resource_type" in resource_type
        assert "category" in resource_type
        assert "display_name" in resource_type
        assert "description" in resource_type

    def test_get_resource_types_gcp_success(
        self, client: TestClient, superuser_token_headers: dict[str, str]
    ):
        """Test successful retrieval of GCP resource types"""
        response = client.get(
            "/api/v1/cloud-sync-config/resource-types?cloud_provider=GCP",
            headers=superuser_token_headers,
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["cloud_provider"] == "GCP"

    def test_get_resource_types_azure_success(
        self, client: TestClient, superuser_token_headers: dict[str, str]
    ):
        """Test successful retrieval of Azure resource types"""
        response = client.get(
            "/api/v1/cloud-sync-config/resource-types?cloud_provider=AZURE",
            headers=superuser_token_headers,
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["cloud_provider"] == "AZURE"

    def test_get_resource_types_case_insensitive(
        self, client: TestClient, superuser_token_headers: dict[str, str]
    ):
        """Test that cloud provider parameter is case insensitive"""
        response = client.get(
            "/api/v1/cloud-sync-config/resource-types?cloud_provider=aws",
            headers=superuser_token_headers,
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["cloud_provider"] == "AWS"

    def test_get_resource_types_invalid_provider(
        self, client: TestClient, superuser_token_headers: dict[str, str]
    ):
        """Test error handling for invalid cloud provider"""
        response = client.get(
            "/api/v1/cloud-sync-config/resource-types?cloud_provider=INVALID",
            headers=superuser_token_headers,
        )
        
        assert response.status_code == 400
        assert "Unsupported cloud provider" in response.json()["detail"]

    def test_get_resource_types_missing_parameter(
        self, client: TestClient, superuser_token_headers: dict[str, str]
    ):
        """Test error when cloud_provider parameter is missing"""
        response = client.get(
            "/api/v1/cloud-sync-config/resource-types",
            headers=superuser_token_headers,
        )
        
        assert response.status_code == 422  # Validation error

    def test_get_resource_types_unauthorized(self, client: TestClient):
        """Test that authentication is required"""
        response = client.get(
            "/api/v1/cloud-sync-config/resource-types?cloud_provider=AWS"
        )
        
        assert response.status_code == 401

    @patch('app.services.cloud_sync_config_service.CloudSyncConfigService.update_or_create_config')
    def test_create_config_success(
        self, mock_service, client: TestClient, superuser_token_headers: dict[str, str],
        connection_id, config_id
    ):
        """Test successful configuration creation"""
        # Mock service response
        from app.models import CloudSyncConfigPublic
        mock_config = CloudSyncConfigPublic(
            id=config_id,
            workspace_id=uuid4(),
            connection_id=connection_id,
            include_stopped_resources=True,
            refresh_interval=120,
            selected_resources=["EC2", "RDS"],
            is_enabled=True,
            created_at="2024-01-01T00:00:00",
            updated_at="2024-01-01T00:00:00",
        )
        mock_service.return_value = mock_config
        
        config_data = {
            "connection_id": str(connection_id),
            "include_stopped_resources": True,
            "refresh_interval": 120,
            "selected_resources": ["EC2", "RDS"],
            "is_enabled": True,
        }
        
        response = client.post(
            "/api/v1/cloud-sync-config/",
            headers=superuser_token_headers,
            json=config_data,
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == str(config_id)
        assert data["connection_id"] == str(connection_id)
        assert data["refresh_interval"] == 120

    def test_create_config_invalid_data(
        self, client: TestClient, superuser_token_headers: dict[str, str]
    ):
        """Test configuration creation with invalid data"""
        config_data = {
            "connection_id": "invalid-uuid",
            "refresh_interval": -1,  # Invalid
            "selected_resources": [],
        }
        
        response = client.post(
            "/api/v1/cloud-sync-config/",
            headers=superuser_token_headers,
            json=config_data,
        )
        
        assert response.status_code == 422  # Validation error

    def test_create_config_unauthorized(self, client: TestClient, connection_id):
        """Test that authentication is required for creation"""
        config_data = {
            "connection_id": str(connection_id),
            "refresh_interval": 120,
            "selected_resources": ["EC2"],
        }
        
        response = client.post(
            "/api/v1/cloud-sync-config/",
            json=config_data,
        )
        
        assert response.status_code == 401

    @patch('app.services.cloud_sync_config_service.CloudSyncConfigService.get_config_by_workspace')
    def test_get_workspace_configs_success(
        self, mock_service, client: TestClient, superuser_token_headers: dict[str, str],
        config_id, connection_id
    ):
        """Test successful retrieval of workspace configurations"""
        # Mock service response
        from app.models import CloudSyncConfigPublic
        mock_configs = [
            CloudSyncConfigPublic(
                id=config_id,
                workspace_id=uuid4(),
                connection_id=connection_id,
                include_stopped_resources=True,
                refresh_interval=120,
                selected_resources=["EC2", "RDS"],
                is_enabled=True,
                created_at="2024-01-01T00:00:00",
                updated_at="2024-01-01T00:00:00",
            )
        ]
        mock_service.return_value = mock_configs
        
        response = client.get(
            "/api/v1/cloud-sync-config/",
            headers=superuser_token_headers,
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["id"] == str(config_id)

    @patch('app.services.cloud_sync_config_service.CloudSyncConfigService.get_config_by_workspace')
    def test_get_workspace_configs_empty(
        self, mock_service, client: TestClient, superuser_token_headers: dict[str, str]
    ):
        """Test retrieval when no configurations exist"""
        mock_service.return_value = []
        
        response = client.get(
            "/api/v1/cloud-sync-config/",
            headers=superuser_token_headers,
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 0

    def test_get_workspace_configs_unauthorized(self, client: TestClient):
        """Test that authentication is required"""
        response = client.get("/api/v1/cloud-sync-config/")
        assert response.status_code == 401

    @patch('app.services.cloud_sync_config_service.CloudSyncConfigService.update_config')
    def test_update_config_success(
        self, mock_service, client: TestClient, superuser_token_headers: dict[str, str],
        config_id, connection_id
    ):
        """Test successful configuration update"""
        # Mock service response
        from app.models import CloudSyncConfigPublic
        mock_config = CloudSyncConfigPublic(
            id=config_id,
            workspace_id=uuid4(),
            connection_id=connection_id,
            include_stopped_resources=False,
            refresh_interval=180,
            selected_resources=["EC2"],
            is_enabled=True,
            created_at="2024-01-01T00:00:00",
            updated_at="2024-01-01T01:00:00",
        )
        mock_service.return_value = mock_config
        
        update_data = {
            "refresh_interval": 180,
            "include_stopped_resources": False,
            "selected_resources": ["EC2"],
        }
        
        response = client.put(
            f"/api/v1/cloud-sync-config/{config_id}",
            headers=superuser_token_headers,
            json=update_data,
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["refresh_interval"] == 180
        assert data["include_stopped_resources"] == False

    def test_update_config_invalid_id(
        self, client: TestClient, superuser_token_headers: dict[str, str]
    ):
        """Test updating with invalid config ID"""
        update_data = {"refresh_interval": 180}
        
        response = client.put(
            "/api/v1/cloud-sync-config/invalid-uuid",
            headers=superuser_token_headers,
            json=update_data,
        )
        
        assert response.status_code == 422  # Validation error

    def test_update_config_unauthorized(self, client: TestClient, config_id):
        """Test that authentication is required for updates"""
        update_data = {"refresh_interval": 180}
        
        response = client.put(
            f"/api/v1/cloud-sync-config/{config_id}",
            json=update_data,
        )
        
        assert response.status_code == 401

    @patch('app.services.cloud_sync_config_service.CloudSyncConfigService.delete_config')
    def test_delete_config_success(
        self, mock_service, client: TestClient, superuser_token_headers: dict[str, str],
        config_id
    ):
        """Test successful configuration deletion"""
        mock_service.return_value = True
        
        response = client.delete(
            f"/api/v1/cloud-sync-config/{config_id}",
            headers=superuser_token_headers,
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "successfully deleted" in data["message"]

    @patch('app.services.cloud_sync_config_service.CloudSyncConfigService.delete_config')
    def test_delete_config_not_found(
        self, mock_service, client: TestClient, superuser_token_headers: dict[str, str],
        config_id
    ):
        """Test deleting non-existent configuration"""
        mock_service.return_value = False
        
        response = client.delete(
            f"/api/v1/cloud-sync-config/{config_id}",
            headers=superuser_token_headers,
        )
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]

    def test_delete_config_unauthorized(self, client: TestClient, config_id):
        """Test that authentication is required for deletion"""
        response = client.delete(f"/api/v1/cloud-sync-config/{config_id}")
        assert response.status_code == 401

    @patch('app.services.cloud_sync_config_service.CloudSyncConfigService.get_config_by_workspace')
    @patch('app.services.cloud_sync_config_service.CloudSyncConfigService._trigger_initial_scan')
    def test_trigger_manual_sync_success(
        self, mock_trigger, mock_get_configs, client: TestClient, 
        superuser_token_headers: dict[str, str], config_id, connection_id
    ):
        """Test successful manual sync trigger"""
        # Mock service responses
        from app.models import CloudSyncConfigPublic
        mock_configs = [
            CloudSyncConfigPublic(
                id=config_id,
                workspace_id=uuid4(),
                connection_id=connection_id,
                include_stopped_resources=True,
                refresh_interval=120,
                selected_resources=["EC2"],
                is_enabled=True,
                created_at="2024-01-01T00:00:00",
                updated_at="2024-01-01T00:00:00",
            )
        ]
        mock_get_configs.return_value = mock_configs
        mock_trigger.return_value = None
        
        response = client.post(
            f"/api/v1/cloud-sync-config/{config_id}/sync",
            headers=superuser_token_headers,
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "triggered successfully" in data["message"]

    @patch('app.services.cloud_sync_config_service.CloudSyncConfigService.get_config_by_workspace')
    def test_trigger_manual_sync_config_not_found(
        self, mock_get_configs, client: TestClient, 
        superuser_token_headers: dict[str, str], config_id
    ):
        """Test manual sync trigger when configuration not found"""
        mock_get_configs.return_value = []  # No configs found
        
        response = client.post(
            f"/api/v1/cloud-sync-config/{config_id}/sync",
            headers=superuser_token_headers,
        )
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]

    @patch('app.services.cloud_sync_config_service.CloudSyncConfigService.get_config_by_workspace')
    def test_trigger_manual_sync_config_disabled(
        self, mock_get_configs, client: TestClient, 
        superuser_token_headers: dict[str, str], config_id, connection_id
    ):
        """Test manual sync trigger when configuration is disabled"""
        # Mock disabled config
        from app.models import CloudSyncConfigPublic
        mock_configs = [
            CloudSyncConfigPublic(
                id=config_id,
                workspace_id=uuid4(),
                connection_id=connection_id,
                include_stopped_resources=True,
                refresh_interval=120,
                selected_resources=["EC2"],
                is_enabled=False,  # Disabled
                created_at="2024-01-01T00:00:00",
                updated_at="2024-01-01T00:00:00",
            )
        ]
        mock_get_configs.return_value = mock_configs
        
        response = client.post(
            f"/api/v1/cloud-sync-config/{config_id}/sync",
            headers=superuser_token_headers,
        )
        
        assert response.status_code == 400
        assert "disabled" in response.json()["detail"]

    def test_trigger_manual_sync_unauthorized(self, client: TestClient, config_id):
        """Test that authentication is required for manual sync"""
        response = client.post(f"/api/v1/cloud-sync-config/{config_id}/sync")
        assert response.status_code == 401

    def test_api_endpoints_have_proper_tags(self, client: TestClient):
        """Test that API endpoints are properly tagged for documentation"""
        response = client.get("/openapi.json")
        assert response.status_code == 200
        
        openapi_spec = response.json()
        paths = openapi_spec.get("paths", {})
        
        # Check that our endpoints exist and have proper tags
        resource_types_path = "/api/v1/cloud-sync-config/resource-types"
        if resource_types_path in paths:
            get_operation = paths[resource_types_path].get("get", {})
            assert "cloud-sync-config" in get_operation.get("tags", [])

    def test_validation_error_format(
        self, client: TestClient, superuser_token_headers: dict[str, str]
    ):
        """Test that validation errors are properly formatted"""
        # Send invalid data
        config_data = {
            "connection_id": "not-a-uuid",
            "refresh_interval": 0,  # Below minimum
        }
        
        response = client.post(
            "/api/v1/cloud-sync-config/",
            headers=superuser_token_headers,
            json=config_data,
        )
        
        assert response.status_code == 422
        error_data = response.json()
        assert "detail" in error_data
        assert isinstance(error_data["detail"], list)  # FastAPI validation error format