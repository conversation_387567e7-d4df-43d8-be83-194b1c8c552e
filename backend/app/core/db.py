from datetime import datetime

from sqlalchemy.ext.asyncio import create_async_engine
from sqlmodel import Session, create_engine, select

from app import crud
from app.core.config import settings
from app.models import (
    ModuleSetting,
    User,
    UserCreate,
)

# Add proper connection pool configuration
engine = create_engine(
    str(settings.SQLALCHEMY_DATABASE_URI),
    pool_size=10,  # Maximum number of persistent connections
    max_overflow=20,  # Maximum number of connections above pool_size
    pool_timeout=30,  # Seconds to wait before timing out on getting a connection
    pool_recycle=1800,  # Recycle connections after 30 minutes
    pool_pre_ping=True,  # Test connections for liveness upon checkout
)

async_engine = create_async_engine(
    str(settings.SQLALCHEMY_DATABASE_URI),
    pool_size=10,  # Maximum number of persistent connections
    max_overflow=20,  # Maximum number of connections above pool_size
    pool_timeout=30,  # Seconds to wait before timing out on getting a connection
    pool_recycle=1800,  # Recycle connections after 30 minutes
    pool_pre_ping=True,  # Test connections for liveness upon checkout
)

# make sure all SQLModel models are imported (app.models) before initializing DB
# otherwise, SQLModel might fail to initialize relationships properly
# for more details: https://github.com/fastapi/full-stack-fastapi-template/issues/28


def init_module_setting(session: Session) -> None:
    payment_enabled = settings.PAYMENT_ENABLED

    module_setting = session.exec(
        select(ModuleSetting).where(ModuleSetting.key == settings.PAYMENT_MODULE_NAME)
    ).first()

    if not module_setting:
        module_setting = ModuleSetting(
            key=settings.PAYMENT_MODULE_NAME, value={"enabled": payment_enabled}
        )
        session.add(module_setting)
        session.commit()
    elif module_setting.value.get("enabled") != payment_enabled:
        module_setting.value["enabled"] = payment_enabled
        module_setting.updated_at = datetime.now()
        session.add(module_setting)
        session.commit()


def init_db(session: Session) -> None:
    # Tables should be created with Alembic migrations
    # But if you don't want to use migrations, create
    # the tables un-commenting the next lines
    # from sqlmodel import SQLModel

    # from app.core.engine import engine
    # This works because the models are already imported and registered from app.models
    # SQLModel.metadata.create_all(engine)

    user = session.exec(
        select(User).where(User.email == settings.FIRST_SUPERUSER)
    ).first()
    if not user:
        user_in = UserCreate(
            email=settings.FIRST_SUPERUSER,
            password=settings.FIRST_SUPERUSER_PASSWORD,
            is_superuser=True,
        )
        crud.create_user(session=session, user_create=user_in)
