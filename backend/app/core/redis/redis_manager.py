import json
from typing import Any

import redis
from redis.exceptions import ConnectionError, TimeoutError

from app.core.config import settings
from app.logger import logger


class RedisManager:
    """Simple Redis manager for caching operations"""

    def __init__(self):
        self.redis_client: redis.Redis | None = None
        self._connect()

    def _connect(self):
        """Initialize Redis connection"""
        try:
            self.redis_client = redis.Redis(
                host=getattr(settings, "REDIS_SERVER", "redis"),
                port=getattr(settings, "REDIS_PORT", 6379),
                db=getattr(settings, "REDIS_DB", 0),
                password=getattr(settings, "REDIS_PASSWORD", None),
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30,
            )
            # Test connection
            self.redis_client.ping()
            logger.info("Redis connection established successfully")
        except (ConnectionError, TimeoutError) as e:
            logger.warning(f"Redis connection failed: {e}. Caching will be disabled.")
            self.redis_client = None
        except Exception as e:
            logger.error(f"Unexpected error connecting to Redis: {e}")
            self.redis_client = None

    def is_connected(self) -> bool:
        """Check if Redis is connected"""
        if not self.redis_client:
            return False
        try:
            self.redis_client.ping()
            return True
        except Exception:
            return False

    def get(self, key: str) -> Any | None:
        """Get value from cache"""
        if not self.is_connected() or not self.redis_client:
            return None

        try:
            value = self.redis_client.get(key)
            if value:
                return json.loads(str(value))
            return None
        except Exception as e:
            logger.error(f"Error getting cache key {key}: {e}")
            return None

    def set(self, key: str, value: Any, ttl: int | None = None) -> bool:
        """Set value in cache with optional TTL in seconds"""
        if not self.is_connected() or not self.redis_client:
            return False

        try:
            serialized_value = json.dumps(value, default=str)
            if ttl:
                result = self.redis_client.setex(key, ttl, serialized_value)
            else:
                result = self.redis_client.set(key, serialized_value)
            return bool(result)
        except Exception as e:
            logger.error(f"Error setting cache key {key}: {e}")
            return False

    def delete(self, key: str) -> bool:
        """Delete key from cache"""
        if not self.is_connected() or not self.redis_client:
            return False

        try:
            return bool(self.redis_client.delete(key))
        except Exception as e:
            logger.error(f"Error deleting cache key {key}: {e}")
            return False

    def delete_pattern(self, pattern: str) -> int:
        """Delete all keys matching pattern"""
        if not self.is_connected() or not self.redis_client:
            return 0

        try:
            keys = self.redis_client.keys(pattern)
            if keys:
                deleted_count = self.redis_client.delete(*keys)
                return int(deleted_count) if deleted_count is not None else 0
            return 0
        except Exception as e:
            logger.error(f"Error deleting cache pattern {pattern}: {e}")
            return 0

    def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        if not self.is_connected() or not self.redis_client:
            return False

        try:
            return bool(self.redis_client.exists(key))
        except Exception as e:
            logger.error(f"Error checking cache key {key}: {e}")
            return False

    def flush_all(self) -> bool:
        """Flush all cache data"""
        if not self.is_connected() or not self.redis_client:
            return False

        try:
            self.redis_client.flushall()
            return True
        except Exception as e:
            logger.error(f"Error flushing cache: {e}")
            return False

    def set_rate_limit(self, key: str, ttl: int) -> bool:
        """Set a rate limit key with TTL in seconds"""
        if not self.is_connected() or not self.redis_client:
            return False

        try:
            # Use SETNX to set the key only if it doesn't exist
            result = self.redis_client.set(key, "1", ex=ttl, nx=True)
            return bool(result)
        except Exception as e:
            logger.error(f"Error setting rate limit key {key}: {e}")
            return False

    def is_rate_limited(self, key: str) -> bool:
        """Check if a rate limit key exists"""
        return self.exists(key)

    def get_rate_limit_ttl(self, key: str) -> int:
        """Get remaining TTL for a rate limit key in seconds"""
        if not self.is_connected() or not self.redis_client:
            return 0

        try:
            ttl = self.redis_client.ttl(key)
            return int(ttl) if ttl > 0 else 0
        except Exception as e:
            logger.error(f"Error getting TTL for rate limit key {key}: {e}")
            return 0

    # Streaming-specific methods for SSE persistence
    def set_stream_status(self, conversation_id: str, status_data: dict) -> bool:
        """Set stream metadata for a conversation"""
        if not self.is_connected() or not self.redis_client:
            return False
        try:
            key = f"stream_meta:{conversation_id}"
            serialized_data = json.dumps(status_data, default=str)
            result = self.redis_client.setex(key, 86400, serialized_data)  # 24 hour TTL
            return bool(result)
        except Exception as e:
            logger.error(f"Error setting stream status {conversation_id}: {e}")
            return False

    def get_stream_status(self, conversation_id: str) -> dict | None:
        """Get stream metadata for a conversation"""
        if not self.is_connected() or not self.redis_client:
            return None
        try:
            key = f"stream_meta:{conversation_id}"
            value = self.redis_client.get(key)
            if value:
                return json.loads(str(value))
            return None
        except Exception as e:
            logger.error(f"Error getting stream status {conversation_id}: {e}")
            return None

    def update_stream_position(self, conversation_id: str, position: int) -> bool:
        """Update the last stream position"""
        if not self.is_connected() or not self.redis_client:
            return False
        try:
            key = f"stream_meta:{conversation_id}"
            # Get current status data
            current_data = self.get_stream_status(conversation_id)
            if current_data:
                # Update the position
                current_data["last_stream_position"] = position
                # Save back as JSON string with TTL preserved
                serialized_data = json.dumps(current_data, default=str)
                try:
                    ttl = self.redis_client.ttl(key)
                    if isinstance(ttl, int) and ttl > 0:
                        return bool(self.redis_client.setex(key, ttl, serialized_data))
                    else:
                        return bool(self.redis_client.setex(key, 86400, serialized_data))  # Default 24h TTL
                except Exception:
                    # Fallback to default TTL if ttl check fails
                    return bool(self.redis_client.setex(key, 86400, serialized_data))
            return False
        except Exception as e:
            logger.error(f"Error updating stream position {conversation_id}: {e}")
            return False

    def queue_stream_event(self, conversation_id: str, event_data: dict) -> bool:
        """Add event to stream queue"""
        if not self.is_connected() or not self.redis_client:
            return False
        try:
            key = f"stream:{conversation_id}"
            serialized_event = json.dumps(event_data, default=str)
            self.redis_client.lpush(key, serialized_event)
            self.redis_client.expire(key, 86400)  # 24 hour TTL
            return True
        except Exception as e:
            logger.error(f"Error queuing stream event {conversation_id}: {e}")
            return False

    def get_stream_events(self, conversation_id: str, from_position: int = 0) -> list:
        """Get stream events from specified position"""
        if not self.is_connected() or not self.redis_client:
            return []
        try:
            key = f"stream:{conversation_id}"
            events = self.redis_client.lrange(key, 0, -1)
            parsed_events = []
            # Ensure we have a list and it's not None
            if events and isinstance(events, list):
                # Redis lrange returns newest first (LPUSH), so reverse to get chronological order
                for event_str in reversed(events):
                    try:
                        event_data = json.loads(event_str)
                        if event_data.get("position", 0) > from_position:
                            parsed_events.append(event_data)
                    except json.JSONDecodeError:
                        continue
            return sorted(parsed_events, key=lambda x: x.get("position", 0))
        except Exception as e:
            logger.error(f"Error getting stream events {conversation_id}: {e}")
            return []

    def publish_stream_event(self, conversation_id: str, event_data: dict) -> bool:
        """Publish event to real-time channel (for future pub/sub implementation)"""
        if not self.is_connected() or not self.redis_client:
            return False
        try:
            channel = f"stream_live:{conversation_id}"
            serialized_event = json.dumps(event_data, default=str)
            self.redis_client.publish(channel, serialized_event)
            return True
        except Exception as e:
            logger.error(f"Error publishing stream event {conversation_id}: {e}")
            return False

    def cleanup_stream_data(self, conversation_id: str) -> bool:
        """Clean up all stream-related data for a conversation"""
        if not self.is_connected() or not self.redis_client:
            return False
        try:
            keys_to_delete = [
                f"stream:{conversation_id}",
                f"stream_meta:{conversation_id}",
                f"stream_live:{conversation_id}",
            ]
            return bool(self.redis_client.delete(*keys_to_delete))
        except Exception as e:
            logger.error(f"Error cleaning up stream data {conversation_id}: {e}")
            return False
