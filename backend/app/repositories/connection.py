from datetime import datetime
from uuid import UUID

from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.logger import logger
from app.models import (
    Connection,
    ConnectionBase,
    ConnectionTransport,
    ConnectionType,
    ConnectionUpdateParams,
)


class ConnectionRepository:
    def __init__(self, session: AsyncSession):
        self.session = session

    def _validate_url_for_transport_type(
        self, config: dict, transport_type: ConnectionTransport
    ) -> dict:
        if not config or "url" not in config:
            return config

        url = config.get("url", "").strip()
        if not url:
            return config

        # Create a copy of config to avoid modifying the original
        updated_config = config.copy()

        # Remove any existing /sse or /mcp suffixes
        clean_url = url.rstrip("/")
        if clean_url.endswith("/sse") or clean_url.endswith("/mcp"):
            clean_url = clean_url.rsplit("/", 1)[0]

        # Add the correct suffix based on transport type
        if transport_type == ConnectionTransport.SSE:
            if not clean_url.endswith("/sse"):
                updated_config["url"] = f"{clean_url}/sse"
        elif transport_type == ConnectionTransport.STREAMABLE_HTTP:
            if not clean_url.endswith("/mcp"):
                updated_config["url"] = f"{clean_url}/mcp"

        return updated_config

    async def create_connection(
        self, workspace_id: UUID, conn_data: ConnectionBase
    ) -> Connection:
        try:
            # Validate and fix URL suffix for transport type
            validated_config = self._validate_url_for_transport_type(
                conn_data.config, conn_data.type
            )

            # Create new MCPServer instance
            db_conn = Connection(
                **conn_data.model_dump(exclude={"config"}),
                config=validated_config,
                workspace_id=workspace_id,
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )

            self.session.add(db_conn)
            await self.session.commit()
            await self.session.refresh(db_conn)
            return db_conn
        except Exception as e:
            await self.session.rollback()
            raise e

    async def get_connections(self, workspace_id: UUID) -> list[Connection] | None:
        try:
            statement = (
                select(Connection)
                .where(Connection.workspace_id == workspace_id)
                .order_by(Connection.created_at.desc())
            )

            result = await self.session.exec(statement)
            mcp_servers = result.all()

            return list(mcp_servers) if mcp_servers else None
        except Exception as e:
            logger.exception(f"Error getting MCP servers: {e}")
            raise e

    async def get_connections_by_type(
        self, workspace_id: UUID | None, connection_type: ConnectionType
    ) -> list[Connection] | None:
        try:
            statement = select(Connection).where(Connection.type == connection_type)

            if workspace_id is not None:
                statement = statement.where(Connection.workspace_id == workspace_id)
            else:
                # For builtin connections, filter by null workspace_id
                statement = statement.where(Connection.workspace_id.is_(None))

            statement = statement.order_by(Connection.created_at.desc())

            result = await self.session.exec(statement)
            connections = result.all()

            return list(connections) if connections else None
        except Exception as e:
            logger.exception(f"Error getting connections by type: {e}")
            raise e

    async def get_connection(
        self, conn_id: UUID, workspace_id: UUID | None
    ) -> Connection | None:
        try:
            statement = select(Connection).where(Connection.id == conn_id)

            if workspace_id is not None:
                statement = statement.where(Connection.workspace_id == workspace_id)
            else:
                statement = statement.where(Connection.workspace_id.is_(None))

            result = await self.session.exec(statement)
            return result.one_or_none()
        except Exception as e:
            logger.exception(f"Error getting MCP servers: {e}")
            raise e

    async def update_connection(
        self,
        update_params: ConnectionUpdateParams,
    ) -> Connection | None:
        try:
            # Get the existing server
            conn = await self.get_connection(
                update_params.conn_id, update_params.workspace_id
            )
            if not conn:
                return None

            # If connection_data is provided, update the connection fields
            if update_params.connection_data is not None:
                data = update_params.connection_data
                # Handle config update with URL validation first
                if data.config is not None:
                    transport_type = data.type if data.type is not None else conn.type
                    validated_config = self._validate_url_for_transport_type(
                        data.config, transport_type
                    )
                    conn.config = validated_config
                elif data.type is not None and conn.config:
                    validated_config = self._validate_url_for_transport_type(
                        conn.config, data.type
                    )
                    conn.config = validated_config

                # Update all other fields using setattr pattern
                for key, value in data.model_dump(exclude_unset=True).items():
                    if key != "config" and value is not None and hasattr(conn, key):
                        setattr(conn, key, value)

            # Always update timestamp
            conn.updated_at = datetime.now()

            self.session.add(conn)
            await self.session.commit()
            await self.session.refresh(conn)
            return conn
        except Exception as e:
            await self.session.rollback()
            raise e

    async def delete_connection(self, server_id: UUID, workspace_id: UUID) -> bool:
        try:
            conn = await self.get_connection(server_id, workspace_id)
            if not conn:
                return False

            await self.session.delete(conn)
            await self.session.commit()
            return True
        except Exception as e:
            await self.session.rollback()
            raise e
