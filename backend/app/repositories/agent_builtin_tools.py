from uuid import <PERSON><PERSON><PERSON>

from sqlmodel import col, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.exceptions.legacy import RepositoryError
from app.logger import logger
from app.models import AgentBuiltInTool, BuiltInTool, WorkspaceBuiltInTool
from app.schemas.agents_builtin_tools import AgentBuiltInToolPublic


class AgentBuiltinToolRepository:
    def __init__(self, async_session: AsyncSession):
        self.async_session = async_session

    async def init_agent_builtin_tools(
        self,
        agent_id: UUID,
        all_workspace_builtin_tool_ids: list[UUID],
        active_tool_ids: list[UUID],
    ) -> None:
        """Initialize all builtin tools for an agent, setting is_active based on active_tool_ids."""
        try:
            # Create tool associations
            for workspace_tool_id in all_workspace_builtin_tool_ids:
                is_active = workspace_tool_id in active_tool_ids
                new_tool = AgentBuiltInTool(
                    agent_id=agent_id,
                    workspace_builtin_tool_id=workspace_tool_id,
                    is_active=is_active,
                )
                self.async_session.add(new_tool)

            await self.async_session.commit()
        except Exception as e:
            await self.async_session.rollback()
            logger.exception(f"Error initializing agent {agent_id} builtin tools. {e}")
            raise RepositoryError(
                f"Error initializing agent {agent_id} builtin tools", 500
            )

    async def update_agent_builtin_tools(
        self,
        workspace_builtin_tool_id: UUID,
        agent_id: UUID,
        is_active: bool,
    ) -> bool | None:
        """Update the active status of a built-in tool for an agent."""
        try:
            result = await self.async_session.exec(
                select(AgentBuiltInTool).where(
                    AgentBuiltInTool.workspace_builtin_tool_id
                    == workspace_builtin_tool_id,
                    AgentBuiltInTool.agent_id == agent_id,
                )
            )
            agent_builtin_tool = result.first()

            if not agent_builtin_tool:
                raise RepositoryError(
                    f"Agent builtin tool not found for agent {agent_id} and workspace builtin tool {workspace_builtin_tool_id}",
                    404,
                )

            agent_builtin_tool.is_active = is_active
            self.async_session.add(agent_builtin_tool)
            await self.async_session.commit()
            return True
        except Exception as e:
            await self.async_session.rollback()
            logger.exception(f"Error updating agent {agent_id} builtin tools. {e}")
            raise RepositoryError(f"Error updating agent {agent_id} builtin tools", 500)

    async def get_all_agent_builtin_tools(
        self, agent_ids: list[UUID]
    ) -> dict[UUID, list[AgentBuiltInToolPublic]]:
        """Get all builtin tools associated with agents using the many-to-many relationship."""
        try:
            statement = (
                select(AgentBuiltInTool, BuiltInTool, WorkspaceBuiltInTool)
                .join(
                    WorkspaceBuiltInTool,
                    col(AgentBuiltInTool.workspace_builtin_tool_id)
                    == col(WorkspaceBuiltInTool.id),
                )
                .join(
                    BuiltInTool,
                    col(WorkspaceBuiltInTool.builtin_tool_id) == col(BuiltInTool.id),
                )
                .where(col(AgentBuiltInTool.agent_id).in_(agent_ids))
            )
            result = await self.async_session.exec(statement)

            # Group builtin tools by agent ID
            agent_tools: dict[UUID, list[AgentBuiltInToolPublic]] = {}
            for agent_builtin_tool, tool, workspace_builtin_tool in result:
                if agent_builtin_tool.agent_id not in agent_tools:
                    agent_tools[agent_builtin_tool.agent_id] = []
                agent_tools[agent_builtin_tool.agent_id].append(
                    AgentBuiltInToolPublic(
                        id=workspace_builtin_tool.id,
                        name=tool.name,
                        display_name=tool.display_name,
                        description=tool.description,
                        required_permission=workspace_builtin_tool.required_permission,
                        is_active=agent_builtin_tool.is_active,
                    )
                )
            return agent_tools
        except Exception as e:
            logger.exception(f"Error getting all agent builtin tools: {e}")
            raise RepositoryError(
                status_code=500,
                message="Failed to get all agent builtin tools.",
            )
