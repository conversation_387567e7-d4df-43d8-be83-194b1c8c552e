from uuid import UUI<PERSON>

from sqlmodel import col, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.exceptions.legacy import RepositoryError
from app.logger import logger
from app.models import AgentConnection, Connection, ConnectionPublic


class AgentConnectionRepository:
    def __init__(self, async_session: AsyncSession):
        self.async_session = async_session

    async def create_agent_connection(
        self,
        agent_id: UUID,
        conn_id: UUID,
    ) -> None:
        """Create a connection association for an agent."""
        try:
            # Check if connection already exists
            result = await self.async_session.exec(
                select(AgentConnection).where(
                    AgentConnection.agent_id == agent_id,
                    AgentConnection.conn_id == conn_id,
                )
            )
            existing_connection = result.first()

            if existing_connection:
                return  # Already exists, no need to add

            new_connection = AgentConnection(
                agent_id=agent_id,
                conn_id=conn_id,
            )
            self.async_session.add(new_connection)
            await self.async_session.commit()
        except Exception as e:
            await self.async_session.rollback()
            logger.exception(
                f"Error creating connection {conn_id} for agent {agent_id}. {e}"
            )
            raise RepositoryError(
                f"Error creating connection for agent {agent_id}", 500
            )

    async def remove_agent_connection(
        self,
        agent_id: UUID,
        conn_id: UUID,
    ) -> None:
        """Remove a connection from an agent."""
        try:
            result = await self.async_session.exec(
                select(AgentConnection).where(
                    AgentConnection.agent_id == agent_id,
                    AgentConnection.conn_id == conn_id,
                )
            )
            agent_connection = result.first()

            if not agent_connection:
                raise RepositoryError(
                    f"Agent connection not found for agent {agent_id} and connection {conn_id}",
                    404,
                )

            await self.async_session.delete(agent_connection)
            await self.async_session.commit()
        except Exception as e:
            await self.async_session.rollback()
            logger.exception(
                f"Error removing connection {conn_id} from agent {agent_id}. {e}"
            )
            raise RepositoryError(
                f"Error removing connection from agent {agent_id}", 500
            )

    async def bulk_create_agent_connections(
        self,
        agent_id: UUID,
        connection_ids: list[UUID],
    ) -> None:
        """Bulk create connection associations for an agent."""
        try:
            # Get existing connections to avoid duplicates
            existing_result = await self.async_session.exec(
                select(AgentConnection.conn_id).where(
                    AgentConnection.agent_id == agent_id,
                    col(AgentConnection.conn_id).in_(connection_ids),
                )
            )
            existing_conn_ids = set(existing_result.all())

            # Create new connections only for non-existing ones
            new_connections = []
            for conn_id in connection_ids:
                if conn_id not in existing_conn_ids:
                    new_connections.append(
                        AgentConnection(
                            agent_id=agent_id,
                            conn_id=conn_id,
                        )
                    )

            if new_connections:
                self.async_session.add_all(new_connections)
                await self.async_session.commit()
        except Exception as e:
            await self.async_session.rollback()
            logger.exception(
                f"Error bulk creating connections for agent {agent_id}. {e}"
            )
            raise RepositoryError(
                f"Error bulk creating connections for agent {agent_id}", 500
            )

    async def bulk_delete_agent_connections(
        self,
        agent_id: UUID,
        connection_ids: list[UUID],
    ) -> None:
        """Bulk delete connection associations for an agent."""
        try:
            # Get connections to delete
            result = await self.async_session.exec(
                select(AgentConnection).where(
                    AgentConnection.agent_id == agent_id,
                    col(AgentConnection.conn_id).in_(connection_ids),
                )
            )
            connections_to_delete = list(result.all())

            # Delete connections
            for connection in connections_to_delete:
                await self.async_session.delete(connection)

            await self.async_session.commit()
        except Exception as e:
            await self.async_session.rollback()
            logger.exception(
                f"Error bulk deleting connections for agent {agent_id}. {e}"
            )
            raise RepositoryError(
                f"Error bulk deleting connections for agent {agent_id}", 500
            )

    async def get_all_agent_connections(
        self, agent_ids: list[UUID]
    ) -> dict[UUID, list[ConnectionPublic]]:
        """Get all connections associated with multiple agents."""
        try:
            statement = (
                select(AgentConnection, Connection)
                .join(
                    Connection,
                    col(AgentConnection.conn_id) == col(Connection.id),
                )
                .where(col(AgentConnection.agent_id).in_(agent_ids))
            )
            result = await self.async_session.exec(statement)

            # Group connections by agent ID and convert to ConnectionPublic
            agent_connections: dict[UUID, list[ConnectionPublic]] = {}
            for agent_connection, connection in result:
                if agent_connection.agent_id not in agent_connections:
                    agent_connections[agent_connection.agent_id] = []

                # Convert Connection to ConnectionPublic
                connection_public = ConnectionPublic(
                    id=connection.id,
                    workspace_id=connection.workspace_id,
                    created_at=connection.created_at,
                    updated_at=connection.updated_at,
                    name=connection.name,
                    prefix=connection.prefix,
                    type=connection.type,
                    transport_type=connection.transport_type,
                    config=connection.config,
                    is_active=connection.is_active,
                    tool_list=connection.tool_list,
                    tool_permissions=connection.tool_permissions,
                    tool_enabled=connection.tool_enabled,
                    tool_schemas=connection.tool_schemas,
                    status=connection.status,
                    status_message=connection.status_message,
                    status_updated_at=connection.status_updated_at,
                )
                agent_connections[agent_connection.agent_id].append(connection_public)

            return agent_connections
        except Exception as e:
            logger.exception(f"Error getting all agent connections: {e}")
            raise RepositoryError(
                status_code=500,
                message="Failed to get all agent connections.",
            )
