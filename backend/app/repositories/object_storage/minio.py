import asyncio
import io
from collections.abc import Async<PERSON>enerator
from datetime import <PERSON><PERSON><PERSON>
from uuid import uuid4

from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UploadFile
from miniopy_async import Minio
from miniopy_async.error import MinioException, S3Error

from app.core.config import settings

from .base import BaseStorageRepository


class MinioStorageRepository(BaseStorageRepository):
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        self.client = Minio(
            settings.MINIO_ENDPOINT,
            access_key=settings.MINIO_ACCESS_KEY,
            secret_key=settings.MINIO_SECRET_KEY,
            secure=settings.MINIO_SECURE,
        )
        self.images_bucket = settings.IMAGES_BUCKET
        self.kb_bucket = settings.KB_BUCKET
        self._ensure_buckets_init_task = None

    async def ensure_buckets_exist(self) -> None:
        try:
            # Check if images bucket exists, create if not
            if not await self.client.bucket_exists(self.images_bucket):
                await self.client.make_bucket(self.images_bucket)

            # Check if kb bucket exists, create if not
            if not await self.client.bucket_exists(self.kb_bucket):
                await self.client.make_bucket(self.kb_bucket)

        except S3Error as err:
            raise HTTPException(
                status_code=500, detail=f"Failed to ensure buckets exist: {err}"
            )

    async def _init_buckets(self) -> None:
        if self._ensure_buckets_init_task is None:
            self._ensure_buckets_init_task = asyncio.create_task(
                self.ensure_buckets_exist()
            )
        await self._ensure_buckets_init_task

    async def upload_file(
        self,
        file: UploadFile,
        prefix: str,
        bucket_name: str,
        metadata: dict[str, str] | None = None,
    ) -> dict:
        """
        Upload a file to the specified bucket.

        Args:
            file: The file to upload
            bucket_name: Target bucket name (defaults to images bucket)
            metadata: Optional metadata to attach to the object

        Returns:
            Object name/path of the uploaded file
        """
        await self._init_buckets()

        content_type = file.content_type
        try:
            # Read file content
            file_data = await file.read()
            file_size = len(file_data)

            # Upload to MinIO
            object_name = f"{prefix}/{file.filename}"
            await self.client.put_object(
                bucket_name,
                object_name,
                io.BytesIO(file_data),
                file_size,
                content_type=content_type,
                metadata=metadata,
            )
            return {
                "object_name": object_name,
                "name": file.filename,
                "type": content_type,
            }

        except (S3Error, MinioException) as err:
            raise HTTPException(status_code=500, detail=f"Failed to upload file: {err}")
        finally:
            # Reset file pointer for potential reuse
            await file.seek(0)

    async def upload_files(
        self,
        files: list[UploadFile],
        prefix: str,
        bucket_name: str,
    ) -> list[str]:
        """
        Upload multiple files to the specified bucket.
        """
        await self._init_buckets()

        return await asyncio.gather(
            *[
                self.upload_file(
                    file,
                    prefix=prefix,
                    bucket_name=bucket_name,
                )
                for file in files
            ]
        )

    async def upload_bytes(
        self,
        data: bytes,
        bucket_name: str,
        object_name: str | None = None,
        content_type: str = "application/octet-stream",
        metadata: dict[str, str] | None = None,
    ) -> str:
        """
        Upload bytes data to the specified bucket.

        Args:
            data: The bytes data to upload
            bucket_name: Target bucket name (defaults to images bucket)
            object_name: Custom object name (generated if not provided)
            content_type: MIME type of the content
            metadata: Optional metadata to attach to the object

        Returns:
            Object name/path of the uploaded bytes
        """
        await self._init_buckets()

        # Generate a unique object name if not provided
        if not object_name:
            object_name = str(uuid4())

        try:
            # Upload to MinIO
            await self.client.put_object(
                bucket_name,
                object_name,
                io.BytesIO(data),
                len(data),
                content_type=content_type,
                metadata=metadata,
            )

            return object_name
        except (S3Error, MinioException) as err:
            raise HTTPException(
                status_code=500, detail=f"Failed to upload bytes data: {err}"
            )

    async def get_file(
        self, object_name: str, bucket_name: str
    ) -> tuple[io.BytesIO, int, str, dict[str, str]]:
        """
        Get a file from the specified bucket.

        Args:
            object_name: The name of the object to retrieve
            bucket_name: Source bucket name (defaults to images bucket)

        Returns:
            Tuple containing file data, size, content type, and metadata
        """
        await self._init_buckets()

        try:
            # Get object stats to retrieve metadata and content type
            stat = await self.client.stat_object(bucket_name, object_name)

            # Get the object data
            response = await self.client.get_object(bucket_name, object_name)
            data = await response.read()

            # Return file data, size, content type, and metadata
            return (io.BytesIO(data), stat.size, stat.content_type, stat.metadata)
        except S3Error as err:
            if err.code == "NoSuchKey":
                raise HTTPException(
                    status_code=404,
                    detail=f"Object {object_name} not found in bucket {bucket_name}",
                )
            raise HTTPException(
                status_code=500, detail=f"Failed to retrieve file: {err}"
            )
        finally:
            # Ensure the response is closed to release resources
            if response:
                response.close()
                response.release()

    async def stream_file(
        self, object_name: str, bucket_name: str
    ) -> AsyncGenerator[bytes, None]:
        """
        Stream a file from the specified bucket in chunks.

        Args:
            object_name: The name of the object to retrieve
            bucket_name: Source bucket name (defaults to images bucket)

        Yields:
            Chunks of file data as bytes
        """
        await self._init_buckets()

        try:
            response = await self.client.get_object(bucket_name, object_name)

            # Stream the response in chunks
            chunk = await response.read(8192)  # 8KB chunks
            while chunk:
                yield chunk
                chunk = await response.read(8192)

            await response.close()
        except S3Error as err:
            if err.code == "NoSuchKey":
                raise HTTPException(
                    status_code=404,
                    detail=f"Object {object_name} not found in bucket {bucket_name}",
                )
            raise HTTPException(status_code=500, detail=f"Failed to stream file: {err}")

    async def download_file(
        self, object_name: str, file_path: str, bucket_name: str
    ) -> None:
        """
        Download a file from the bucket to a local path.

        Args:
            object_name: The name of the object to download
            file_path: Local path where the file will be saved
            bucket_name: Source bucket name (defaults to images bucket)
        """
        await self._init_buckets()

        try:
            await self.client.fget_object(bucket_name, object_name, file_path)
        except S3Error as err:
            if err.code == "NoSuchKey":
                raise HTTPException(
                    status_code=404,
                    detail=f"Object {object_name} not found in bucket {bucket_name}",
                )
            raise HTTPException(
                status_code=500, detail=f"Failed to download file: {err}"
            )

    async def delete_file(self, object_name: str, bucket_name: str) -> None:
        """
        Delete a file from the specified bucket.

        Args:
            object_name: The name of the object to delete
            bucket_name: Source bucket name (defaults to images bucket)
        """
        await self._init_buckets()

        try:
            await self.client.remove_object(bucket_name, object_name)
        except S3Error as err:
            # If object doesn't exist, we consider deletion successful
            if err.code != "NoSuchKey":
                raise HTTPException(
                    status_code=500, detail=f"Failed to delete file: {err}"
                )

    async def delete_multiple_files(
        self, object_names: list[str], bucket_name: str
    ) -> None:
        """
        Delete multiple files from the specified bucket.

        Args:
            object_names: List of object names to delete
            bucket_name: Source bucket name (defaults to images bucket)
        """
        await self._init_buckets()

        try:
            # Convert to list of DeleteObject as required by MinIO
            objects = list(object_names)
            errors = await self.client.remove_objects(bucket_name, objects)

            # Check for errors
            error_count = 0
            for error in errors:
                error_count += 1
                print(f"Error removing object {error.object_name}: {error.message}")

            if error_count > 0:
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to delete {error_count} of {len(object_names)} files",
                )
        except S3Error as err:
            raise HTTPException(
                status_code=500, detail=f"Failed to delete files: {err}"
            )

    async def list_files(
        self,
        bucket_name: str,
        prefix: str = "",
        recursive: bool = True,
        skip: int = 0,
        limit: int = 50,
        search: str | None = None,
    ) -> list[dict[str, str | int]]:
        """
        List objects in the specified bucket.

        Args:
            prefix: Prefix filter for object names
            recursive: Whether to list objects recursively
            bucket_name: Source bucket name (defaults to images bucket)

        Returns:
            List of objects with their metadata
        """
        await self._init_buckets()

        try:
            objects = []
            async for obj in self.client.list_objects(
                bucket_name, prefix=prefix, recursive=recursive
            ):
                objects.append(
                    {
                        "name": obj.object_name,
                        "size": obj.size,
                        "last_modified": obj.last_modified.isoformat()
                        if obj.last_modified
                        else None,
                    }
                )
            if search:
                objects = [obj for obj in objects if search in obj["name"]]
            if skip:
                objects = objects[skip:]
            if limit:
                objects = objects[:limit]
            return objects
        except S3Error as err:
            raise HTTPException(status_code=500, detail=f"Failed to list files: {err}")

    async def get_presigned_url(
        self,
        object_name: str,
        bucket_name: str,
        expires: timedelta | None = None,
        response_headers: dict[str, str] | None = None,
    ) -> str:
        """
        Generate a presigned URL for object download.

        Args:
            object_name: The name of the object
            bucket_name: Source bucket name (defaults to images bucket)
            expires: Expiration time in seconds
            response_headers: Additional response headers

        Returns:
            Presigned URL for downloading the object
        """
        await self._init_buckets()

        bucket_name = bucket_name or self.images_bucket

        try:
            url = await self.client.presigned_get_object(
                bucket_name,
                object_name,
                expires=expires,
                response_headers=response_headers,
            )
            return url
        except S3Error as err:
            raise HTTPException(
                status_code=500, detail=f"Failed to generate presigned URL: {err}"
            )

    async def get_presigned_put_url(
        self,
        object_name: str,
        bucket_name: str,
        expires: int = 3600,
        content_type: str | None = None,
    ) -> str:
        """
        Generate a presigned URL for object upload.

        Args:
            object_name: The name for the object to be uploaded
            bucket_name: Target bucket name (defaults to images bucket)
            expires: Expiration time in seconds
            content_type: Content type for the upload (not used in MinIO implementation)

        Returns:
            Presigned URL for uploading to the object
        """
        await self._init_buckets()

        try:
            url = await self.client.presigned_put_object(
                bucket_name, object_name, expires=expires
            )
            return url
        except S3Error as err:
            raise HTTPException(
                status_code=500, detail=f"Failed to generate presigned PUT URL: {err}"
            )

    async def copy_object(
        self,
        source_bucket_name: str,
        source_object_name: str,
        dest_bucket_name: str,
        dest_object_name: str | None = None,
        metadata: dict[str, str] | None = None,
    ) -> None:
        """
        Copy an object from source to destination.

        Args:
            source_bucket_name: Source bucket name
            source_object_name: Source object name
            dest_bucket_name: Destination bucket name
            dest_object_name: Destination object name (defaults to source name)
            metadata: Optional metadata to attach to the destination object
        """
        await self._init_buckets()

        # Use source object name as destination if not specified
        dest_object_name = dest_object_name or source_object_name

        try:
            # Copy object
            await self.client.copy_object(
                dest_bucket_name,
                dest_object_name,
                f"{source_bucket_name}/{source_object_name}",
                metadata=metadata,
            )
        except S3Error as err:
            raise HTTPException(status_code=500, detail=f"Failed to copy object: {err}")

    async def get_object_metadata(
        self, object_name: str, bucket_name: str
    ) -> dict[str, str]:
        """
        Get object metadata.

        Args:
            object_name: The name of the object
            bucket_name: Source bucket name (defaults to images bucket)

        Returns:
            Object metadata
        """
        await self._init_buckets()

        bucket_name = bucket_name or self.images_bucket

        try:
            stat = await self.client.stat_object(bucket_name, object_name)
            return stat.metadata
        except S3Error as err:
            if err.code == "NoSuchKey":
                raise HTTPException(
                    status_code=404,
                    detail=f"Object {object_name} not found in bucket {bucket_name}",
                )
            raise HTTPException(
                status_code=500, detail=f"Failed to get object metadata: {err}"
            )

    async def file_exists(self, object_name: str, bucket_name: str) -> bool:
        """
        Check if a file exists in the bucket.

        Args:
            object_name: The name of the object to check
            bucket_name: Source bucket name (defaults to images bucket)

        Returns:
            True if the file exists, False otherwise
        """
        await self._init_buckets()

        try:
            await self.client.stat_object(bucket_name, object_name)
            return True
        except S3Error as err:
            if err.code == "NoSuchKey":
                return False
            raise HTTPException(
                status_code=500, detail=f"Failed to check if file exists: {err}"
            )
