import io
from abc import ABC, abstractmethod
from collections.abc import Async<PERSON>enerator
from datetime import timed<PERSON><PERSON>

from fastapi import UploadFile


class BaseStorageRepository(ABC):
    @abstractmethod
    async def upload_file(
        self,
        file: UploadFile,
        prefix: str,
        bucket_name: str,
        metadata: dict[str, str] | None = None,
    ) -> dict:
        pass

    @abstractmethod
    async def upload_files(
        self,
        files: list[UploadFile],
        prefix: str,
        bucket_name: str,
    ) -> list[dict[str, str]]:
        pass

    @abstractmethod
    async def upload_bytes(
        self,
        data: bytes,
        bucket_name: str,
        object_name: str | None = None,
        content_type: str = "application/octet-stream",
        metadata: dict[str, str] | None = None,
    ) -> str:
        pass

    @abstractmethod
    async def get_file(
        self,
        object_name: str,
        bucket_name: str,
    ) -> tuple[io.BytesIO, int, str, dict[str, str]]:
        pass

    @abstractmethod
    async def stream_file(
        self, object_name: str, bucket_name: str
    ) -> AsyncGenerator[bytes, None]:
        pass

    @abstractmethod
    async def download_file(
        self, object_name: str, file_path: str, bucket_name: str
    ) -> None:
        pass

    @abstractmethod
    async def delete_file(self, object_name: str, bucket_name: str) -> None:
        pass

    @abstractmethod
    async def delete_multiple_files(
        self, object_names: list[str], bucket_name: str
    ) -> None:
        pass

    @abstractmethod
    async def list_files(
        self,
        bucket_name: str,
        prefix: str = "",
        recursive: bool = True,
        skip: int = 0,
        limit: int = 50,
        search: str | None = None,
    ) -> list[dict[str, str | int]]:
        pass

    @abstractmethod
    async def get_presigned_url(
        self,
        object_name: str,
        bucket_name: str,
        expires: timedelta | None = None,
        response_headers: dict[str, str] | None = None,
    ) -> str:
        pass

    @abstractmethod
    async def get_presigned_put_url(
        self,
        object_name: str,
        bucket_name: str,
        expires: int = 3600,
        content_type: str | None = None,
    ) -> str:
        pass

    @abstractmethod
    async def copy_object(
        self,
        source_bucket_name: str,
        source_object_name: str,
        dest_bucket_name: str,
        dest_object_name: str | None = None,
        metadata: dict[str, str] | None = None,
    ) -> None:
        pass

    @abstractmethod
    async def get_object_metadata(
        self, object_name: str, bucket_name: str
    ) -> dict[str, str]:
        pass

    @abstractmethod
    async def file_exists(self, object_name: str, bucket_name: str) -> bool:
        pass
