import uuid
from uuid import <PERSON><PERSON><PERSON>

from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from sqlalchemy import or_
from sqlalchemy.orm import selectinload
from sqlmodel import Session, col, desc, not_, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.db import engine
from app.models import (
    AWSAccount,
    AWSAccountCredentials,
    CloudProvider,
    GCPAccount,
    User,
    UserWorkspace,
    Workspace,
)


class WorkspaceRepository:
    def __init__(self, async_session: AsyncSession):
        self.async_session = async_session
        self.session = Session(engine)

    def can_create_workspaces(self, user_id: UUID, default_workspace: bool, is_on_onboarding: bool) -> bool:
        """
        Check if a user can manage (create/update/delete) workspaces.
        Only superusers and users with own workspaces can manage workspaces.
        """
        user = self.session.get(User, user_id)
        return (
            user.is_superuser or len(user.own_workspaces or []) > 0 or default_workspace or is_on_onboarding
            if user
            else False
        )

    def can_manage_workspace(self, user_id: UUID, workspace_id: UUID) -> bool:
        """
        Check if a user can manage a workspace.
        Only the workspace owner and superusers can manage workspaces.
        """
        user = self.session.get(User, user_id)
        workspace = self.session.get(Workspace, workspace_id)
        if not workspace:
            return False
        return user.is_superuser or user.id == workspace.owner_id if user else False

    def can_view_workspace(self, user_id: UUID, workspace_id: UUID) -> bool:
        """
        Check if a user can view a workspace.
        A user can view a workspace if they are:
        1. A superuser
        2. The workspace owner
        3. An invited member of the workspace
        """
        user = self.session.get(User, user_id)
        if not user:
            return False

        if user.is_superuser:
            return True

        workspace = self.session.get(Workspace, workspace_id)
        if not workspace:
            return False

        # Check if user is owner
        if workspace.owner_id == user_id:
            return True

        # Check if user is invited member
        statement = select(UserWorkspace).where(
            UserWorkspace.workspace_id == workspace_id, UserWorkspace.user_id == user_id
        )
        result = self.session.exec(statement)
        return result.first() is not None

    async def get_workspaces(
        self, user_id: UUID, skip: int = 0, limit: int = 10
    ) -> list[Workspace]:
        """
        Retrieve workspaces - both owned and invited (non-deleted only).
        """
        statement = (
            select(Workspace)
            .distinct()
            .where(
                or_(
                    col(Workspace.owner_id) == user_id,  # Owned workspaces
                    col(Workspace.id).in_(  # Invited workspaces
                        select(UserWorkspace.workspace_id).where(
                            UserWorkspace.user_id == user_id
                        )
                    ),
                ),
                not_(Workspace.is_deleted),
            )
            .offset(skip)
            .limit(limit)
            .order_by(desc(Workspace.created_at))
        )

        result = await self.async_session.exec(statement)
        workspaces = result.all()
        return list(workspaces)

    async def get_workspace(self, workspace_id: uuid.UUID) -> Workspace | None:
        statement = (
            select(Workspace)
            .where(Workspace.id == workspace_id)
            .options(
                selectinload(Workspace.aws_account).selectinload(AWSAccount.credential),
                selectinload(Workspace.gcp_account).selectinload(GCPAccount.credential),
                selectinload(Workspace.settings),
            )
        )
        result = await self.async_session.exec(statement)
        workspace = result.first()

        return workspace

    async def create_workspace(self, workspace: Workspace) -> Workspace:
        return await self._save_workspace(workspace)

    async def update_workspace(self, workspace: Workspace) -> Workspace:
        return await self._save_workspace(workspace)

    async def _save_workspace(self, workspace: Workspace) -> Workspace:
        """
        Private helper method to save (create or update) a workspace.
        """
        try:
            self.async_session.add(workspace)
            await self.async_session.commit()
            await self.async_session.refresh(workspace)
            return workspace
        except Exception as e:
            await self.async_session.rollback()
            raise e

    async def delete_workspace(self, workspace: Workspace) -> None:
        """
        Delete a workspace.
        """
        try:
            await self.async_session.delete(workspace)
            await self.async_session.commit()
        except Exception as e:
            await self.async_session.rollback()
            raise e

    def get_all_workspaces(
        self, workspace_ids: list[UUID] | None = None
    ) -> list[Workspace]:
        """
        Get all workspaces, optionally filtered by workspace IDs.

        Args:
            workspace_ids: Optional list of workspace IDs to filter by

        Returns:
            List of Workspace objects matching the criteria
        """
        statement = select(Workspace)
        if workspace_ids:
            statement = statement.where(col(Workspace.id).in_(workspace_ids))
        result = self.session.exec(statement)
        workspaces = result.all()
        return list(workspaces)

    def get_aws_credentials(
        self, aws_account_id: uuid.UUID
    ) -> AWSAccountCredentials | None:
        statement = select(AWSAccountCredentials).where(
            AWSAccountCredentials.aws_account_id == aws_account_id
        )
        result = self.session.exec(statement)
        return result.first()
