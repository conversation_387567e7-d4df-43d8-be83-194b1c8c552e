import json
from uuid import U<PERSON><PERSON>

from sqlalchemy import func
from sqlalchemy.orm import selectinload
from sqlmodel import Session, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.exceptions.conversation_exceptions import (
    ConversationAccessDenied,
    ConversationNotFound,
    ConversationRepositoryError,
    ConversationShareNotFound,
)
from app.logger import logger
from app.models import (
    Agent,
    Conversation,
    Message,
    MessageAgentThoughtPublic,
    MessageAttachmentPublic,
    MessageCheckpoint,
    MessageDisplayComponentPublic,
    MessagePublic,
    MessagePublicList,
)
from app.models.dashboards import Dashboard
from app.models.reports import Report


class ConversationRepository:
    def __init__(
        self, async_session: AsyncSession | None = None, session: Session | None = None
    ):
        self.async_session = async_session
        self.session = session

    async def check_conversation_permission(
        self,
        agent_id: UUID,
        workspace_id: UUID,
    ) -> bool:
        """Check if user has permission to create conversation in workspace given agent_id and workspace_id."""
        agent = await self.async_session.get(Agent, agent_id)

        if agent.workspace_id != workspace_id:
            raise ConversationAccessDenied(
                conversation_id=agent_id, user_id=workspace_id
            )

        return True

    def get_conversation(self, conversation_id: UUID) -> Conversation | None:
        return self.session.get(Conversation, conversation_id)

    async def async_get_conversation(self, conversation_id: UUID) -> Conversation:
        query = (
            select(Conversation)
            .where(Conversation.id == conversation_id)
            .options(
                selectinload(Conversation.agent), selectinload(Conversation.resource)
            )
        )
        result = await self.async_session.exec(query)
        return result.first()

    async def get_conversations(
        self,
        agent_id: UUID,
        workspace_id: UUID,
        resource_id: UUID | None = None,
        search: str | None = None,
        skip: int | None = None,
        limit: int | None = None,
    ) -> tuple[list[Conversation], int]:
        """Get list of conversations with filtering and pagination."""
        try:
            await self.check_conversation_permission(agent_id, workspace_id)

            query = (
                select(Conversation)
                .where(Conversation.is_deleted == False)
                .where(Conversation.agent_id == agent_id)
            )

            if resource_id:
                query = query.where(Conversation.resource_id == resource_id)

            if search:
                query = query.where(Conversation.name.ilike(f"%{search}%"))

            count_query = select(func.count()).select_from(query.subquery())
            total_count = await self.async_session.exec(count_query)
            total_count = total_count.first()

            query = query.order_by(Conversation.created_at.desc())
            if skip:
                query = query.offset(skip)
            if limit:
                query = query.limit(limit)
            if resource_id:
                query = query.options(selectinload(Conversation.resource))

            result = await self.async_session.exec(query)
            conversations = result.all()

            return conversations, total_count
        except Exception as e:
            logger.exception(f"Failed to get conversations: {e}")
            raise ConversationRepositoryError(detail="Failed to get conversations")

    async def get_conversation_history(
        self,
        conversation_id: UUID,
        workspace_id: UUID,
        skip: int = 0,
        limit: int = 100,
    ) -> MessagePublicList:
        """Get conversation history with pagination."""
        try:
            conversation = await self.async_get_conversation(conversation_id)
            if not conversation:
                raise ConversationNotFound(conversation_id=conversation_id)

            await self.check_conversation_permission(
                conversation.agent_id, workspace_id
            )

            # Query with proper eager loading for MessagePublic schema
            query = (
                select(Message)
                .where(Message.conversation_id == conversation_id)
                .where(Message.is_deleted == False)
                .options(
                    selectinload(Message.thoughts),
                    selectinload(Message.display_components),
                    selectinload(Message.attachments),
                )
            )

            count_query = select(func.count()).select_from(
                select(Message)
                .where(
                    Message.conversation_id == conversation_id,
                    Message.is_deleted == False,
                )
                .subquery()
            )
            total_count = await self.async_session.exec(count_query)
            total_count = total_count.first()

            query = query.order_by(Message.created_at.asc())
            if skip:
                query = query.offset(skip)
            if limit:
                query = query.limit(limit)

            result = await self.async_session.exec(query)
            messages = result.all()

            # Convert Message objects to MessagePublic schema
            message_publics = []
            for message in messages:
                thoughts = []
                if message.thoughts:
                    for thought in sorted(message.thoughts, key=lambda x: x.position):
                        try:
                            tool_output = json.loads(thought.tool_output)
                        except Exception:
                            tool_output = thought.tool_output

                        tool_output = (
                            json.dumps(tool_output)
                            if isinstance(tool_output, dict)
                            else str(tool_output)
                        )
                        thoughts.append(
                            MessageAgentThoughtPublic(
                                id=thought.id,
                                position=thought.position,
                                tool_name=thought.tool_name,
                                tool_input=thought.tool_input,
                                tool_output=tool_output,
                                content=thought.content,
                                created_at=thought.created_at,
                            )
                        )

                message_public = MessagePublic(
                    id=message.id,
                    content=message.content,
                    role=message.role,
                    is_interrupt=message.is_interrupt,
                    interrupt_message=message.interrupt_message,
                    thoughts=thoughts,
                    display_components=[
                        MessageDisplayComponentPublic(
                            id=comp.id,
                            type=comp.type,
                            chart_type=comp.chart_type,
                            title=comp.title,
                            description=comp.description,
                            data=comp.data,
                            config=comp.config,
                            position=comp.position,
                            created_at=comp.created_at,
                        )
                        for comp in message.display_components
                    ]
                    if message.display_components
                    else None,
                    attachments=[
                        MessageAttachmentPublic(
                            id=att.id,
                            filename=att.filename,
                            original_filename=att.original_filename,
                            file_type=att.file_type,
                            file_size=att.file_size,
                            storage_key=att.storage_key,
                            thumbnail_key=att.thumbnail_key,
                            created_at=att.created_at,
                        )
                        for att in message.attachments
                    ]
                    if message.attachments
                    else None,
                    created_at=message.created_at,
                )
                message_publics.append(message_public)

            # Check for reports and dashboards
            has_report = await self._check_conversation_has_report(conversation_id)
            has_dashboard = await self._check_conversation_has_dashboard(
                conversation_id
            )

            return MessagePublicList(
                messages=message_publics,
                resource_id=conversation.resource_id,
                has_report=has_report,
                has_dashboard=has_dashboard,
                total=total_count or 0,
            )

        except Exception as e:
            logger.exception(f"Failed to get conversation history: {e}")
            raise ConversationRepositoryError(
                detail="Failed to get conversation history"
            )

    async def create_conversation(
        self,
        agent_id: UUID,
        workspace_id: UUID,
        resource_id: UUID | None = None,
    ) -> Conversation:
        """Create a new conversation."""
        try:
            await self.check_conversation_permission(agent_id, workspace_id)

            conversation = Conversation(agent_id=agent_id, resource_id=resource_id)

            self.async_session.add(conversation)
            await self.async_session.commit()
            await self.async_session.refresh(conversation)
            return conversation
        except Exception as e:
            await self.async_session.rollback()
            logger.exception(f"Failed to create conversation: {e}")
            raise ConversationRepositoryError(detail="Failed to create conversation")

    async def rename_conversation(
        self,
        conversation_id: UUID,
        workspace_id: UUID,
        name: str,
    ) -> Conversation:
        try:
            conversation = await self.async_get_conversation(conversation_id)
            if not conversation:
                raise ConversationNotFound(conversation_id=conversation_id)

            await self.check_conversation_permission(
                conversation.agent_id, workspace_id
            )

            conversation.name = name
            self.async_session.add(conversation)
            await self.async_session.commit()
            await self.async_session.refresh(conversation)

            return conversation
        except Exception as e:
            await self.async_session.rollback()
            logger.exception(f"Failed to rename conversation: {e}")
            raise ConversationRepositoryError(detail="Failed to rename conversation")

    async def delete_conversation(
        self,
        workspace_id: UUID,
        conversation_id: UUID,
    ) -> None:
        try:
            conversation = await self.async_get_conversation(conversation_id)
            if not conversation:
                raise ConversationNotFound(conversation_id=conversation_id)

            await self.check_conversation_permission(
                conversation.agent_id, workspace_id
            )

            conversation.is_deleted = True
            self.async_session.add(conversation)
            await self.async_session.commit()
        except Exception as e:
            await self.async_session.rollback()
            logger.exception(f"Failed to delete conversation: {e}")
            raise ConversationRepositoryError(detail="Failed to delete conversation")

    async def get_conversation_by_share_id(self, share_id: UUID) -> Conversation:
        """Get conversation by share ID."""
        try:
            query = select(Conversation).filter(
                Conversation.share_id == share_id,
                Conversation.is_shared == True,
                Conversation.is_deleted == False,
            )
            result = await self.async_session.exec(query)
            conversation = result.first()
            if not conversation:
                raise ConversationShareNotFound(share_id=share_id)
            return conversation
        except Exception as e:
            logger.exception(f"Failed to get conversation by share ID: {e}")
            raise ConversationRepositoryError(
                detail="Failed to get conversation by share ID"
            )

    async def get_last_assistant_message(self, conversation_id: UUID) -> Message | None:
        query = (
            select(Message)
            .where(Message.conversation_id == conversation_id)
            .where(Message.is_deleted == False)
            .where(Message.role != "user")
            .order_by(Message.created_at.desc())
            .limit(1)
        )
        result = await self.async_session.exec(query)
        return result.first()

    async def get_checkpoint_by_message_id(
        self, message_id: UUID
    ) -> MessageCheckpoint | None:
        query = select(MessageCheckpoint).where(
            MessageCheckpoint.message_id == message_id
        )
        result = await self.async_session.exec(query)
        return result.first()

    async def get_latest_user_message(self, conversation_id: UUID) -> Message | None:
        query = (
            select(Message)
            .where(Message.is_deleted == False)
            .where(Message.conversation_id == conversation_id)
            .where(Message.role == "user")
            .order_by(Message.created_at.desc())
            .limit(1)
        )
        result = await self.async_session.exec(query)
        return result.first()

    async def attach_resource_to_conversation(
        self, conversation_id: UUID, resource_id: UUID
    ) -> Conversation:
        """Attach a resource to a conversation if it doesn't already have one."""
        conversation = await self.async_get_conversation(conversation_id)
        if not conversation:
            raise ValueError("Conversation not found")

        # Only attach resource if conversation doesn't already have one
        if conversation.resource_id is None:
            conversation.resource_id = resource_id
            self.async_session.add(conversation)
            await self.async_session.commit()
            await self.async_session.refresh(conversation)

        return conversation

    async def _check_conversation_has_report(self, conversation_id: UUID) -> bool:
        """Check if there are reports for the given conversation_id."""
        report_query = select(Report).where(Report.conversation_id == conversation_id)
        report_result = await self.async_session.exec(report_query)
        return report_result.first() is not None

    async def _check_conversation_has_dashboard(self, conversation_id: UUID) -> bool:
        """Check if there are dashboards for the given conversation_id."""
        dashboard_query = select(Dashboard).where(
            Dashboard.conversation_id == conversation_id
        )
        dashboard_result = await self.async_session.exec(dashboard_query)
        return dashboard_result.first() is not None
