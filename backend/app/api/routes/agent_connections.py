import uuid

from fastapi import <PERSON><PERSON><PERSON><PERSON>, HTTPException, status

from app.api.deps import CurrentUser, SessionAsyncDep
from app.exceptions.legacy import BaseAppException
from app.logger import logger
from app.schemas.agent_connections import (
    AgentConnectionCreateRequest,
    AgentConnectionResponse,
    AgentsConnectionsResponse,
)
from app.services import AgentService
from app.services.agent_connection_service import AgentConnectionService

router = APIRouter()


@router.get("/", response_model=AgentsConnectionsResponse)
async def get_workspace_agent_connections(
    session: SessionAsyncDep,
    current_user: CurrentUser,
) -> AgentsConnectionsResponse:
    """Get connections for all agents in the current workspace"""
    try:
        agents = await AgentService(session).get_agents(
            workspace_id=current_user.current_workspace_id
        )
        agent_connections = await AgentConnectionService(
            session
        ).get_agents_connections(agent_ids=[agent.id for agent in agents])
        return AgentsConnectionsResponse(agents_connections=agent_connections)
    except BaseAppException as e:
        logger.exception(f"Error getting agent connections. {e}")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.message,
        )


@router.post("/{agent_id}/connections", response_model=AgentConnectionResponse)
async def create_agent_connection(
    current_user: CurrentUser,
    agent_id: uuid.UUID,
    session: SessionAsyncDep,
    request: AgentConnectionCreateRequest,
) -> AgentConnectionResponse:
    """Create a connection for an agent"""
    try:
        # Validate agent access
        agent = await AgentService(session).get_agent(agent_id)
        if agent.workspace_id != current_user.current_workspace_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied to agent"
            )

        # Create connection
        await AgentConnectionService(session).create_agent_connection(
            workspace_id=current_user.current_workspace_id,
            agent_id=agent_id,
            conn_id=request.connection_id,
        )

        return AgentConnectionResponse(
            success=True,
            message="Connection created successfully",
        )
    except BaseAppException as e:
        logger.exception(f"Error creating agent connection. {e}")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.message,
        )


@router.delete(
    "/{agent_id}/connections/{connection_id}", response_model=AgentConnectionResponse
)
async def delete_agent_connection(
    current_user: CurrentUser,
    agent_id: uuid.UUID,
    connection_id: uuid.UUID,
    session: SessionAsyncDep,
) -> AgentConnectionResponse:
    """Delete a connection from an agent"""
    try:
        # Validate agent access
        agent = await AgentService(session).get_agent(agent_id)
        if agent.workspace_id != current_user.current_workspace_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied to agent"
            )

        # Delete connection
        await AgentConnectionService(session).delete_agent_connection(
            workspace_id=current_user.current_workspace_id,
            agent_id=agent_id,
            conn_id=connection_id,
        )

        return AgentConnectionResponse(
            success=True,
            message="Connection deleted successfully",
        )
    except BaseAppException as e:
        logger.exception(f"Error deleting agent connection. {e}")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.message,
        )
