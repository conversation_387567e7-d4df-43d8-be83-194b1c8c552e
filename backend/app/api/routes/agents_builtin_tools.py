import uuid

from fastapi import APIRouter, HTTPException, status

from app.api.deps import CurrentUser, SessionAsyncDep
from app.exceptions.legacy import BaseAppException
from app.logger import logger
from app.schemas.agents_builtin_tools import (
    AgentBuiltInToolsBulkUpdateResponse,
    AgentBuiltInToolsUpdateRequest,
    AgentsBuiltInToolsResponse,
)
from app.services import AgentBuiltinToolService, AgentService

router = APIRouter()


@router.get("/", response_model=AgentsBuiltInToolsResponse)
async def get_workspace_agent_builtin_tools(
    session: SessionAsyncDep,
    current_user: CurrentUser,
) -> AgentsBuiltInToolsResponse:
    """Get built-in tools for all agents in the current workspace"""
    try:
        agents = await AgentService(session).get_agents(
            workspace_id=current_user.current_workspace_id
        )
        agent_builtin_tools = await AgentBuiltinToolService(
            session
        ).get_agents_builtin_tools(agent_ids=[agent.id for agent in agents])
        return AgentsBuiltInToolsResponse(agents_builtin_tools=agent_builtin_tools)
    except BaseAppException as e:
        logger.exception(f"Error getting agent builtin tools. {e}")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.message,
        )


@router.patch("/{agent_id}", response_model=AgentBuiltInToolsBulkUpdateResponse)
async def bulk_update_agent_builtin_tools(
    current_user: CurrentUser,
    agent_id: uuid.UUID,
    session: SessionAsyncDep,
    agent_builtin_tools_update: AgentBuiltInToolsUpdateRequest,
) -> AgentBuiltInToolsBulkUpdateResponse:
    """Bulk update builtin tools for an agent"""
    try:
        # Validate agent access
        agent = await AgentService(session).get_agent(agent_id)
        if agent.workspace_id != current_user.current_workspace_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied to agent"
            )

        # Perform bulk update
        results = await AgentBuiltinToolService(
            session
        ).bulk_update_agent_builtin_tools(
            workspace_id=current_user.current_workspace_id,
            agent_id=agent_id,
            updates=agent_builtin_tools_update.agent_builtin_tools,
        )

        return results
    except BaseAppException as e:
        logger.exception(f"Error bulk updating agent builtin tools. {e}")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.message,
        )
