import datetime
import secrets

from fastapi import <PERSON><PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi_sso.sso.base import Open<PERSON>
from fastapi_sso.sso.google import GoogleSSO
from sqlmodel import select

from app import crud
from app.api.deps import PaymentServiceDep, SessionDep
from app.core import security
from app.core.config import settings
from app.logger import logger
from app.models import Token, UserCreate, UserWorkspace, Workspace
from app.tasks.user_onboarding_tasks import send_welcome_email

router = APIRouter()

google_sso = GoogleSSO(
    settings.GOOGLE_CLIENT_ID,
    settings.GOOGLE_CLIENT_SECRET,
    settings.GOOGLE_LOGIN_CALLBACK,
    allow_insecure_http=True,
)


@router.get("/login")
async def google_login(request: Request):
    """Initiate Google OAuth login flow"""
    try:
        # Get Slack OAuth parameters
        slack_oauth = request.query_params.get("slack_oauth", "false").lower() == "true"
        app_id = request.query_params.get("app_id", "")
        team_id = request.query_params.get("team_id", "")

        # Create state parameter with Slack OAuth info if needed
        state = ""
        if slack_oauth:
            state = f"slack_oauth:app_id={app_id}:team_id={team_id}"

        async with google_sso:
            return await google_sso.get_login_redirect(state=state)
    except Exception:
        logger.exception("Error during Google login initiation.")
        raise HTTPException(
            status_code=500, detail="An unexpected error occurred during login."
        )


@router.get("/callback")
async def google_callback(
    request: Request, session: SessionDep, payment_service: PaymentServiceDep
) -> Token:
    """Handle Google OAuth callback and login/create user"""
    try:
        # Parse the state parameter for Slack OAuth info
        state = request.query_params.get("state", "")
        slack_oauth = False
        app_id = ""
        team_id = ""

        logger.info(f"state: {state}")

        if state.startswith("slack_oauth:"):
            try:
                # Parse the state parameter format: slack_oauth:app_id={app_id}:team_id={team_id}
                parts = state.split(":")
                app_id = parts[1].split("=")[1]
                team_id = parts[2].split("=")[1]
                slack_oauth = True
            except (IndexError, ValueError):
                # If parsing fails, treat as normal login
                pass

        async with google_sso:
            google_user: OpenID | None = await google_sso.verify_and_process(request)

        if not google_user or not google_user.email:
            raise HTTPException(status_code=400, detail="Email not provided by Google")

        # Look up user by email
        user = crud.get_user_by_email(session=session, email=google_user.email)

        first_workspace_id = None
        is_first_login = False

        # Create new user if not found
        if not user:
            user = crud.create_user(
                session=session,
                user_create=UserCreate(
                    email=google_user.email,
                    password=secrets.token_urlsafe(30),
                    is_active=True,
                    full_name=google_user.display_name or google_user.email,
                    avatar_url=google_user.picture,
                ),
            )

            # Send welcome email asynchronously for new users
            send_welcome_email.delay(user.email, user.full_name) # type: ignore

            # Create default workspace for new user
            # async for async_session in get_async_session():
            #     service = UserService(session, async_session)
            #     workspace = await service.onboard_new_user(user.id)
            #     first_workspace_id = workspace.id
            #     is_first_login = True

            # NOTE: Migrate to onboarding workflow
            first_workspace_id = None
            is_first_login = True
        else:
            # Get existing workspace info
            own_workspaces = session.exec(
                select(Workspace).where(Workspace.owner_id == user.id)
            ).all()
            workspaces = session.exec(
                select(UserWorkspace).where(UserWorkspace.user_id == user.id)
            ).all()

            if len(own_workspaces) > 0:
                is_default_workspace = False
                for workspace in own_workspaces:
                    if workspace.is_default:
                        first_workspace_id = workspace.id
                        is_default_workspace = True
                        break
                if not is_default_workspace:
                    first_workspace_id = own_workspaces[0].id
            elif len(workspaces) > 0:
                first_workspace_id = workspaces[0].workspace_id

            if not user.last_login_time:
                is_first_login = True

        # Update last login time and avatar URL
        user.last_login_time = datetime.datetime.now()

        # Update avatar URL if available from Google
        if google_user.picture:
            user.avatar_url = google_user.picture

        session.add(user)
        session.commit()

        # check and create customer in payment service if there is no customer
        if payment_service:
            exists = await payment_service.get_customer_by_user_id(user.id)
            if not exists:
                await payment_service.create_customer_for_user(user.id, user.email)

        # Generate access token
        access_token_expires = datetime.timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )

        # If this is part of Slack OAuth flow, include the necessary info in the token
        if slack_oauth and app_id and team_id:
            return Token(
                access_token=security.create_access_token(
                    subject=user.id,
                    payload={
                        "workspace_id": str(first_workspace_id)
                        if first_workspace_id
                        else None,
                    },
                    expires_delta=access_token_expires,
                ),
                workspace_id=first_workspace_id,
                is_first_login=is_first_login,
                slack_oauth=True,
                app_id=app_id,
                team_id=team_id,
            )

        return Token(
            access_token=security.create_access_token(
                subject=user.id,
                payload={
                    "workspace_id": str(first_workspace_id)
                    if first_workspace_id
                    else None,
                },
                expires_delta=access_token_expires,
            ),
            workspace_id=first_workspace_id,
            is_first_login=is_first_login,
        )
    except Exception as e:
        logger.exception(f"Error during Google callback. {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred during Google callback.",
        )
