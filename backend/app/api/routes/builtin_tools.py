from uuid import UUID

from fastapi import APIRouter, HTTPException, status

from app.api.deps import CurrentUser, SessionAsyncDep
from app.exceptions.legacy import BaseAppException
from app.logger import logger
from app.schemas.builtin_tools import (
    BuiltInToolUpdateRequest,
    WorkspaceBuiltInToolResponse,
)
from app.services import BuiltInToolService

router = APIRouter()


@router.get("/", response_model=list[WorkspaceBuiltInToolResponse])
async def get_workspace_builtin_tools(
    session: SessionAsyncDep,
    current_user: CurrentUser,
) -> list[WorkspaceBuiltInToolResponse]:
    """List all built-in tools for a workspace"""
    try:
        builtin_tools = await BuiltInToolService(session).get_workspace_builtin_tools(
            workspace_id=current_user.current_workspace_id,
        )
        return builtin_tools
    except BaseAppException as e:
        logger.exception(f"Error listing builtin tools. {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list builtin tools.",
        )


@router.patch("/{workspace_builtin_tool_id}/", response_model=bool)
async def update_workspace_builtin_tool(
    current_user: CurrentUser,
    workspace_builtin_tool_id: UUID,
    session: SessionAsyncDep,
    builtin_tool_update: BuiltInToolUpdateRequest,
) -> bool:
    """Update the permission requirement of a built-in tool for a workspace"""
    try:
        updated_status = await BuiltInToolService(
            session
        ).update_workspace_builtin_tool(
            workspace_id=current_user.current_workspace_id,
            workspace_builtin_tool_id=workspace_builtin_tool_id,
            required_permission=builtin_tool_update.required_permission,
        )
        if updated_status is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Built-in tool not found.",
            )
        return updated_status
    except BaseAppException as e:
        logger.exception(f"Error updating builtin tool. {e}")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.message,
        )
