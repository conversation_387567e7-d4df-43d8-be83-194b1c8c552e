import uuid
from datetime import <PERSON><PERSON><PERSON>
from typing import Any

from fastapi import APIRouter, HTTPException
from sqlmodel import func, select

from app import crud
from app.api.deps import CurrentUser, SessionDep
from app.core.config import settings
from app.core.security import create_access_token, get_password_hash, verify_password
from app.logger import logger
from app.models import (
    Message,
    Token,
    UpdatePassword,
    User,
    UserCreate,
    UserDetail,
    UserPublic,
    UsersPublic,
    UserUpdate,
    UserUpdateMe,
    UserWorkspace,
    Workspace,
)
from app.utils import (
    generate_new_account_email,
    is_allowed_access_to_workspace,
    is_user_belong_to_workspaces,
    send_email,
)

router = APIRouter()


@router.get(
    "/",
    response_model=UsersPublic,
)
def read_users(
    session: SessionDep, current_user: CurrentUser, skip: int = 0, limit: int = 100
) -> Any:
    """
    Retrieve users based on workspace relationship.
    Only returns users that belong to the current user's active workspace.
    """
    try:
        if not current_user.current_workspace_id:
            raise HTTPException(
                status_code=400,
                detail="No active workspace selected",
            )

        # Get users that belong to the current workspace through UserWorkspace relationship
        statement = (
            select(User)
            .join(UserWorkspace)
            .where(UserWorkspace.workspace_id == current_user.current_workspace_id)
            .offset(skip)
            .limit(limit)
        )
        users = session.exec(statement).all()

        # Get total count of users in workspace
        count_statement = (
            select(func.count())
            .select_from(User)
            .join(UserWorkspace)
            .where(UserWorkspace.workspace_id == current_user.current_workspace_id)
        )
        count = session.exec(count_statement).one()

        return UsersPublic(data=users, count=count)
    except HTTPException as e:
        raise e
    except Exception:
        logger.exception("Error reading users.")
        raise HTTPException(
            status_code=500, detail="An unexpected error occurred while reading users."
        )


@router.post("/", response_model=UserPublic)
def create_user(
    *, session: SessionDep, current_user: CurrentUser, user_in: UserCreate
) -> User:
    """
    Create new user.
    """
    try:
        user = crud.get_user_by_email(session=session, email=user_in.email)
        if user:
            raise HTTPException(
                status_code=400,
                detail="The user with this email already exists in the system.",
            )

        # create user entity first
        user = crud.create_user(session=session, user_create=user_in)

        # create user - workspace relationship
        user_workspace_create = UserWorkspace(
            user_id=user.id,
            workspace_id=current_user.current_workspace_id,
        )
        session.add(user_workspace_create)
        session.commit()

        if settings.emails_enabled and user_in.email:
            email_data = generate_new_account_email(
                email_to=user_in.email,
                username=user_in.email,
                password=user_in.password,
            )
            send_email(
                email_to=user_in.email,
                subject=email_data.subject,
                html_content=email_data.html_content,
            )

        return user
    except HTTPException as e:
        raise e
    except Exception:
        logger.exception("Error creating user.")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while creating the user.",
        )


@router.patch("/me", response_model=UserPublic)
def update_user_me(
    *, session: SessionDep, user_in: UserUpdateMe, current_user: CurrentUser
) -> Any:
    """
    Update own user.
    """
    try:
        if user_in.email:
            existing_user = crud.get_user_by_email(session=session, email=user_in.email)
            if existing_user and existing_user.id != current_user.id:
                raise HTTPException(
                    status_code=409, detail="User with this email already exists"
                )
        user_data = user_in.model_dump(exclude_unset=True)
        current_user.sqlmodel_update(user_data)
        session.add(current_user)
        session.commit()
        session.refresh(current_user)
        return current_user
    except HTTPException as e:
        raise e
    except Exception:
        logger.exception("Error updating user me.")
        raise HTTPException(
            status_code=500, detail="An unexpected error occurred while updating user."
        )


@router.patch("/me/password", response_model=Message)
def update_password_me(
    *, session: SessionDep, body: UpdatePassword, current_user: CurrentUser
) -> Any:
    """
    Update own password.
    """
    try:
        if not verify_password(body.current_password, current_user.hashed_password):
            raise HTTPException(status_code=400, detail="Incorrect password")
        if body.current_password == body.new_password:
            raise HTTPException(
                status_code=400,
                detail="New password cannot be the same as the current one",
            )
        hashed_password = get_password_hash(body.new_password)
        current_user.hashed_password = hashed_password
        session.add(current_user)
        session.commit()
        return Message(message="Password updated successfully")
    except HTTPException as e:
        raise e
    except Exception:
        logger.exception(f"Error updating password for user {current_user.id}.")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while updating password.",
        )


@router.get("/me", response_model=UserDetail)
def read_user_me(session: SessionDep, current_user: CurrentUser) -> Any:
    """
    Get current user.
    """
    try:
        user = session.get(User, current_user.id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        workspace_ids = [u.workspace_id for u in user.workspaces]

        # Handle case where user has no workspaces
        if workspace_ids:
            workspaces = session.exec(
                select(Workspace).where(Workspace.id.in_(workspace_ids))
            ).all()
        else:
            workspaces = []

        return UserDetail(
            **user.model_dump(),
            own_workspaces=user.own_workspaces,
            workspaces=workspaces,
        )
    except HTTPException:
        raise
    except Exception:
        logger.exception("Error reading user me.")
        raise HTTPException(
            status_code=500, detail="An unexpected error occurred while reading user."
        )


@router.delete("/me", response_model=Message)
def delete_user_me(session: SessionDep, current_user: CurrentUser) -> Any:
    """
    Delete own user.
    """
    try:
        if current_user.is_superuser:
            raise HTTPException(
                status_code=403,
                detail="Super users are not allowed to delete themselves",
            )
        session.delete(current_user)
        session.commit()
        return Message(message="User deleted successfully")
    except HTTPException as e:
        raise e
    except Exception:
        logger.exception("Error deleting user me.")
        raise HTTPException(
            status_code=500, detail="An unexpected error occurred while deleting user."
        )


@router.get("/{user_id}", response_model=UserPublic)
def read_user_by_id(
    user_id: uuid.UUID, session: SessionDep, current_user: CurrentUser
) -> Any:
    """
    Get a specific user by id.
    """
    try:
        user = session.get(User, user_id)
        if user == current_user:
            return user
        if not current_user.is_superuser and not is_user_belong_to_workspaces(
            user, current_user.current_workspace_id
        ):
            raise HTTPException(
                status_code=403,
                detail="The user doesn't have enough privileges",
            )
        return user
    except HTTPException as e:
        raise e
    except Exception:
        logger.exception(f"Error reading user {user_id}.")
        raise HTTPException(
            status_code=500, detail="An unexpected error occurred while reading user."
        )


@router.patch(
    "/{user_id}",
    response_model=UserPublic,
)
def update_user(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    user_id: uuid.UUID,
    user_in: UserUpdate,
) -> Any:
    """
    Update a user.
    """
    try:
        db_user = session.get(User, user_id)
        if not db_user:
            raise HTTPException(
                status_code=404,
                detail="The user with this id does not exist in the system",
            )

        if not current_user.is_superuser and not is_user_belong_to_workspaces(
            db_user, current_user.current_workspace_id
        ):
            raise HTTPException(
                status_code=403,
                detail="You can't update this user due to insufficient permissions",
            )

        if user_in.email:
            existing_user = crud.get_user_by_email(session=session, email=user_in.email)
            if existing_user and existing_user.id != user_id:
                raise HTTPException(
                    status_code=409, detail="User with this email already exists"
                )

        db_user = crud.update_user(session=session, db_user=db_user, user_in=user_in)
        return db_user
    except HTTPException as e:
        raise e
    except Exception:
        logger.exception(f"Error updating user {user_id}.")
        raise HTTPException(
            status_code=500, detail="An unexpected error occurred while updating user."
        )


@router.delete("/{user_id}")
def delete_user(
    session: SessionDep, current_user: CurrentUser, user_id: uuid.UUID
) -> Message:
    """
    Delete a user.
    """
    try:
        user = session.get(User, user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        if user == current_user:
            raise HTTPException(
                status_code=403, detail="Users are not allowed to delete themselves"
            )

        if not current_user.is_superuser and not is_user_belong_to_workspaces(
            user, current_user.current_workspace_id
        ):
            raise HTTPException(
                status_code=403,
                detail="You can't update this user due to insufficient permissions",
            )

        session.delete(user)
        session.commit()
        return Message(message="User deleted successfully")
    except HTTPException as e:
        raise e
    except Exception:
        logger.exception(f"Error deleting user {user_id}.")
        raise HTTPException(
            status_code=500, detail="An unexpected error occurred while deleting user."
        )


@router.get("/switch-workspace/{workspace_id}", response_model=Token)
def switch_workspace(
    session: SessionDep, current_user: CurrentUser, workspace_id: uuid.UUID
) -> Message:
    """
    Allow user to get new token for a different workspace.
    """
    try:
        user = session.get(User, current_user.id)

        # Get the workspace and check if it's deleted
        workspace = session.get(Workspace, workspace_id)
        if not workspace:
            raise HTTPException(status_code=404, detail="Workspace not found")
        if workspace.is_deleted:
            raise HTTPException(
                status_code=400, detail="Cannot switch to a deleted workspace"
            )

        if not is_allowed_access_to_workspace(user, workspace_id):
            raise HTTPException(status_code=403, detail="Unauthorized workspace access")

        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        return Token(
            access_token=create_access_token(
                subject=user.id,
                payload={"workspace_id": str(workspace_id)},
                expires_delta=access_token_expires,
            ),
            workspace_id=workspace_id,
            is_first_login=False,
        )
    except HTTPException as e:
        raise e
    except Exception:
        logger.exception(f"Error switching workspace for user {current_user.id}.")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while switching workspace.",
        )
