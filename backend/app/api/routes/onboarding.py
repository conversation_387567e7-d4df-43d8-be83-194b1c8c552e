
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from sqlmodel.ext.asyncio.session import AsyncSession

from app.api.deps import CurrentUser, get_async_session
from app.logger import logger
from app.models import (
    CloudProvider,
    WorkspaceCreate,
)
from app.models.workspaces import WorkspacePublic
from app.schemas.onboarding import (
    HARDCODE_TASK_TEMPLATES,
    AWSOnboardingCreate,
    AzureOnboardingCreate,
    GCPOnboardingCreate,
    OnboardingStatus,
    TaskTemplateAction,
    TaskTemplateResponse,
    TaskTemplateSelection,
)
from app.services.onboarding_service import OnboardingService
from app.services.workspace_service import WorkspaceService

router = APIRouter()


@router.get("/status", response_model=OnboardingStatus)
async def get_onboarding_status(
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
):
    service = OnboardingService(session)
    return await service.get_onboarding_status(current_user.id)


@router.post("/workspace")
async def create_workspace(
    workspace_in: Workspace<PERSON>reate,
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
) -> WorkspacePublic:
    try:
        workspace_service = WorkspaceService(session)
        workspace_public = await workspace_service.create_workspace(
            current_user.id, workspace_in, is_on_onboarding=True,
        )

        # Mark step 1 as completed
        onboarding_service = OnboardingService(session)
        await onboarding_service.mark_step_completed(current_user.id, 1, None)

        return workspace_public
    except Exception as e:
        logger.error(f"Error creating workspace: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while creating the workspace.",
        )


@router.post("/connect-aws")
async def connect_aws(
    aws_onboarding: AWSOnboardingCreate,
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
):
    onboarding_service = OnboardingService(session)
    result = await onboarding_service.connect_provider(
        aws_onboarding, current_user.current_workspace_id, current_user.id, "AWS"
    )

    # Mark step 2 as completed
    await onboarding_service.mark_step_completed(current_user.id, 2, CloudProvider.AWS)

    return result


@router.post("/connect-gcp")
async def connect_gcp(
    gcp_onboarding: GCPOnboardingCreate,
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
):
    onboarding_service = OnboardingService(session)
    result = await onboarding_service.connect_provider(
        gcp_onboarding, current_user.current_workspace_id, current_user.id, "GCP"
    )

    # Mark step 2 as completed
    await onboarding_service.mark_step_completed(current_user.id, 2, CloudProvider.GCP)

    return result


@router.post("/connect-azure")
async def connect_azure(
    azure_onboarding: AzureOnboardingCreate,
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
):
    onboarding_service = OnboardingService(session)
    result = await onboarding_service.connect_provider(
        azure_onboarding, current_user.current_workspace_id, current_user.id, "Azure"
    )

    # Mark step 2 as completed
    await onboarding_service.mark_step_completed(current_user.id, 2, CloudProvider.AZURE)

    return result


@router.get("/task-template", response_model=TaskTemplateResponse)
async def get_task_template(
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
):
    try:
        onboarding_service = OnboardingService(session)

        # Get selected provider from database
        selected_provider = await onboarding_service.get_selected_provider(current_user.id)

        if not selected_provider:
            raise HTTPException(
                status_code=400,
                detail="No cloud provider selected. Please complete the previous onboarding steps first.",
            )

        # Get task templates from HARDCODE_TASK_TEMPLATES
        templates = HARDCODE_TASK_TEMPLATES.get(selected_provider, [])

        if not templates:
            raise HTTPException(
                status_code=404,
                detail=f"No task templates found for cloud provider: {selected_provider.value}",
            )

        return {"cloud_provider": selected_provider.value, "templates": templates}

    except Exception as e:
        logger.error(f"Error getting task templates: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while retrieving task templates.",
        )


@router.post("/complete-task-template")
async def complete_task_template(
    selection: TaskTemplateSelection,
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
):
    """
    Complete the task template step by either selecting a template or skipping.
    This marks step 3 of onboarding as completed.
    """
    try:
        onboarding_service = OnboardingService(session)

        # Validate the action
        if selection.action not in [TaskTemplateAction.SUBMIT, TaskTemplateAction.SKIP]:
            raise HTTPException(
                status_code=400,
                detail="Action must be either 'submit' or 'skip'."
            )

        # If action is submit, validate that a template was selected
        if selection.action == TaskTemplateAction.SUBMIT and not selection.selected_template_title:
            raise HTTPException(
                status_code=400,
                detail="Template title is required when action is 'submit'."
            )

        # If action is submit, validate that the selected template exists
        if selection.action == TaskTemplateAction.SUBMIT:
            selected_provider = await onboarding_service.get_selected_provider(current_user.id)
            if not selected_provider:
                raise HTTPException(
                    status_code=400,
                    detail="No cloud provider selected. Please complete the previous onboarding steps first.",
                )

            templates = HARDCODE_TASK_TEMPLATES.get(selected_provider, [])
            template_titles = [template["title"] for template in templates]

            if selection.selected_template_title not in template_titles:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid template title. Available templates: {template_titles}"
                )

        # Mark step 3 as completed
        await onboarding_service.mark_step_completed(current_user.id, 3)

        # Return success response
        if selection.action == TaskTemplateAction.SUBMIT:
            return {
                "message": f"Task template '{selection.selected_template_title}' selected successfully.",
                "action": selection.action,
                "selected_template": selection.selected_template_title,
                "step_completed": 3
            }
        else:
            return {
                "message": "Task template step skipped successfully.",
                "action": selection.action,
                "selected_template": None,
                "step_completed": 3
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error completing task template step: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while completing the task template step.",
        )
