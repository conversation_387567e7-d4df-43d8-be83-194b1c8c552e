import json
import uuid

from fastapi import <PERSON><PERSON><PERSON>er, HTTPException, Query
from fastapi.responses import StreamingResponse

from app.api.deps import CurrentUser, SessionAsyncDep, SessionDep
from app.core.langfuse import langfuse_handler
from app.core.redis.redis_manager import RedisManager
from app.logger import logger
from app.models import (
    ConversationPublic,
    ConversationRenameRequest,
    ConversationsPublic,
    MessagePublicList,
    MessageStreamInput,
    StreamResponse,
)
from app.schemas.chat import ChatServiceContext
from app.services.agent import AgentService, validator
from app.services.agent.stream_persistence import (
    create_background_stream_task,
    stream_from_redis_queue,
)
from app.services.conversation_service import ConversationService

router = APIRouter()


# NOTE: This is not used anymore, but keeping it here for reference
# @router.post("/conversations", response_model=ConversationPublic)
# async def create_conversation(
#     current_user: CurrentUser,
#     async_session: SessionAsyncDep,
#     session: SessionDep,
#     request: ConversationCreateRequest,
# ) -> ConversationPublic:
#     """Create a new conversation."""
#     conversation_service = ConversationService(
#         session=session, async_session=async_session
#     )
#     conversation = await conversation_service.create_conversation(
#         agent_id=request.agent_id,
#         workspace_id=current_user.current_workspace_id,
#         resource_id=request.resource_id,
#     )
#     return ConversationPublic.model_validate(conversation)


@router.put("/conversations/{conversation_id}/name", response_model=ConversationPublic)
async def rename_conversation(
    current_user: CurrentUser,
    conversation_id: uuid.UUID,
    session: SessionDep,
    async_session: SessionAsyncDep,
    request: ConversationRenameRequest,
) -> ConversationPublic:
    conversation_service = ConversationService(
        session=session, async_session=async_session
    )
    conversation = await conversation_service.rename_conversation(
        conversation_id=conversation_id,
        name=request.name,
        workspace_id=current_user.current_workspace_id,
    )
    return ConversationPublic.model_validate(conversation)


@router.delete("/conversations/{conversation_id}", response_model=dict[str, str])
async def delete_conversation(
    current_user: CurrentUser,
    conversation_id: uuid.UUID,
    session: SessionDep,
    async_session: SessionAsyncDep,
) -> dict[str, str]:
    """Delete a conversation and its associated LangGraph thread data."""
    conversation_service = ConversationService(
        session=session, async_session=async_session
    )
    await conversation_service.delete_conversation(
        conversation_id, current_user.current_workspace_id
    )
    return {"status": "success"}


@router.get("/conversations", response_model=ConversationsPublic)
async def get_conversations(
    current_user: CurrentUser,
    session: SessionDep,
    async_session: SessionAsyncDep,
    agent_id: uuid.UUID | None = None,
    resource_id: uuid.UUID | None = None,
    search: str | None = None,
    skip: int = Query(
        default=0, ge=0, description="Number of records to skip for pagination"
    ),
    limit: int = Query(
        default=10, ge=1, le=100, description="Maximum number of records to return"
    ),
) -> ConversationsPublic:
    """Get list of conversations with filtering and pagination."""
    conversation_service = ConversationService(
        session=session, async_session=async_session
    )
    conversations, total = await conversation_service.get_conversations(
        workspace_id=current_user.current_workspace_id,
        agent_id=agent_id,
        resource_id=resource_id,
        search=search,
        skip=skip,
        limit=limit,
    )
    return ConversationsPublic(
        data=[ConversationPublic.model_validate(conv) for conv in conversations],
        total=total,
    )


@router.get("/messages/{conversation_id}", response_model=MessagePublicList)
async def get_messages_history(
    current_user: CurrentUser,
    conversation_id: uuid.UUID,
    session: SessionDep,
    async_session: SessionAsyncDep,
    skip: int = Query(
        default=0, ge=0, description="Number of records to skip for pagination"
    ),
    limit: int = Query(
        default=100, ge=1, le=200, description="Maximum number of records to return"
    ),
) -> MessagePublicList:
    conversation_service = ConversationService(
        session=session, async_session=async_session
    )

    # Get the conversation history
    message_list = await conversation_service.get_conversation_history(
        conversation_id=conversation_id,
        workspace_id=current_user.current_workspace_id,
        skip=skip,
        limit=limit,
    )

    # Get stream status from Redis
    redis_manager = RedisManager()
    stream_status = redis_manager.get_stream_status(str(conversation_id))

    # Update the message list with stream status
    message_list.is_streaming_active = (
        stream_status.get("is_streaming_active", False) if stream_status else False
    )
    message_list.stream_status = (
        stream_status.get("stream_status", "completed")
        if stream_status
        else "completed"
    )
    message_list.last_stream_position = (
        stream_status.get("last_stream_position", 0) if stream_status else 0
    )

    return message_list


@router.post("/chat/stream", response_model=StreamResponse)
@router.post("/chat/{conversation_id}/stream", response_model=StreamResponse)
async def chat_stream(
    current_user: CurrentUser,
    message: MessageStreamInput,
    session: SessionDep,
    async_session: SessionAsyncDep,
    conversation_id: uuid.UUID | None = None,
) -> StreamingResponse:
    # Validate inputs and handle conversation creation/loading
    response = await validator.validate_inputs(
        session,
        async_session,
        message,
        current_user.current_workspace_id,
        conversation_id=conversation_id,
        resource_id=uuid.UUID(message.resource_id) if message.resource_id else None,
    )
    conversation = response["conversation"]
    if conversation_id is None:
        conversation_id = conversation.id

    # Initialize Redis manager for stream persistence
    redis_manager = RedisManager()

    # Create chat service context
    ctx = ChatServiceContext(
        # User context
        user_id=current_user.id,
        workspace_id=current_user.current_workspace_id,
        conversation=conversation,
        # Message context
        user_prompt=message.content,
        resume=message.resume,
        approve=message.approve,
        resource_id=uuid.UUID(message.resource_id) if message.resource_id else None,
        # Runtime context
        session=session,
        async_session=async_session,
        callbacks=[langfuse_handler],
    )

    # Create async task for background processing
    task = create_background_stream_task(str(conversation_id), ctx, redis_manager)
    logger.info(
        f"Created background stream task {task} for conversation {conversation_id}"
    )

    # Send conversation_id early and then stream from Redis queue
    async def event_generator():
        try:
            # Send conversation_id early in the stream for new conversations
            yield f"data: {json.dumps({'type': 'conversation_id', 'conversation_id': str(conversation_id)})}\n\n"

            # Stream from Redis queue (handles both historical and real-time events)
            async for event_data in stream_from_redis_queue(
                str(conversation_id), redis_manager
            ):
                yield event_data

            # Trigger memory extraction in the background after stream completes
            autonomous_agent_service = AgentService(session, async_session)
            autonomous_agent_service.trigger_memory_extraction(conversation_id)
        except Exception as e:
            # Handle any streaming errors
            error_message = {"type": "error", "content": str(e), "status": 500}
            logger.exception(f"Error in persistent stream: {str(e)}")
            yield f"data: {json.dumps(error_message)}\n\n"
            yield f"data: {json.dumps({'type': 'complete'})}\n\n"

    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        },
    )


@router.get("/chat/{conversation_id}/reconnect-stream")
async def reconnect_to_stream(
    current_user: CurrentUser,
    conversation_id: uuid.UUID,
    session: SessionDep,
    async_session: SessionAsyncDep,
    last_position: int = Query(default=0, description="Last event position received"),
) -> StreamingResponse:
    """
    Reconnects to an existing stream, sending missed events + continuing live stream.
    Used when users return to a conversation page with an active stream.
    """
    redis_manager = RedisManager()
    stream_status = redis_manager.get_stream_status(str(conversation_id))

    if not stream_status or not stream_status.get("is_streaming_active"):
        raise HTTPException(404, "No active stream for this conversation")

    return StreamingResponse(
        stream_from_redis_queue(
            str(conversation_id), redis_manager, from_position=last_position
        ),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        },
    )
