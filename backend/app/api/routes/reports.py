from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException
from sqlmodel.ext.asyncio.session import AsyncSession

from app.api.deps import CurrentUser, get_async_session
from app.exceptions.legacy import RepositoryError
from app.logger import logger
from app.models import (
    Report,
)
from app.repositories.report import ReportRepository

router = APIRouter()

@router.get("/{conversation_id}")
async def get_report_by_conversation(
    conversation_id: UUID,
    current_user: CurrentUser,
    async_session: AsyncSession = Depends(get_async_session),
) -> Report:
    try:
        repo = ReportRepository(async_session)
        report = await repo.get_by_conversation_id(
            conversation_id, current_user.current_workspace_id
        )
        return report

    except RepositoryError as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        logger.exception("Internal server error.")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
