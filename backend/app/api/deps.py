from collections.abc import AsyncGenerator, Generator
from typing import Annotated

import jwt
from fastapi import Depends, HTTPException, Security, status
from fastapi.security import API<PERSON><PERSON><PERSON>eader, OAuth2PasswordBearer
from jwt.exceptions import InvalidTokenError
from jwt.exceptions import PyJWTError as JW<PERSON>rror
from pydantic import ValidationError
from qdrant_client import Async<PERSON>drantClient
from sqlmodel import Session, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core import security
from app.core.config import settings
from app.core.db import async_engine, engine
from app.core.qdrant import aclient
from app.logger import logger
from app.models import AuthorizedUser, TokenPayload, User, UserWorkspace, Workspace
from app.models.users import AuthorizedUserOnboarding
from app.modules.payment.exceptions import PaymentConfigError
from app.modules.payment.payment_client import PaymentClient
from app.services.payment_service import PaymentService
from app.utils import is_allowed_access_to_workspace


def verify_access_token(token: str) -> TokenPayload | None:
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[security.ALGORITHM]
        )
        token_data = TokenPayload(**payload)
        return token_data
    except (InvalidTokenError, ValidationError):
        logger.exception("Error verifying access token")
        return None


reusable_oauth2 = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/login/access-token"
)

api_key_header = APIKeyHeader(name="X-API-Key", auto_error=False)


def get_db() -> Generator[Session, None, None]:
    with Session(engine) as session:
        yield session


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    async with AsyncSession(async_engine) as session:
        try:
            yield session
        finally:
            await session.close()


SessionDep = Annotated[Session, Depends(get_db)]
SessionAsyncDep = Annotated[AsyncSession, Depends(get_async_session)]
TokenDep = Annotated[str, Depends(reusable_oauth2)]
ApiKeyDep = Annotated[str, Depends(api_key_header)]


def user_auth(session: SessionDep, token: TokenDep) -> AuthorizedUser:
    """
    Validates the access token and returns an authorized user.
    Raises HTTPException if token is invalid or user doesn't have access.
    """
    try:
        token_data = verify_access_token(token)
        if not token_data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
            )

        user = session.get(User, token_data.sub)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="User not found"
            )
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Inactive user"
            )

        # Get all accessible non-deleted workspaces (both owned and invited)
        workspaces = list(
            session.exec(
                select(Workspace)
                .distinct()
                .where(
                    (
                        (Workspace.owner_id == token_data.sub)  # Owned workspaces
                        | (
                            Workspace.id.in_(  # Invited workspaces
                                select(UserWorkspace.workspace_id).where(
                                    UserWorkspace.user_id == token_data.sub
                                )
                            )
                        )
                    ),
                    Workspace.is_deleted == False,
                )
            ).all()
        )

        # User has no workspaces and onboarding is not completed
        if not user.onboarding:
            logger.info(f"Allowing user {user.id} with no workspaces (onboarding not completed)")
            return AuthorizedUserOnboarding(
                **user.model_dump(),
            )

        # User is in onboarding but has no workspaces yet (step 0)
        if not user.onboarding.is_completed and not workspaces:
            logger.info(f"Allowing user {user.id} in onboarding with no workspaces")
            return AuthorizedUserOnboarding(
                **user.model_dump(),
            )

        # Determine the current workspace ID
        current_workspace_id = token_data.workspace_id

        # If token doesn't have workspace_id but user has workspaces, use the first one
        if not current_workspace_id and workspaces:
            current_workspace_id = workspaces[0].id

        # Check if the workspace in token is deleted
        if token_data.workspace_id:
            workspace = session.get(Workspace, token_data.workspace_id)
            if workspace and workspace.is_deleted:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token is for a deleted workspace, Please sign in again",
                )

        authorized_user = AuthorizedUser(
            **user.model_dump(),
            workspaces=workspaces,  # This now includes both owned and invited workspaces
            own_workspaces=user.own_workspaces,
            current_workspace_id=current_workspace_id,
        )

        if not is_allowed_access_to_workspace(authorized_user, current_workspace_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Unauthorized workspace access",
            )

        return authorized_user
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
        )


def api_auth(
    session: SessionDep, api_key: str = Security(api_key_header, use_cache=False)
) -> User:
    user = None
    if api_key:
        if api_key == settings.APP_API_KEY:
            # Here you would typically look up the user associated with this API key
            # For this example, we'll just get the first user
            user = session.exec(select(User)).first()
    else:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Authentication required",
        )

    if not user:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Authentication required",
        )
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Authentication required",
        )
    return user


CurrentUser = Annotated[AuthorizedUser, Depends(user_auth)]
APIAuth = Annotated[User, Depends(api_auth)]


def get_current_active_superuser(current_user: CurrentUser) -> AuthorizedUser:
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403, detail="The user doesn't have enough privileges"
        )
    return current_user


def get_payment_client() -> PaymentClient:
    try:
        return PaymentClient()
    except PaymentConfigError as e:
        raise HTTPException(
            status_code=500, detail=f"Payment service configuration error: {str(e)}"
        )


def get_payment_service(
    payment_client: PaymentClient = Depends(get_payment_client),
) -> PaymentService:
    return PaymentService(payment_client)


PaymentServiceDep = Annotated[PaymentService, Depends(get_payment_service)]


# QDRANT


def get_qdrant_client() -> Generator[AsyncQdrantClient, None, None]:
    try:
        yield aclient
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Qdrant service configuration error: {str(e)}"
        )


QdrantClientDep = Annotated[AsyncQdrantClient, Depends(get_qdrant_client)]
