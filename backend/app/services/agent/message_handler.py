import uuid
from datetime import datetime

from sqlmodel import Session, select

from app.logger import logger
from app.models import Message, MessageAgentThought, MessageCheckpoint
from app.services.notification_service import NotificationService


class MessageHandler:
    def __init__(self, session: Session):
        self.session = session
        self.notification_service = NotificationService(session)

    def create_message(
        self, conversation_id: uuid.UUID, content: str, role: str
    ) -> Message:
        message = Message(conversation_id=conversation_id, content=content, role=role)
        self.session.add(message)
        self.session.commit()
        self.session.refresh(message)
        return message

    def update_message_namespace(self, message_id: uuid.UUID, namespace: str):
        message = self.session.get(Message, message_id)
        if not message:
            raise ValueError(f"Message with id {message_id} not found")
        message.role = namespace
        self.session.add(message)
        self.session.commit()
        self.session.refresh(message)
        return message

    def handle_interrupt(
        self,
        response_message: Message,
        interrupt_message: str,
        accumulated_content: str = "",
    ):
        if accumulated_content and accumulated_content.strip():
            response_message.content = accumulated_content
        response_message.is_interrupt = True
        response_message.interrupt_message = interrupt_message
        self.session.add(response_message)
        self.session.commit()

        # Create a notification for the interrupt message
        conversation = response_message.conversation
        if conversation and conversation.agent and conversation.agent.workspace:
            # Create action URL with agent ID and conversation ID
            action_url = (
                f"/agents/{conversation.agent.id}?conversation={conversation.id}"
            )

            notification_result = (
                self.notification_service.create_interrupt_notification(
                    user_id=conversation.agent.workspace.owner_id,
                    interrupt_message=interrupt_message,
                    conversation_id=conversation.id,
                    requires_action=True,
                    action_url=action_url,
                    message_id=response_message.id,
                )
            )

            logger.info(
                f"Scheduled interrupt notification task: {notification_result['task_id']}"
            )

    def save_final_response(
        self, response_message: Message, content: str, role: str | None = None
    ):
        if role:
            response_message.role = role
        response_message.content += content
        response_message.updated_at = datetime.now()
        self.session.add(response_message)
        self.session.commit()

    def cleanup_on_error(
        self, response_message: Message | None, user_message: Message | None
    ):
        from app.models import MessageCheckpoint

        if response_message and response_message.id:
            # Delete associated thoughts
            thoughts = (
                self.session.execute(
                    select(MessageAgentThought).where(
                        MessageAgentThought.message_id == response_message.id
                    )
                )
                .scalars()
                .all()
            )

            # Delete associated checkpoint
            checkpoint = self.session.execute(
                select(MessageCheckpoint).where(
                    MessageCheckpoint.message_id == response_message.id
                )
            ).scalar_one_or_none()
            if checkpoint:
                self.session.delete(checkpoint)

            for thought in thoughts:
                self.session.delete(thought)
            self.session.commit()

            # Delete message
            self.session.delete(response_message)
            self.session.commit()

        if user_message:
            self.session.delete(user_message)
            self.session.commit()

    def update_message_checkpoint(
        self,
        message: Message,
        start_checkpoint_id: str | None,
        end_checkpoint_id: str | None,
    ):
        if not message or message.role == "user":
            return

        # Get existing checkpoint or create new one
        checkpoint = self.session.exec(
            select(MessageCheckpoint).where(MessageCheckpoint.message_id == message.id)
        ).first()

        if not checkpoint:
            checkpoint = MessageCheckpoint(
                message_id=message.id, start_checkpoint_id=None, end_checkpoint_id=None
            )

        # Update checkpoint IDs if available - handle start and end independently
        if start_checkpoint_id:
            checkpoint.start_checkpoint_id = start_checkpoint_id
        if end_checkpoint_id:
            checkpoint.end_checkpoint_id = end_checkpoint_id

        self.session.add(checkpoint)
        self.session.commit()

    def create_thought(
        self,
        message_id: uuid.UUID,
        position: int,
        content: str = "",
        tool_name: str = "",
        tool_input: dict | None = None,
        tool_output: str = "",
    ) -> MessageAgentThought:
        tool_input = tool_input or {}
        tool_input.pop("plans_manager", None)

        thought = MessageAgentThought(
            message_id=message_id,
            position=position,
            content=content,
            tool_name=tool_name,
            tool_input=tool_input or {},
            tool_output=tool_output,
        )
        self.session.add(thought)
        self.session.commit()
        self.session.refresh(thought)
        return thought

    def update_thought(
        self,
        thought: MessageAgentThought,
        content: str | None = None,
        tool_name: str | None = None,
        tool_input: dict | None = None,
        tool_output: str | None = None,
    ) -> MessageAgentThought:
        if content is not None:
            thought.content = content
        if tool_output is not None:
            thought.tool_output = tool_output
        if tool_name is not None:
            thought.tool_name = tool_name
        if tool_input is not None:
            thought.tool_input = tool_input

        # Use merge to handle objects that might be attached to another session
        thought = self.session.merge(thought)
        self.session.commit()
        self.session.refresh(thought)
        return thought
