import json
import uuid
from typing import Any

from partial_json_parser import loads

from app.models import Message, MessageAgentThought

from .message_handler import MessageHandler


class StreamHandler:
    def __init__(self, message_handler: MessageHandler):
        self.message_handler = message_handler

        # Track streaming tool states by tool_id
        self.tool_streams = {}

    def handle_stream_event(
        self,
        event: dict[str, Any],
        current_thought: MessageAgentThought | None,
        response_text: str,
        response_text_pointer: int,
        response_message: Message,
        thought_idx: int,
    ) -> tuple[str, int, MessageAgentThought | None, int, dict[str, Any]]:
        """Process a single stream event and update state."""
        result = {"type": None, "content": None, "name": None}

        if event["event"] == "on_chat_model_stream":
            if event["metadata"].get("langgraph_node", "") == "summarize":
                return (
                    response_text,
                    response_text_pointer,
                    current_thought,
                    thought_idx,
                    result,
                )

            if (
                len(event["data"]["chunk"].content) > 0
                and event["data"]["chunk"].response_metadata.get("type") == "tool_use"
            ):
                response_text, current_thought, thought_idx, result = (
                    self._handle_tool_use_streaming(
                        conversation_id=response_message.conversation_id,
                        event=event,
                        current_thought=current_thought,
                        response_text=response_text,
                        thought_idx=thought_idx,
                    )
                )
                return (
                    response_text,
                    response_text_pointer,
                    current_thought,
                    thought_idx,
                    result,
                )
            else:
                response_text, response_text_pointer, current_thought, result = (
                    self._handle_chat_stream(
                        event["data"],
                        response_text,
                        response_text_pointer,
                        current_thought,
                        response_message,
                        thought_idx,
                    )
                )
        elif event["event"] == "on_tool_start":
            current_thought, result = self._handle_tool_start(
                event,
                response_text,
                current_thought,
                response_message,
                thought_idx,
            )
        elif (
            event["event"] == "on_tool_end"
            and current_thought
            and current_thought.tool_name
        ):
            result = self._handle_tool_end(
                conversation_id=response_message.conversation_id,
                event=event,
                current_thought=current_thought,
            )

            thought_idx += 1
            response_text = ""
            response_text_pointer = 0
            current_thought = None

        # Filter out empty content results
        if result["type"] == "stream" and not result.get("content"):
            result = {"type": None, "content": None, "name": None}

        return (
            response_text,
            response_text_pointer,
            current_thought,
            thought_idx,
            result,
        )

    def _handle_chat_stream(
        self,
        event_data: dict[str, Any],
        response_text: str,
        response_text_pointer: int,
        current_thought: MessageAgentThought | None,
        response_message: Message,
        thought_idx: int,
    ) -> tuple[str, int, MessageAgentThought | None, dict[str, Any]]:
        """Handle chat model streaming events."""
        content = event_data["chunk"].content
        result = {"type": None, "content": None, "name": None}

        if isinstance(content, list):
            for chunk in content:
                if chunk["type"] == "text":
                    response_text += chunk["text"]
        else:
            response_text += content

        process_text = response_text[response_text_pointer:]
        if "<topic>" in process_text and "</topic>" in process_text:
            topic_start = process_text.find("<topic>")
            topic_end = process_text.find("</topic>")
            if topic_start != -1 and topic_end != -1:
                topic_text = process_text[
                    topic_start + len("<topic>") : topic_end
                ].strip()
                result = {"type": "thinking", "content": topic_text + "..."}
                response_text_pointer = (
                    response_text_pointer + topic_end + len("</topic>")
                )

        process_text = response_text[response_text_pointer:]
        if "<tool_brief>" in process_text and "</tool_brief>" in process_text:
            tool_brief_start = process_text.find("<tool_brief>")
            tool_brief_end = process_text.find("</tool_brief>")
            if tool_brief_start != -1 and tool_brief_end != -1:
                tool_brief_text = process_text[
                    tool_brief_start + len("<tool_brief>") : tool_brief_end
                ].strip()
                result = {"type": "thinking", "content": tool_brief_text + "..."}
                response_text_pointer = (
                    response_text_pointer + tool_brief_end + len("</tool_brief>")
                )

        if response_text.strip():
            if not current_thought:
                current_thought = self.message_handler.create_thought(
                    message_id=response_message.id,
                    position=thought_idx,
                    # content=response_text,
                    content="",  # TODO: remove this
                )
            else:
                current_thought = self.message_handler.update_thought(
                    thought=current_thought,
                    # content=response_text,
                    content="",  # TODO: remove this
                )

        return response_text, response_text_pointer, current_thought, result

    def _handle_tool_use_streaming(
        self,
        conversation_id: uuid.UUID,
        event: dict[str, Any],
        current_thought: MessageAgentThought | None,
        response_text: str,
        thought_idx: int,
    ) -> tuple[str, MessageAgentThought | None, int, dict[str, Any]]:
        """Handle streaming tool use chunks with enhanced phases."""
        chunk_content = event["data"]["chunk"].content[0]
        tool_id = chunk_content.get("id")
        tool_name = chunk_content.get("name")
        partial_input = chunk_content.get("partial_json", "")

        if not tool_name and conversation_id not in self.tool_streams:
            return (
                response_text,
                current_thought,
                thought_idx,
                {"type": None, "content": None, "name": None},
            )
        elif tool_name and (
            "console" not in tool_name and "group_chat" not in tool_name
        ):
            return (
                response_text,
                current_thought,
                thought_idx,
                {"type": None, "content": None, "name": None},
            )

        if conversation_id not in self.tool_streams:
            self.tool_streams[conversation_id] = {
                "name": tool_name,
                "accumulated_input": "",
                "tool_id": tool_id,
            }

            return (
                response_text,
                current_thought,
                thought_idx,
                {
                    "type": "function_stream",
                    "tool_id": self.tool_streams[conversation_id]["tool_id"],
                    "name": self.tool_streams[conversation_id]["name"],
                    "content": "",
                },
            )

        tool_stream = self.tool_streams[conversation_id]
        if partial_input:
            tool_stream["accumulated_input"] += partial_input
            return (
                response_text,
                current_thought,
                thought_idx,
                {
                    "type": "function_stream",
                    "tool_id": self.tool_streams[conversation_id]["tool_id"],
                    "name": self.tool_streams[conversation_id]["name"],
                    "content": loads(tool_stream["accumulated_input"]),
                },
            )

        return (
            response_text,
            current_thought,
            thought_idx,
            {"type": None, "content": None, "name": None},
        )

    def _handle_tool_start(
        self,
        event: dict[str, Any],
        response_text: str,
        current_thought: MessageAgentThought | None,
        response_message: Message,
        thought_idx: int,
    ) -> tuple[MessageAgentThought, dict[str, Any]]:
        """Handle tool start events."""
        tool_input: dict = event["data"].get("input")

        # Handle tool reasoning parsing for streaming tool call reasoning
        if "<tool_reasoning>" in response_text and "</tool_reasoning>" in response_text:
            tool_reasoning_start = response_text.find("<tool_reasoning>")
            tool_reasoning_end = response_text.find("</tool_reasoning>")
            if tool_reasoning_start != -1 and tool_reasoning_end != -1:
                tool_reasoning_text = response_text[
                    tool_reasoning_start + len("<tool_reasoning>") : tool_reasoning_end
                ].strip()
                tool_input["reasoning"] = (
                    tool_reasoning_text  # Overwrite reasoning if exists
                )

        if isinstance(tool_input, str):
            try:
                tool_input = json.loads(tool_input)
            except json.JSONDecodeError:
                tool_input = {"input": tool_input}

        if not current_thought:
            current_thought = self.message_handler.create_thought(
                message_id=response_message.id,
                position=thought_idx,
                tool_name=event["name"],
                tool_input=tool_input,
                content="",
            )
        else:
            current_thought = self.message_handler.update_thought(
                thought=current_thought,
                tool_name=event["name"],
                tool_input=tool_input,
            )

        result = {
            "type": "function_call",
            "name": event["name"],
            "content": tool_input,
        }

        return current_thought, result

    def _handle_tool_end(
        self,
        conversation_id: uuid.UUID,
        event: dict[str, Any],
        current_thought: MessageAgentThought,
    ):
        """Handle tool end events."""
        if conversation_id in self.tool_streams:
            del self.tool_streams[conversation_id]

        try:
            observation = json.loads(event["data"].get("output"))
        except Exception:
            observation = event["data"].get("output")

        current_thought = self.message_handler.update_thought(
            thought=current_thought,
            tool_output=json.dumps(observation)
            if isinstance(observation, dict)
            else str(observation),
        )

        return {
            "type": "function_result",
            "name": event["name"],
            "content": observation,
        }

    def check_interrupts(self, debug_data: dict[str, Any]) -> dict[str, Any] | None:
        """Check for interrupts in debug data."""
        # Handle regular interrupts
        if (
            debug_data["type"] == "task_result"
            and len(debug_data["payload"]["interrupts"]) > 0
        ):
            interrupt_message = debug_data["payload"]["interrupts"][-1]["value"]
            return {"type": "interrupt", "content": interrupt_message}
        return None

    def handle_token_usage(
        self, event: dict[str, Any], input_token_usage: int, output_token_usage: int
    ) -> tuple[int, int]:
        """Handle token usage."""

        if event["event"] == "on_chat_model_stream":
            if event["data"]["chunk"].usage_metadata:
                input_token_usage += event["data"]["chunk"].usage_metadata[
                    "input_tokens"
                ]
                output_token_usage += event["data"]["chunk"].usage_metadata[
                    "output_tokens"
                ]

        return input_token_usage, output_token_usage

    def handle_chain_stream_debug(
        self,
        event_data: dict[str, Any],
        first_checkpoint_id: str | None,
        last_checkpoint_id: str | None,
        response_message: Message,
        current_namespace: str | None,
        resume: bool,
        full_message_text: str,
    ) -> tuple[str | None, str | None, dict[str, Any] | None]:
        """Handle debug events in the chain stream.

        Returns:
            tuple containing (first_checkpoint_id, last_checkpoint_id, interrupt_result)
        """
        interrupt_result = None

        if event_data["type"] == "checkpoint":
            if first_checkpoint_id is None and not resume and full_message_text == "":
                first_checkpoint_id = event_data["payload"]["config"]["configurable"][
                    "checkpoint_id"
                ]
                # Update checkpoint will be handled by the checkpoint handler

            last_checkpoint_id = event_data["payload"]["config"]["configurable"][
                "checkpoint_id"
            ]

        interrupt_result = self.check_interrupts(event_data)
        if interrupt_result:
            if current_namespace:
                interrupt_result["namespace"] = current_namespace
            self.message_handler.handle_interrupt(
                response_message, interrupt_result["content"], full_message_text
            )

        return first_checkpoint_id, last_checkpoint_id, interrupt_result
