import uuid

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from sqlmodel import Session
from sqlmodel.ext.asyncio.session import AsyncSession

from app.models import MessageStreamInput
from app.models.agents import AgentType
from app.repositories.agent import AgentRepository
from app.repositories.attachment import AttachmentRepository
from app.repositories.conversation import ConversationRepository
from app.services.agent.token_usage_handler import TokenUsageHandler
from app.services.conversation_service import ConversationService


async def validate_inputs(
    session: Session,
    async_session: AsyncSession,
    input: MessageStreamInput,
    workspace_id: uuid.UUID,
    conversation_id: uuid.UUID | None = None,
    resource_id: uuid.UUID | None = None,
):
    """
    Unified method to either create a new conversation (if conversation_id is None)
    or validate an existing conversation (if conversation_id is provided).
    Includes all necessary validations for both cases.
    """
    conversation = None

    if conversation_id is None:
        # Create new conversation with default agent
        conversation_service = ConversationService(
            session=session, async_session=async_session
        )

        # Get default autonomous agent for the workspace
        agent_repo = AgentRepository(async_session=async_session)
        agents = await agent_repo.get_agents(workspace_id)
        autonomous_agents = [
            agent for agent in agents if agent.type == AgentType.AUTONOMOUS_AGENT
        ]

        if not autonomous_agents:
            raise HTTPException(
                status_code=400,
                detail="No autonomous agents available for conversation creation",
            )

        # Use the first autonomous agent
        default_agent = autonomous_agents[0]
        conversation = await conversation_service.create_conversation(
            agent_id=default_agent.id,
            workspace_id=workspace_id,
            resource_id=resource_id,
        )

        # Get the full conversation object
        conversation_repo = ConversationRepository(async_session=async_session)
        conversation = await conversation_repo.async_get_conversation(
            conversation_id=conversation.id
        )
        conversation_id = conversation.id
    else:
        # Validate existing conversation
        conversation_repo = ConversationRepository(async_session=async_session)
        conversation = await conversation_repo.async_get_conversation(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")

        # Attach resource to conversation if resource_id is provided
        if resource_id:
            await conversation_repo.attach_resource_to_conversation(
                conversation_id, resource_id
            )
            # Refresh the conversation object to get the updated resource_id
            conversation = await conversation_repo.async_get_conversation(
                conversation_id
            )

    # Common validations for both new and existing conversations

    # Validate attachment limits if attachments are provided
    if input.attachment_ids:
        attachment_repo = AttachmentRepository(async_session)
        is_valid, error_message = await attachment_repo.validate_attachment_limits(
            input.attachment_ids
        )
        if not is_valid:
            raise HTTPException(status_code=400, detail=error_message)

    # Validate message content
    if not input.resume and not input.content:
        error_message = "Message content must be provided when not resuming"
        raise HTTPException(status_code=400, detail=error_message)

    # Validate quota limits if not resuming
    if not input.resume:
        token_usage_handler = TokenUsageHandler(session, async_session)
        if await token_usage_handler.check_out_of_quota(workspace_id):
            raise HTTPException(status_code=429, detail="Out of quota")

    return {
        "conversation": conversation,
    }
