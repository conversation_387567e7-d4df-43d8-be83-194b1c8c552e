"""
Stream persistence implementation for SSE with Redis Queue support.
Phase 1 implementation with asyncio.create_task() background processing.
"""

import asyncio
import json
import time
from typing import AsyncGenerator

from app.api.deps import get_async_session, get_db
from app.core.redis.redis_manager import RedisManager
from app.logger import logger
from app.schemas.chat import ChatServiceContext
from app.services.agent import AgentService


async def process_and_queue_stream(
    conversation_id: str, ctx: ChatServiceContext, redis_manager: RedisManager
) -> None:
    """
    Runs the actual agent execution and queues all events to Redis.
    This continues even if client disconnects.
    Uses asyncio.create_task() for background processing (will use Celery in future).
    """
    position = 0

    # Mark conversation as actively streaming in Redis
    redis_manager.set_stream_status(
        conversation_id,
        {
            "is_streaming_active": True,
            "stream_status": "in_progress",
            "last_stream_position": 0,
            "started_at": time.time(),
        },
    )

    try:
        # Create new database sessions for background task
        # This prevents session lifecycle conflicts with the HTTP request
        async_session_gen = get_async_session()
        async_session = await async_session_gen.__anext__()

        session = next(get_db())

        try:
            # Create new context with background task sessions
            background_ctx = ChatServiceContext(
                # User context
                user_id=ctx.user_id,
                workspace_id=ctx.workspace_id,
                conversation=ctx.conversation,
                # Message context
                user_prompt=ctx.user_prompt,
                resume=ctx.resume,
                approve=ctx.approve,
                resource_id=ctx.resource_id,
                # Runtime context - use new sessions
                session=session,
                async_session=async_session,
                callbacks=ctx.callbacks,
            )

            # Create agent service for processing
            autonomous_agent_service = AgentService(session, async_session)

            # Process the agent execution
            async for event in autonomous_agent_service.execute(background_ctx):
                position += 1

                # Queue each event to Redis with timestamp and position
                event_data = {
                    "event": event,
                    "timestamp": time.time(),
                    "position": position,
                }

                redis_manager.queue_stream_event(conversation_id, event_data)

                # Update stream position in metadata
                redis_manager.update_stream_position(conversation_id, position)

                # Publish to real-time channel
                redis_manager.publish_stream_event(conversation_id, event_data)

        finally:
            # Clean up sessions
            try:
                await async_session.close()
                session.close()
            except Exception as cleanup_error:
                logger.error(f"Error cleaning up sessions: {cleanup_error}")

    except Exception as e:
        # Queue error event
        position += 1
        error_event = {
            "event": {"type": "error", "content": str(e)},
            "timestamp": time.time(),
            "position": position,
        }
        redis_manager.queue_stream_event(conversation_id, error_event)
        redis_manager.set_stream_status(conversation_id, {"stream_status": "error"})
        logger.error(f"Error in background stream processing {conversation_id}: {e}")

    finally:
        # Mark conversation as stream complete
        position += 1
        complete_event = {
            "event": {"type": "complete"},
            "timestamp": time.time(),
            "position": position,
        }
        redis_manager.queue_stream_event(conversation_id, complete_event)
        redis_manager.set_stream_status(
            conversation_id,
            {
                "is_streaming_active": False,
                "stream_status": "completed",
                "last_stream_position": position,
            },
        )
        logger.info(f"Stream processing completed for conversation {conversation_id}")


async def stream_from_redis_queue(
    conversation_id: str, redis_manager: RedisManager, from_position: int = 0
) -> AsyncGenerator[str, None]:
    """
    Streams events from Redis queue to client.
    Handles both historical events and real-time events.
    """
    client_id = f"client_{conversation_id}_{int(time.time())}"

    try:
        # First, send all historical events from specified position
        historical_events = redis_manager.get_stream_events(
            conversation_id, from_position
        )
        for event_data in historical_events:
            yield f"data: {json.dumps(event_data)}\n\n"

        # Then listen for new events in real-time using polling approach
        # Note: This would need to be implemented with aioredis for true async pub/sub
        # For now, using a polling approach with the existing RedisManager
        last_position = from_position + len(historical_events)

        while True:
            # Poll for new events every 100ms
            await asyncio.sleep(0.1)

            new_events = redis_manager.get_stream_events(conversation_id, last_position)
            for event_data in new_events:
                yield f"data: {json.dumps(event_data)}\n\n"
                last_position = event_data["position"]

                # Break if complete event
                if event_data["event"]["type"] == "complete":
                    return

            # Check if stream is still active
            stream_status = redis_manager.get_stream_status(conversation_id)
            if not stream_status or not stream_status.get("is_streaming_active"):
                break

    except asyncio.CancelledError:
        # Client disconnected - this is normal for page navigation
        logger.info(f"Client {client_id} disconnected from stream {conversation_id}")
    except Exception as e:
        logger.error(f"Error in stream_from_redis_queue: {str(e)}")
        # Send error event to client
        error_event = {
            "event": {"type": "error", "content": str(e)},
            "timestamp": time.time(),
            "position": 0,
        }
        yield f"data: {json.dumps(error_event)}\n\n"


def create_background_stream_task(
    conversation_id: str, ctx: ChatServiceContext, redis_manager: RedisManager
) -> asyncio.Task:
    """
    Creates and returns a background task for stream processing.
    The task continues even if the client disconnects.
    """
    task = asyncio.create_task(
        process_and_queue_stream(conversation_id, ctx, redis_manager)
    )

    # Add done callback for logging
    def task_done_callback(task: asyncio.Task) -> None:
        if task.exception():
            logger.error(
                f"Background stream task failed for {conversation_id}: {task.exception()}"
            )
        else:
            logger.info(f"Background stream task completed for {conversation_id}")

    task.add_done_callback(task_done_callback)
    return task
