from datetime import UTC, datetime
from uuid import UUID

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.logger import logger
from app.models import CloudProvider, UserOnboarding
from app.models.connections import ConnectionType
from app.schemas.onboarding import (
    AWSOnboardingCreate,
    AzureOnboardingCreate,
    GCPOnboardingCreate,
    OnboardingStatus,
)
from app.services.connection_service import ConnectionService


class OnboardingService:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_onboarding_status(self, user_id: UUID) -> OnboardingStatus:
        """Get the current onboarding status for a user."""
        stmt = select(UserOnboarding).where(UserOnboarding.user_id == user_id)
        scala_result = await self.session.exec(stmt)
        result = scala_result.first()

        if not result:
            return OnboardingStatus(is_completed=False, current_step=0)

        return OnboardingStatus(
            is_completed=result.is_completed, current_step=result.current_step
        )

    async def get_selected_provider(self, user_id: UUID) -> CloudProvider | None:
        """Get the selected cloud provider for a user from the database."""
        stmt = select(UserOnboarding).where(UserOnboarding.user_id == user_id)
        result = await self.session.exec(stmt)
        user_onboarding = result.first()

        if not user_onboarding:
            return None

        return user_onboarding.selected_provider

    async def mark_step_completed(
        self, user_id: UUID, step: int, selected_provider: CloudProvider | None = None
    ) -> None:
        """Mark a specific onboarding step as completed."""
        stmt = select(UserOnboarding).where(UserOnboarding.user_id == user_id)
        result = await self.session.exec(stmt)
        user_onboarding = result.first()

        if not user_onboarding:
            user_onboarding = UserOnboarding(
                user_id=user_id,
                current_step=step,
                is_completed=step >= 3,
                selected_provider=selected_provider,
            )
            self.session.add(user_onboarding)
        else:
            user_onboarding.current_step = max(user_onboarding.current_step, step)
            user_onboarding.is_completed = user_onboarding.current_step >= 3
            user_onboarding.updated_at = datetime.now(UTC)
            if selected_provider and not user_onboarding.selected_provider:
                user_onboarding.selected_provider = selected_provider
            if user_onboarding.is_completed:
                user_onboarding.completed_at = datetime.now(UTC)

        await self.session.commit()
        await self.session.refresh(user_onboarding)

    async def get_builtin_cloud_connection_by_provider_name(
        self, user_id: UUID, workspace_id: UUID, provider_name: str
    ) -> UUID:
        """Get builtin connection ID by cloud type (aws, gcp, azure)."""
        try:
            conn_service = ConnectionService(self.session)

            # Get cloud connections from default workspace (workspace_id=None)
            builtin_connections = await conn_service.get_builtin_connections(
                user_id=user_id,
                workspace_id=workspace_id,
                connection_types=[ConnectionType.CLOUD.value.lower()],
                filter_by_provider=False,
            )

            # Filter by cloud type prefix
            for connection in builtin_connections.data:
                if connection.prefix == provider_name.lower():
                    return connection.id

            raise HTTPException(
                status_code=404,
                detail=f"No builtin connection found for provider: {provider_name}",
            )
        except Exception as e:
            logger.exception(
                f"Error getting builtin connection for {provider_name}: {e}"
            )
            raise HTTPException(
                status_code=500,
                detail=f"Error retrieving builtin connection for {provider_name}",
            )

    async def connect_provider(
        self,
        onboarding_data: AWSOnboardingCreate
        | AzureOnboardingCreate
        | GCPOnboardingCreate,
        workspace_id: UUID,
        user_id: UUID,
        provider_name: str,
    ):
        """Common logic for connecting cloud providers."""
        try:
            # Get the builtin connection ID based on provider type
            builtin_cloud_connection_id = (
                await self.get_builtin_cloud_connection_by_provider_name(
                    user_id=user_id,
                    workspace_id=workspace_id,
                    provider_name=provider_name,
                )
            )

            conn_service = ConnectionService(self.session)
            config_override = onboarding_data.model_dump()
            installed_conn = await conn_service.install_builtin_connection(
                workspace_id=workspace_id,
                builtin_connection_id=builtin_cloud_connection_id,
                config_override=config_override,
            )
            if not installed_conn:
                raise HTTPException(status_code=404, detail="Connection not found")
            return installed_conn
        except ValueError as e:
            logger.exception(f"Error installing {provider_name} connection: {e}")
            raise HTTPException(status_code=400, detail="Error installing connection")
        except Exception as e:
            logger.exception(f"Error installing {provider_name} connection: {e}")
            raise HTTPException(
                status_code=500, detail="Integration service temporarily unavailable"
            )
