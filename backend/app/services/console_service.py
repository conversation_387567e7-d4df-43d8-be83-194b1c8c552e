import asyncio
import json
import uuid
from typing import Any
from urllib.parse import urlparse, urlunparse

from fastapi import WebSocket
from fastapi.websockets import WebSocketState
from sqlmodel.ext.asyncio.session import AsyncSession
from websockets.exceptions import ConnectionClosedError, ConnectionClosedOK
from websockets.legacy.client import connect as ws_connect

from app.core.config import settings
from app.core.db import async_engine
from app.exceptions.legacy import ServiceError
from app.logger import logger
from app.models import CloudProvider
from app.repositories.workspaces import WorkspaceRepository
from app.services.connection_service import ConnectionService


async def get_cloud_credentials(workspace_id: uuid.UUID) -> dict | None:
    """Fetch credentials for a workspace"""
    async_session = None
    try:
        async_session = AsyncSession(async_engine)
        workspace_repo = WorkspaceRepository(async_session)
        conn_service = ConnectionService(async_session)

        workspace = await workspace_repo.get_workspace(workspace_id)
        provider = workspace.provider if workspace else None

        if not workspace:
            return None

        cloud_credentials = await conn_service.get_cloud_connection_credentials(
            workspace_id
        )
        if provider == CloudProvider.AWS:
            return {
                "provider": CloudProvider.AWS,
                "aws_access_key_id": cloud_credentials.get("AWS_ACCESS_KEY_ID")
                if cloud_credentials
                else None,
                "aws_secret_access_key": cloud_credentials.get("AWS_SECRET_ACCESS_KEY")
                if cloud_credentials
                else None,
                "aws_default_region": cloud_credentials.get("AWS_DEFAULT_REGION")
                if cloud_credentials
                else None,
            }
        elif provider == CloudProvider.GCP:
            return {
                "provider": CloudProvider.GCP,
                "service_account_key": cloud_credentials.get(
                    "GOOGLE_SERVICE_ACCOUNT_KEY"
                )
                if cloud_credentials
                else None,
            }
        elif provider == CloudProvider.AZURE:
            return {
                "provider": CloudProvider.AZURE,
                "username": cloud_credentials.get("username")
                if cloud_credentials
                else None,
                "password": cloud_credentials.get("password")
                if cloud_credentials
                else None,
                "tenant": cloud_credentials.get("tenant")
                if cloud_credentials
                else None,
            }
        else:
            raise ServiceError(f"Unsupported provider: {provider}", status_code=400)
    except Exception as e:
        logger.exception(
            f"Error fetching cloud credentials for workspace {workspace_id}: {e}"
        )
        raise ServiceError(
            f"Error fetching cloud credentials for workspace {workspace_id}.",
            status_code=500,
        )
    finally:
        if async_session:
            await async_session.close()


async def get_k8s_credentials(workspace_id: uuid.UUID) -> dict | None:
    """Fetch k8s credentials for a workspace"""
    async_session = None
    try:
        async_session = AsyncSession(async_engine)
        connection_service = ConnectionService(async_session)
        kubeconfig = await connection_service.get_k8s_connection_credentials(
            workspace_id
        )
        if not kubeconfig:
            return None

        return kubeconfig
    except Exception as e:
        logger.exception(
            f"Error fetching k8s credentials for workspace {workspace_id}: {e}"
        )
        raise ServiceError(
            f"Error fetching k8s credentials for workspace {workspace_id}.",
            status_code=500,
        )


class ConsoleWebSocketProxy:
    """WebSocket proxy between frontend and executor following the executor pattern"""

    def __init__(self, workspace_id: uuid.UUID, user_id: uuid.UUID):
        self.workspace_id = workspace_id
        self.user_id = user_id
        self.frontend_ws: WebSocket | None = None
        self.executor_ws: Any | None = None
        self.running = False
        self.cloud_credentials: dict | None = None
        self.k8s_credentials: dict | None = None

    async def connect_to_executor(self) -> None:
        """Establish WebSocket connection to executor"""
        try:
            self.cloud_credentials = await get_cloud_credentials(self.workspace_id)
            self.k8s_credentials = await get_k8s_credentials(self.workspace_id)
            ws_url = self._build_executor_ws_url()
            self.executor_ws = await asyncio.wait_for(
                ws_connect(ws_url), timeout=settings.CONSOLE_TIMEOUT
            )
            await self._send_credentials_config()
        except Exception as e:
            raise ServiceError(f"Failed to connect to executor: {e}", status_code=500)

    def _build_executor_ws_url(self) -> str:
        """Build WebSocket URL for executor connection"""
        parsed_url = urlparse(settings.EXECUTOR_HOST)
        ws_scheme = "wss" if parsed_url.scheme == "https" else "ws"

        return urlunparse(
            (
                ws_scheme,
                parsed_url.netloc,
                f"/api/v1/console/ws/{self.workspace_id}",
                None,
                None,
                None,
            )
        )

    async def _send_credentials_config(self) -> None:
        """Send credentials configuration to executor if available"""
        if not self.cloud_credentials or not self.executor_ws:
            return

        config_message = {
            "type": "config",
            "cloud_credentials": self.cloud_credentials,
            "k8s_credentials": {
                "kubeconfig": self.k8s_credentials.get("KUBECONFIG")
                if self.k8s_credentials
                else None
            },
        }
        try:
            await self.executor_ws.send(json.dumps(config_message))
        except Exception as e:
            logger.exception(
                f"[{self.workspace_id}:{self.user_id}] Error sending credentials config to executor: {e}"
            )
            raise

    async def proxy_frontend_to_executor(self):
        """Forward messages from frontend to executor"""
        while self.running and self.frontend_ws and self.executor_ws:
            try:
                message = await self.frontend_ws.receive_text()
                await self.executor_ws.send(message)
            except (ConnectionClosedError, ConnectionClosedOK):
                # Normal websocket disconnection - no need to log as error
                logger.info(
                    f"[{self.workspace_id}:{self.user_id}] Frontend WebSocket connection closed normally"
                )
                break
            except Exception as e:
                # Only log unexpected errors
                if self.running:  # Only log if we're still supposed to be running
                    logger.error(
                        f"[{self.workspace_id}:{self.user_id}] Error forwarding message from frontend to executor: {e}"
                    )
                    await self.cleanup()
                break

    async def proxy_executor_to_frontend(self):
        """Forward messages from executor to frontend"""
        try:
            while self.running and self.frontend_ws and self.executor_ws:
                try:
                    message = await self.executor_ws.recv()
                    if self.frontend_ws.client_state == WebSocketState.CONNECTED:
                        await self.frontend_ws.send_text(message)
                    else:
                        # Frontend disconnected normally
                        logger.info(
                            f"[{self.workspace_id}:{self.user_id}] Frontend WebSocket disconnected, stopping executor proxy"
                        )
                        break
                except Exception as e:
                    # Only log unexpected errors
                    if self.running:  # Only log if we're still supposed to be running
                        logger.exception(
                            f"[{self.workspace_id}:{self.user_id}] Error forwarding message from executor to frontend: {e}"
                        )
                    break
        except Exception as e:
            # Only log if this is an unexpected error during setup/teardown
            if self.running:
                logger.exception(
                    f"[{self.workspace_id}:{self.user_id}] Unexpected error in executor->frontend proxy: {e}"
                )

    async def start_proxy(self, frontend_ws: WebSocket):
        """Start the bidirectional proxy following the async pattern"""
        self.frontend_ws = frontend_ws
        self.running = True

        try:
            await self.connect_to_executor()
            await asyncio.gather(
                self.proxy_frontend_to_executor(),
                self.proxy_executor_to_frontend(),
                return_exceptions=True,
            )
        except Exception as e:
            logger.exception(
                f"[{self.workspace_id}:{self.user_id}] Error starting proxy: {e}"
            )
            raise ServiceError(f"Error starting proxy: {e}", status_code=500)
        finally:
            await self.cleanup()

    async def cleanup(self):
        """Cleanup connections and resources"""
        self.running = False
        cleanup_tasks = []

        # Cleanup executor WebSocket
        if self.executor_ws:
            cleanup_tasks.append(self._cleanup_executor_ws())

        # Cleanup frontend WebSocket
        if self.frontend_ws:
            cleanup_tasks.append(self._cleanup_frontend_ws())

        # Run all cleanup tasks concurrently with timeout
        if cleanup_tasks:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*cleanup_tasks, return_exceptions=True),
                    timeout=5.0,  # 5 second timeout for cleanup
                )
            except Exception as e:
                logger.exception(
                    f"[{self.workspace_id}:{self.user_id}] Error during cleanup: {e}"
                )

    async def _cleanup_executor_ws(self):
        """Cleanup executor WebSocket connection"""
        try:
            if self.executor_ws:
                if not self.executor_ws.closed:
                    await asyncio.wait_for(self.executor_ws.close(), timeout=2.0)
                self.executor_ws = None
        except Exception as e:
            raise ServiceError(
                f"Error closing executor WebSocket: {e}", status_code=500
            )

    async def _cleanup_frontend_ws(self):
        """Cleanup frontend WebSocket connection"""
        try:
            if self.frontend_ws:
                if self.frontend_ws.client_state == WebSocketState.CONNECTED:
                    await asyncio.wait_for(
                        self.frontend_ws.close(code=1000, reason="Proxy cleanup"),
                        timeout=2.0,
                    )
                self.frontend_ws = None
        except Exception as e:
            raise ServiceError(
                f"Error closing frontend WebSocket: {e}", status_code=500
            )
