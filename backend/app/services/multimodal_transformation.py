"""Multimodal transformation for handling documents with images during ingestion."""

from typing import Any, Sequence

from llama_index.core.node_parser import <PERSON><PERSON><PERSON>arser
from llama_index.core.schema import BaseNode, Document, TextNode, TransformComponent

from app.logger import logger
from app.services.multimodal_embedding import CohereMultiModalEmbedding


class MultiModalTransformation(TransformComponent):
    """Custom transformation that handles both text and image content in documents."""

    def __init__(
        self,
        multimodal_embed_model: CohereMultiModalEmbedding,
        text_node_parser: NodeParser | None = None,
        **kwargs,
    ):
        super().__init__(**kwargs)
        # Use object.__setattr__ to bypass Pydantic validation for custom attributes
        object.__setattr__(self, "multimodal_embed_model", multimodal_embed_model)
        object.__setattr__(self, "text_node_parser", text_node_parser)

    def __call__(self, nodes: Sequence[BaseNode], **kwargs: Any) -> list[BaseNode]:
        """Transform nodes, handling multimodal content."""
        transformed_nodes = []

        for node in nodes:
            if isinstance(node, Document):
                # Process document - may contain image metadata
                processed_nodes = self._process_document(node)
                transformed_nodes.extend(processed_nodes)
            else:
                # Pass through other node types
                transformed_nodes.append(node)

        return transformed_nodes

    def _process_document(self, document: Document) -> list[BaseNode]:
        """Process a document, creating appropriate nodes based on content type."""
        nodes = []

        if not document.metadata:
            # Regular text document
            if self.text_node_parser:
                text_nodes = self.text_node_parser([document])
                nodes.extend(text_nodes)
            else:
                # Create single text node
                text_node = TextNode(
                    text=document.text,
                    metadata=document.metadata or {},
                    id_=document.doc_id,
                )
                nodes.append(text_node)
            return nodes

        content_type = document.metadata.get("content_type", "text")

        if content_type == "image":
            # Handle image document
            image_node = self._create_image_node(document)
            if image_node:
                nodes.append(image_node)
        elif content_type == "text":
            # Handle text document
            if self.text_node_parser:
                text_nodes = self.text_node_parser([document])
                nodes.extend(text_nodes)
            else:
                text_node = TextNode(
                    text=document.text, metadata=document.metadata, id_=document.doc_id
                )
                nodes.append(text_node)
        else:
            # Default handling for unknown content types
            text_node = TextNode(
                text=document.text, metadata=document.metadata, id_=document.doc_id
            )
            nodes.append(text_node)

        return nodes

    def _create_image_node(self, document: Document) -> BaseNode | None:
        """Create an image node from a document with image content."""
        try:
            image_base64 = document.metadata.get("image_base64")
            if not image_base64:
                logger.warning(
                    f"Document {document.doc_id} marked as image but has no image_base64"
                )
                return None

            # Create a specialized text node that will get image embeddings
            # We'll use the text content as description and embed the image
            image_node = TextNode(
                text=document.text,  # This contains the description like "PDF Page Image: ..."
                metadata={
                    **document.metadata,
                    "is_image": True,
                    "requires_image_embedding": True,
                },
                id_=document.doc_id,
            )

            return image_node

        except Exception as e:
            logger.error(
                f"Error creating image node for document {document.doc_id}: {e}"
            )
            return None

    async def acall(self, nodes: Sequence[BaseNode], **kwargs: Any) -> list[BaseNode]:
        """Async version of __call__."""
        return self.__call__(nodes, **kwargs)


class MultiModalEmbeddingTransformation(TransformComponent):
    """Transformation that applies multimodal embeddings to nodes."""

    def __init__(self, multimodal_embed_model: CohereMultiModalEmbedding, **kwargs):
        super().__init__(**kwargs)
        # Use object.__setattr__ to bypass Pydantic validation for custom attributes
        object.__setattr__(self, "multimodal_embed_model", multimodal_embed_model)

    def __call__(self, nodes: Sequence[BaseNode], **kwargs: Any) -> list[BaseNode]:
        """Apply embeddings to nodes."""
        nodes_list = list(nodes)
        for node in nodes_list:
            if isinstance(node, TextNode):
                if node.metadata and node.metadata.get("requires_image_embedding"):
                    # This node represents an image, use image embedding
                    self._apply_image_embedding(node)
                else:
                    # Regular text node, use text embedding
                    self._apply_text_embedding(node)

        return nodes_list

    def _apply_text_embedding(self, node: TextNode) -> None:
        """Apply text embedding to a text node."""
        try:
            embedding = self.multimodal_embed_model.get_text_embedding(node.text)
            node.embedding = embedding
        except Exception as e:
            logger.error(f"Error applying text embedding to node {node.id_}: {e}")

    def _apply_image_embedding(self, node: TextNode) -> None:
        """Apply image embedding to a node that represents an image."""
        try:
            image_base64 = node.metadata.get("image_base64")
            if image_base64:
                embedding = self.multimodal_embed_model.get_image_embedding(
                    image_base64
                )
                node.embedding = embedding
            else:
                logger.warning(
                    f"Node {node.id_} requires image embedding but has no image_base64"
                )
                # Fallback to text embedding
                self._apply_text_embedding(node)
        except Exception as e:
            logger.error(f"Error applying image embedding to node {node.id_}: {e}")
            # Fallback to text embedding
            self._apply_text_embedding(node)

    async def acall(self, nodes: Sequence[BaseNode], **kwargs: Any) -> list[BaseNode]:
        """Async version of __call__."""
        # For now, use sync version - can be improved with async embeddings
        return self.__call__(nodes, **kwargs)
