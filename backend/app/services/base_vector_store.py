import asyncio
from enum import Enum

import qdrant_client.models as qdrant_models
from llama_index.embeddings.bedrock import BedrockEmbedding
from llama_index.vector_stores.qdrant import QdrantVectorStore

from app.core.config import settings
from app.core.qdrant import aclient, client
from app.exceptions.kb_exceptions import (
    CollectionAccessError,
    CollectionCreationError,
    EmbeddingGenerationError,
)
from app.logger import logger


class FieldType(Enum):
    KEYWORD = "keyword"
    INTEGER = "integer"
    FLOAT = "float"
    BOOL = "bool"
    GEO = "geo"
    DATETIME = "datetime"
    TEXT = "text"
    UUID = "uuid"


field_type_map = {
    FieldType.TEXT: qdrant_models.PayloadSchemaType.TEXT,
    FieldType.INTEGER: qdrant_models.PayloadSchemaType.INTEGER,
    FieldType.FLOAT: qdrant_models.PayloadSchemaType.FLOAT,
    FieldType.BOOL: qdrant_models.PayloadSchemaType.BOOL,
    FieldType.DATETIME: qdrant_models.PayloadSchemaType.DATETIME,
    FieldType.UUID: qdrant_models.PayloadSchemaType.UUID,
    FieldType.KEYWORD: qdrant_models.PayloadSchemaType.KEYWORD,
    FieldType.GEO: qdrant_models.PayloadSchemaType.GEO,
}


class BaseVectorStore:
    def __init__(self):
        self.aqdrant_client = aclient
        self.qdrant_client = client
        self.embed_model_doc = BedrockEmbedding(
            model_name=settings.EMBEDDING_MODEL_NAME,
            region_name=settings.EMBEDDING_REGION_NAME,
            input_type=settings.COHERE_EMBEDDING_SEARCH_DOC,
        )
        self.embed_model_query = BedrockEmbedding(
            model_name=settings.EMBEDDING_MODEL_NAME,
            region_name=settings.EMBEDDING_REGION_NAME,
            input_type=settings.COHERE_EMBEDDING_SEARCH_QUERY,
        )

    async def _create_collection(self, collection_name: str) -> None:
        try:
            self.qdrant_client.create_collection(
                collection_name=collection_name,
                shard_number=1,
                replication_factor=1,
                vectors_config=qdrant_models.VectorParams(
                    size=settings.EMBEDDING_MODEL_DIMS,
                    distance=qdrant_models.Distance.COSINE,
                    on_disk=True,
                ),
                sparse_vectors_config={
                    "text-sparse": qdrant_models.SparseVectorParams(
                        index=qdrant_models.SparseIndexParams(on_disk=True),
                    )
                },
                # INFO: https://qdrant.tech/documentation/guides/multiple-partitions/#calibrate-performance
                hnsw_config=qdrant_models.HnswConfigDiff(
                    on_disk=True,
                    payload_m=16,
                    m=0,  # disable building global index for the whole collection
                ),
                optimizers_config=qdrant_models.OptimizersConfigDiff(
                    memmap_threshold=20000,  # 20KB
                    deleted_threshold=0.2,
                    vacuum_min_vector_number=1000,
                ),
                # quantization_config=qdrant_models.ScalarQuantization(
                #     scalar=qdrant_models.ScalarQuantizationConfig(
                #         type=qdrant_models.ScalarType.INT8,
                #         always_ram=True,
                #     ),
                # ),
            )

            await self.create_payload_index(collection_name, "role", FieldType.KEYWORD)
            await self.create_payload_index(collection_name, "kb_id", FieldType.UUID)
            await self.create_payload_index(
                collection_name, "workspace_id", FieldType.UUID
            )
        except Exception as e:
            logger.error(f"Failed to create collection {collection_name}: {str(e)}")
            raise CollectionCreationError(collection_name)

    async def ensure_collection_exists(self, collection_name: str) -> None:
        try:
            if not self.qdrant_client.collection_exists(collection_name):
                await self._create_collection(collection_name)
        except Exception as e:
            logger.error(
                f"Failed to check or create collection {collection_name}: {str(e)}"
            )
            raise CollectionAccessError(collection_name)

    async def create_payload_index(
        self, collection_name: str, field_name: str, field_type: FieldType
    ) -> None:
        # Use synchronous client for payload index creation as async client has gRPC issues
        self.qdrant_client.create_payload_index(
            collection_name=collection_name,
            field_name=field_name,
            field_schema=field_type_map[field_type],
            ordering=qdrant_models.WriteOrdering.WEAK,
        )

    async def get_vector_store(self) -> QdrantVectorStore:
        try:
            collection_name = settings.QDRANT_COLLECTION_NAME
            if not self.qdrant_client.collection_exists(collection_name):
                await self._create_collection(collection_name)

            # Use pre-loaded cached model instance instead of model name
            return QdrantVectorStore(
                collection_name=collection_name,
                client=self.qdrant_client,
                aclient=self.aqdrant_client,
                batch_size=8,
                parallel=1,
                enable_hybrid=True,
                sparse_embed_model=settings.FASTEMBED_SPARSE_MODEL_NAME,
                dense_vector_name="text-dense",
                sparse_vector_name="text-sparse",
            )
        except CollectionCreationError:
            raise
        except Exception as e:
            logger.error(f"Failed to initialize vector store: {str(e)}")
            raise CollectionAccessError(settings.QDRANT_COLLECTION_NAME)

    async def generate_embedding_with_retry(self, text: str) -> list[float]:
        # Generate embedding with retry logic for transient failures
        retries = 0

        while retries < settings.MAX_EMBEDDING_RETRIES:
            try:
                return await self.embed_model_doc.aget_text_embedding(text)
            except Exception:
                retries += 1
                logger.warning(
                    f"Embedding generation failed (attempt {retries}/{settings.MAX_EMBEDDING_RETRIES})"
                )
                if retries < settings.MAX_EMBEDDING_RETRIES:
                    # Exponential backoff
                    delay = settings.EMBEDDING_RETRY_DELAY * (2 ** (retries - 1))
                    logger.info(f"Retrying in {delay} seconds...")
                    await asyncio.sleep(delay)

        # All retries failed
        logger.error("All embedding generation retries failed")
        raise EmbeddingGenerationError(
            "Failed to generate embeddings after all retries"
        )
