"""Multimodal embedding service using Cohere Embed V4."""

import base64
from io import BytesIO

from llama_index.core.schema import ImageType
from llama_index.embeddings.cohere import CohereEmbedding
from llama_index.embeddings.cohere.base import CohereAIInputType, CohereAIModelName
from PIL import Image

from app.core.config import settings
from app.logger import logger


class CohereMultiModalEmbedding(CohereEmbedding):
    """Cohere multimodal embedding that supports both text and images using Embed V4."""

    def __init__(
        self,
        api_key: str,
        model_name: CohereAIModelName = CohereAIModelName.EMBED_V4,
        input_type: CohereAIInputType = CohereAIInputType.SEARCH_DOCUMENT,
        embedding_type: str = "int8",
        **kwargs,
    ):
        # Convert enum to string if needed
        model_name_str = (
            model_name.value if hasattr(model_name, "value") else str(model_name)
        )
        input_type_str = (
            input_type.value if hasattr(input_type, "value") else str(input_type)
        )

        # Call parent constructor with proper parameters
        super().__init__(
            api_key=api_key,
            model_name=model_name_str,
            input_type=input_type_str,
            embedding_type=embedding_type,
            **kwargs,
        )

        # Store for our custom methods
        self.api_key = api_key
        self.model_name = model_name
        self.input_type = input_type
        self.embedding_type = embedding_type

        # Check if model supports multimodal
        if model_name != CohereAIModelName.EMBED_V4:
            logger.warning(
                f"Model {model_name} may not support multimodal embeddings. Use EMBED_V4 for best results."
            )

    def get_text_embedding(self, text: str) -> list[float]:
        """Get embedding for text using parent's method."""
        # Use the parent class method which already handles this
        return super().get_text_embedding(text)

    def _get_image_embedding(self, image: ImageType) -> list[float | int]:
        """Get embedding for image using Cohere V4 API."""
        try:
            # Convert image to base64 if needed
            if isinstance(image, (str, bytes)):
                image_b64 = self._prepare_image_for_embedding(image)
            else:
                # Assume it's already a PIL Image or similar
                image_b64 = self._pil_to_base64(image)

            # Ensure client is initialized by calling a text embedding first if needed
            if self._client is None:
                logger.debug("Initializing Cohere client by calling text embedding")
                # Call parent's text embedding to initialize the client
                super().get_text_embedding("init")

            # Use the client from parent class
            model_name_str = (
                self.model_name.value
                if hasattr(self.model_name, "value")
                else str(self.model_name)
            )
            input_type_str = (
                self.input_type.value
                if hasattr(self.input_type, "value")
                else str(self.input_type)
            )

            response = self._client.embed(
                images=[image_b64],
                model=model_name_str,
                input_type=input_type_str,
                embedding_types=[self.embedding_type],
            )

            # Extract embedding based on type
            if self.embedding_type == "int8":
                return (
                    response.embeddings.int8[0]
                    if hasattr(response.embeddings, "int8")
                    else response.embeddings[0]
                )
            elif self.embedding_type == "uint8":
                return (
                    response.embeddings.uint8[0]
                    if hasattr(response.embeddings, "uint8")
                    else response.embeddings[0]
                )
            elif self.embedding_type == "binary":
                return (
                    response.embeddings.binary[0]
                    if hasattr(response.embeddings, "binary")
                    else response.embeddings[0]
                )
            elif self.embedding_type == "ubinary":
                return (
                    response.embeddings.ubinary[0]
                    if hasattr(response.embeddings, "ubinary")
                    else response.embeddings[0]
                )
            else:  # float
                return (
                    response.embeddings.float_[0]
                    if hasattr(response.embeddings, "float_")
                    else response.embeddings[0]
                )

        except Exception as e:
            logger.error(f"Error getting image embedding: {e}")
            raise

    def _prepare_image_for_embedding(self, image: str | bytes) -> str:
        if isinstance(image, str):
            # Check if it's already a data URI
            if image.startswith("data:"):
                return image
            # Assume it's base64, convert to data URI
            return f"data:image/png;base64,{image}"
        elif isinstance(image, bytes):
            # Convert bytes to base64 data URI
            b64_data = base64.b64encode(image).decode("utf-8")
            return f"data:image/png;base64,{b64_data}"

    def _pil_to_base64(self, image: Image.Image) -> str:
        """Convert PIL Image to data URI format."""
        buffer = BytesIO()
        image.save(buffer, format="PNG")
        image_bytes = buffer.getvalue()
        buffer.close()
        b64_data = base64.b64encode(image_bytes).decode("utf-8")
        return f"data:image/png;base64,{b64_data}"

    # Override the parent class image embedding method
    def get_image_embedding(self, img_file_path: ImageType) -> list[float | int]:
        """Get embedding for image."""
        return self._get_image_embedding(img_file_path)

    async def aget_image_embedding(self, img_file_path: ImageType) -> list[float | int]:
        """Async version of get_image_embedding."""
        # For now, use sync version - can be improved with async client
        return self.get_image_embedding(img_file_path)


def create_multimodal_embedding_service() -> CohereMultiModalEmbedding:
    """Factory function to create multimodal embedding service."""
    try:
        return CohereMultiModalEmbedding(
            api_key=settings.COHERE_API_KEY,
            model_name=settings.EMBEDDING_MODEL_NAME,
            input_type=settings.COHERE_EMBEDDING_SEARCH_DOC,
            embedding_type="float",
        )
    except Exception as e:
        logger.error(f"Error creating multimodal embedding service: {e}")
        raise
