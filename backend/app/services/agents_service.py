from uuid import <PERSON>UI<PERSON>

from sqlmodel.ext.asyncio.session import Async<PERSON>ession

from app.core.constants import DefaultAgents
from app.models import Agent, AgentType
from app.repositories import (
    AgentBuiltinToolRepository,
    AgentRepository,
    BuiltInToolRepository,
)


class AgentService:
    def __init__(self, async_session: AsyncSession):
        self.async_session = async_session
        self.agent_repository = AgentRepository(async_session)
        self.builtin_tool_repository = BuiltInToolRepository(async_session)
        self.agent_builtin_tool_repository = AgentBuiltinToolRepository(async_session)

    async def get_agents(self, workspace_id: UUID) -> list[Agent]:
        """Get all agents for a workspace."""
        agents = await self.agent_repository.get_agents(workspace_id)
        # Sort agents by is_active and then by title
        agents.sort(key=lambda x: x.title, reverse=True)
        return agents

    async def get_agent(self, agent_id: UUID) -> Agent:
        """Get an agent by ID."""
        agent = await self.agent_repository.get_agent(agent_id)
        return agent

    async def init_default_agents(self, workspace_id: UUID):
        """Initialize default agents for a workspace with their builtin tools."""
        # Get all default agent configurations
        default_agent_configs = DefaultAgents.get_all_agents()

        # Get workspace builtin tools for tool assignment
        workspace_builtin_tools = (
            await self.builtin_tool_repository.get_workspace_builtin_tools(workspace_id)
        )

        # Create a mapping of tool names to workspace builtin tool IDs
        tool_name_to_workspace_tool_id = {}
        for workspace_tool in workspace_builtin_tools:
            if workspace_tool.builtin_tool:
                tool_name_to_workspace_tool_id[workspace_tool.builtin_tool.name] = (
                    workspace_tool.id
                )

        # Create agents
        for agent_config in default_agent_configs:
            # Check if agent already exists
            if await self.agent_repository.agent_exists(
                agent_config.title, workspace_id
            ):
                continue

            # Determine agent type
            agent_type = (
                AgentType.AUTONOMOUS_AGENT
                if agent_config.title == "Cloud Thinker"
                else AgentType.CONVERSATION_AGENT
            )

            # Create agent
            agent = Agent(
                title=agent_config.title,
                type=agent_type,
                workspace_id=workspace_id,
                instructions=agent_config.instructions,
                is_active=agent_config.is_active,
                alias=agent_config.alias,
                role=agent_config.role,
                goal=agent_config.goal,
            )

            await self.agent_repository.create_agent(agent)

            if agent_type == AgentType.AUTONOMOUS_AGENT:
                continue

            # Associate agent with builtin tools using the new is_active approach
            # Get all workspace tool IDs
            all_workspace_tool_ids = list(tool_name_to_workspace_tool_id.values())

            # Get active tool IDs for this agent
            active_tool_ids = [
                tool_name_to_workspace_tool_id[tool_name]
                for tool_name in agent_config.tools
                if tool_name in tool_name_to_workspace_tool_id
            ]

            if all_workspace_tool_ids:
                await self.agent_builtin_tool_repository.init_agent_builtin_tools(
                    agent.id, all_workspace_tool_ids, active_tool_ids
                )

    async def update_agent_status(self, agent_id: UUID, is_active: bool) -> Agent:
        """Update the status of an agent."""
        agent = await self.agent_repository.update_agent(agent_id, is_active=is_active)
        return agent

    async def update_agent_instructions(
        self, agent_id: UUID, instructions: str
    ) -> Agent:
        """Update the instructions of an agent."""
        agent = await self.agent_repository.update_agent(
            agent_id, instructions=instructions
        )
        return agent
