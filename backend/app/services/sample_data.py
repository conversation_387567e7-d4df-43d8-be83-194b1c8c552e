import random
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import List

from faker import Faker
from sqlmodel import Session

from app.models import (
    CloudProvider,
    Resource,
    ResourceStatus,
    AWSResourceType,
)
from app.repositories.resources import ResourceRepository

fake = Faker()


class SampleDataService:
    """Service for generating sample data for testing and development"""

    def __init__(self, session: Session):
        self.session = session

    @staticmethod
    def _generate_aws_arn(
        resource_type: AWSResourceType, region: str, account_id: str = None
    ) -> str:
        """Generate a realistic AWS ARN for the given resource type"""
        if not account_id:
            account_id = str(random.randint(************, ************))

        resource_id = fake.uuid4()

        arn_mappings = {
            AWSResourceType.EC2: f"arn:aws:ec2:{region}:{account_id}:instance/{resource_id}",
            AWSResourceType.LAMBDA: f"arn:aws:lambda:{region}:{account_id}:function:{fake.word()}-{fake.word()}",
            AWSResourceType.RDS: f"arn:aws:rds:{region}:{account_id}:db:{fake.word()}-{fake.word()}-db",
            AWSResourceType.S3: f"arn:aws:s3:::{fake.word()}-{fake.word()}-bucket",
            AWSResourceType.ECS: f"arn:aws:ecs:{region}:{account_id}:cluster/{fake.word()}-cluster",
            AWSResourceType.EKS: f"arn:aws:eks:{region}:{account_id}:cluster/{fake.word()}-eks-cluster",
            AWSResourceType.DYNAMODB: f"arn:aws:dynamodb:{region}:{account_id}:table/{fake.word()}-table",
            AWSResourceType.EBS: f"arn:aws:ec2:{region}:{account_id}:volume/{resource_id}",
            AWSResourceType.ELB: f"arn:aws:elasticloadbalancing:{region}:{account_id}:loadbalancer/{fake.word()}-lb",
            AWSResourceType.VPC: f"arn:aws:ec2:{region}:{account_id}:vpc/{resource_id}",
            AWSResourceType.SQS: f"arn:aws:sqs:{region}:{account_id}:{fake.word()}-queue",
            AWSResourceType.SNS: f"arn:aws:sns:{region}:{account_id}:{fake.word()}-topic",
            AWSResourceType.CLOUDWATCH: f"arn:aws:logs:{region}:{account_id}:log-group:/aws/{fake.word()}",
            AWSResourceType.REDSHIFT: f"arn:aws:redshift:{region}:{account_id}:cluster:{fake.word()}-cluster",
            AWSResourceType.OPENSEARCH: f"arn:aws:opensearch:{region}:{account_id}:domain/{fake.word()}-domain",
            AWSResourceType.ELASTICACHE: f"arn:aws:elasticache:{region}:{account_id}:cluster:{fake.word()}-cache",
            AWSResourceType.CLOUDFORMATION: f"arn:aws:cloudformation:{region}:{account_id}:stack/{fake.word()}-stack/{resource_id}",
        }

        return arn_mappings.get(
            resource_type,
            f"arn:aws:{resource_type.value.lower()}:{region}:{account_id}:resource/{resource_id}",
        )

    @staticmethod
    def _generate_resource_name(resource_type: AWSResourceType) -> str:
        """Generate a realistic resource name based on type"""
        name_templates = {
            AWSResourceType.EC2: f"{fake.word()}-{fake.word()}-instance",
            AWSResourceType.LAMBDA: f"{fake.word()}-{fake.word()}-function",
            AWSResourceType.RDS: f"{fake.word()}-{fake.word()}-database",
            AWSResourceType.S3: f"{fake.word()}-{fake.word()}-bucket",
            AWSResourceType.ECS: f"{fake.word()}-ecs-cluster",
            AWSResourceType.EKS: f"{fake.word()}-eks-cluster",
            AWSResourceType.DYNAMODB: f"{fake.word()}-table",
            AWSResourceType.EBS: f"{fake.word()}-volume",
            AWSResourceType.ELB: f"{fake.word()}-load-balancer",
            AWSResourceType.VPC: f"{fake.word()}-vpc",
            AWSResourceType.SQS: f"{fake.word()}-queue",
            AWSResourceType.SNS: f"{fake.word()}-topic",
            AWSResourceType.CLOUDWATCH: f"/aws/{fake.word()}/{fake.word()}",
            AWSResourceType.REDSHIFT: f"{fake.word()}-data-warehouse",
            AWSResourceType.OPENSEARCH: f"{fake.word()}-search-domain",
            AWSResourceType.ELASTICACHE: f"{fake.word()}-cache-cluster",
            AWSResourceType.CLOUDFORMATION: f"{fake.word()}-stack",
        }

        return name_templates.get(
            resource_type, f"{fake.word()}-{resource_type.value.lower()}"
        )

    @staticmethod
    def _generate_resource_tags() -> dict:
        """Generate realistic resource tags"""
        environments = ["dev", "staging", "prod", "test"]
        teams = ["frontend", "backend", "devops", "data", "ml", "security"]
        projects = ["web-app", "api", "data-pipeline", "analytics", "mobile"]

        tags = {
            "Environment": random.choice(environments),
            "Team": random.choice(teams),
            "Project": random.choice(projects),
            "CostCenter": f"CC-{random.randint(1000, 9999)}",
            "Owner": fake.email(),
            "CreatedBy": fake.name(),
        }

        # Add some optional tags
        if random.choice([True, False]):
            tags["Application"] = fake.word()
        if random.choice([True, False]):
            tags["Version"] = (
                f"v{random.randint(1, 10)}.{random.randint(0, 9)}.{random.randint(0, 9)}"
            )
        if random.choice([True, False]):
            tags["Backup"] = random.choice(["enabled", "disabled"])

        return tags

    @staticmethod
    def _generate_resource_configurations(resource_type: AWSResourceType) -> dict:
        """Generate realistic resource configurations based on type"""
        base_config = {
            "created_date": fake.date_time_between(
                start_date="-1y", end_date="now"
            ).isoformat(),
            "monitoring_enabled": random.choice([True, False]),
        }

        type_specific_configs = {
            AWSResourceType.EC2: {
                "instance_type": random.choice(
                    ["t3.micro", "t3.small", "m5.large", "c5.xlarge", "r5.2xlarge"]
                ),
                "ami_id": f"ami-{fake.hexify(text='^^^^^^^^^^^^^^^^', upper=False)}",
                "key_name": f"{fake.word()}-keypair",
                "security_groups": [
                    f"sg-{fake.hexify(text='^^^^^^^^^^^^^^^^', upper=False)}"
                    for _ in range(random.randint(1, 3))
                ],
                "subnet_id": f"subnet-{fake.hexify(text='^^^^^^^^^^^^^^^^', upper=False)}",
                "public_ip": fake.ipv4() if random.choice([True, False]) else None,
                "private_ip": fake.ipv4_private(),
                "ebs_optimized": random.choice([True, False]),
                "storage": {
                    "root_volume_size": random.randint(8, 100),
                    "root_volume_type": random.choice(["gp3", "gp2", "io1", "io2"]),
                },
            },
            AWSResourceType.RDS: {
                "engine": random.choice(
                    ["mysql", "postgresql", "mariadb", "oracle-ee", "sqlserver-ex"]
                ),
                "engine_version": f"{random.randint(5, 8)}.{random.randint(0, 9)}",
                "instance_class": random.choice(
                    ["db.t3.micro", "db.t3.small", "db.m5.large", "db.r5.xlarge"]
                ),
                "allocated_storage": random.randint(20, 1000),
                "storage_type": random.choice(["gp2", "gp3", "io1"]),
                "multi_az": random.choice([True, False]),
                "backup_retention_period": random.randint(0, 35),
                "encrypted": random.choice([True, False]),
            },
            AWSResourceType.LAMBDA: {
                "runtime": random.choice(
                    ["python3.9", "python3.10", "nodejs18.x", "java11", "dotnet6"]
                ),
                "memory_size": random.choice([128, 256, 512, 1024, 2048, 3008]),
                "timeout": random.randint(3, 900),
                "handler": f"{fake.word()}.{fake.word()}",
                "code_size": random.randint(1000, 50000000),
                "environment_variables": {
                    "NODE_ENV": random.choice(["development", "production"]),
                    "LOG_LEVEL": random.choice(["DEBUG", "INFO", "WARN", "ERROR"]),
                },
            },
            AWSResourceType.S3: {
                "bucket_type": random.choice(["Standard", "IA", "Glacier"]),
                "versioning": random.choice([True, False]),
                "encryption": random.choice([True, False]),
                "public_access_blocked": random.choice([True, False]),
                "lifecycle_rules": random.randint(0, 5),
                "size_bytes": random.randint(1000000, ************0),
                "object_count": random.randint(1, 1000000),
            },
            AWSResourceType.DYNAMODB: {
                "billing_mode": random.choice(["PAY_PER_REQUEST", "PROVISIONED"]),
                "read_capacity": random.randint(1, 1000)
                if random.choice([True, False])
                else None,
                "write_capacity": random.randint(1, 1000)
                if random.choice([True, False])
                else None,
                "table_size_bytes": random.randint(1000, 1000000000),
                "item_count": random.randint(1, 1000000),
                "global_secondary_indexes": random.randint(0, 5),
                "point_in_time_recovery": random.choice([True, False]),
                "encryption_at_rest": random.choice([True, False]),
            },
        }

        config = {**base_config, **type_specific_configs.get(resource_type, {})}
        return config

    def generate_sample_resources(
        self, workspace_id: uuid.UUID, count: int = 50
    ) -> List[Resource]:
        """Generate a list of sample resources for a workspace"""
        resources = []

        # AWS regions
        regions = [
            "us-east-1",
            "us-east-2",
            "us-west-1",
            "us-west-2",
            "eu-west-1",
            "eu-west-2",
            "eu-central-1",
            "ap-southeast-1",
            "ap-northeast-1",
            "ca-central-1",
        ]

        # Get all resource types
        resource_types = list(AWSResourceType)

        # Generate account ID for consistency
        account_id = str(random.randint(************, ************))

        for _ in range(count):
            resource_type = random.choice(resource_types)
            region = random.choice(regions)

            resource = Resource(
                workspace_id=workspace_id,
                name=self._generate_resource_name(resource_type),
                resource_id=self._generate_aws_arn(resource_type, region, account_id),
                type=resource_type,
                region=region,
                status=random.choice(list(ResourceStatus)),
                provider=CloudProvider.AWS,
                description=fake.text(max_nb_chars=200),
                tags=self._generate_resource_tags(),
                configurations=self._generate_resource_configurations(resource_type),
                created_at=fake.date_time_between(start_date="-6M", end_date="now"),
                updated_at=fake.date_time_between(start_date="-1M", end_date="now"),
                is_active=random.choice([True, True, True, False]),  # 75% active
            )

            resources.append(resource)

        return resources

    def create_sample_resources(
        self, workspace_id: uuid.UUID, count: int = 50
    ) -> List[Resource]:
        """Create sample resources in the database"""
        resources = self.generate_sample_resources(workspace_id, count)
        created_resources = []

        for resource in resources:
            # Use the existing create_or_update method
            db_resource = ResourceRepository.create_or_update(self.session, resource)
            created_resources.append(db_resource)

        self.session.commit()
        return created_resources

    def clear_sample_resources(self, workspace_id: uuid.UUID) -> int:
        """Clear all resources for a workspace (use with caution)"""
        from sqlmodel import delete

        # Delete resources for the workspace
        statement = delete(Resource).where(Resource.workspace_id == workspace_id)
        result = self.session.exec(statement)
        deleted_count = result.rowcount
        self.session.commit()

        return deleted_count
