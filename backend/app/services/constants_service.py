"""
Service for managing application constants and enums.
"""

from app.constants import (
    get_all_constants,
    get_constant_categories,
    get_constant_data,
)
from app.logger import logger
from app.schemas.constants import (
    AllConstantsResponse,
    ConstantCategoriesResponse,
    ConstantCategory,
)


class ConstantsService:
    """Service for handling constants and enums data."""

    @staticmethod
    def get_all_categories() -> ConstantCategoriesResponse:
        """Get all available constant categories."""
        try:
            categories = get_constant_categories()
            return ConstantCategoriesResponse(
                categories=categories, total=len(categories)
            )
        except Exception as e:
            logger.error(f"Error getting constant categories: {str(e)}")
            raise

    @staticmethod
    def get_category_data(category: str) -> ConstantCategory | None:
        """Get data for a specific constant category."""
        try:
            registry_data = get_constant_data(category)
            if not registry_data:
                logger.warning(f"Category '{category}' not found in constants registry")
                return None

            return ConstantCategory.from_registry_data(category, registry_data)
        except Exception as e:
            logger.error(f"Error getting category data for '{category}': {str(e)}")
            raise

    @staticmethod
    def get_all_constants_data() -> AllConstantsResponse:
        """Get all constants data."""
        try:
            registry = get_all_constants()
            return AllConstantsResponse.from_registry(registry)
        except Exception as e:
            logger.error(f"Error getting all constants data: {str(e)}")
            raise
