from uuid import UUID

from sqlmodel.ext.asyncio.session import AsyncSession

from app.exceptions.legacy import ServiceError
from app.repositories import AgentBuiltinToolRepository, BuiltInToolRepository
from app.schemas.agents_builtin_tools import (
    AgentBuiltInToolBulkUpdateResult,
    AgentBuiltInToolsBulkUpdateResponse,
    AgentBuiltInToolsPublic,
    AgentBuiltInToolUpdate,
)


class AgentBuiltinToolService:
    def __init__(self, session: AsyncSession):
        self.session = session
        self.builtin_tool_repository = BuiltInToolRepository(session)
        self.agent_builtin_tool_repository = AgentBuiltinToolRepository(session)

    async def get_agents_builtin_tools(
        self, agent_ids: list[UUID]
    ) -> list[AgentBuiltInToolsPublic]:
        """
        Get all built-in tools associated with agents, including permission requirements.

        Args:
            agent_ids: List of agent IDs to get tools for

        Returns:
            List of agent built-in tools with their permission requirements
        """
        # Get all agent tools from repository
        agent_tools = (
            await self.agent_builtin_tool_repository.get_all_agent_builtin_tools(
                agent_ids
            )
        )

        return [
            AgentBuiltInToolsPublic(
                agent_id=agent_id,
                tools=sorted(tools, key=lambda x: (not x.is_active, x.name.lower())),
            )
            for agent_id, tools in agent_tools.items()
        ]

    async def bulk_update_agent_builtin_tools(
        self,
        workspace_id: UUID,
        agent_id: UUID,
        updates: list[AgentBuiltInToolUpdate],
    ) -> AgentBuiltInToolsBulkUpdateResponse:
        """
        Bulk update agent builtin tools with transaction support.

        Args:
            workspace_id: ID of the workspace
            agent_id: ID of the agent
            updates: List of updates to apply

        Returns:
            Bulk update response with individual results
        """
        results = []
        success_count = 0

        try:
            # Validate all workspace_builtin_tool_ids belong to workspace
            tool_ids = [update.workspace_builtin_tool_id for update in updates]
            valid_tools = (
                await self.builtin_tool_repository.get_workspace_builtin_tools_by_ids(
                    workspace_id, tool_ids
                )
            )
            valid_tool_ids = {tool.id for tool in valid_tools}

            for update in updates:
                try:
                    if update.workspace_builtin_tool_id not in valid_tool_ids:
                        results.append(
                            AgentBuiltInToolBulkUpdateResult(
                                workspace_builtin_tool_id=update.workspace_builtin_tool_id,
                                success=False,
                                error_message="Tool not found in workspace",
                            )
                        )
                        continue

                    # Perform individual update
                    success = await self.agent_builtin_tool_repository.update_agent_builtin_tools(
                        workspace_builtin_tool_id=update.workspace_builtin_tool_id,
                        agent_id=agent_id,
                        is_active=update.is_active,
                    )

                    if success:
                        success_count += 1
                        results.append(
                            AgentBuiltInToolBulkUpdateResult(
                                workspace_builtin_tool_id=update.workspace_builtin_tool_id,
                                success=True,
                            )
                        )
                    else:
                        results.append(
                            AgentBuiltInToolBulkUpdateResult(
                                workspace_builtin_tool_id=update.workspace_builtin_tool_id,
                                success=False,
                                error_message="Update failed",
                            )
                        )

                except Exception as e:
                    results.append(
                        AgentBuiltInToolBulkUpdateResult(
                            workspace_builtin_tool_id=update.workspace_builtin_tool_id,
                            success=False,
                            error_message=str(e),
                        )
                    )

            return AgentBuiltInToolsBulkUpdateResponse(
                total_count=len(updates),
                success_count=success_count,
                failed_count=len(updates) - success_count,
                results=results,
            )

        except Exception as e:
            raise ServiceError(f"Bulk update failed: {str(e)}", 500)
