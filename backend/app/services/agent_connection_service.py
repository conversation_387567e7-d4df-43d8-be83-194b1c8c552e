from uuid import UUID

from sqlmodel.ext.asyncio.session import AsyncSession

from app.exceptions.legacy import ServiceError
from app.repositories.agent_connection import AgentConnectionRepository
from app.repositories.connection import ConnectionRepository
from app.schemas.agent_connections import AgentConnectionsPublic


class AgentConnectionService:
    def __init__(self, session: AsyncSession):
        self.session = session
        self.agent_connection_repository = AgentConnectionRepository(session)
        self.connection_repository = ConnectionRepository(session)

    async def get_agents_connections(
        self, agent_ids: list[UUID]
    ) -> list[AgentConnectionsPublic]:
        """
        Get all connections associated with agents.

        Args:
            agent_ids: List of agent IDs to get connections for

        Returns:
            List of agent connections with their connections
        """
        try:
            # Get all agent connections from repository
            agent_connections = (
                await self.agent_connection_repository.get_all_agent_connections(
                    agent_ids
                )
            )

            return [
                AgentConnectionsPublic(
                    agent_id=agent_id,
                    connections=connections,
                )
                for agent_id, connections in agent_connections.items()
            ]
        except Exception as e:
            raise ServiceError(f"Failed to get agents connections: {str(e)}", 500)

    async def create_agent_connection(
        self,
        workspace_id: UUID,
        agent_id: UUID,
        conn_id: UUID,
    ) -> None:
        """
        Create a connection association for an agent.

        Args:
            workspace_id: ID of the workspace
            agent_id: ID of the agent
            conn_id: Connection ID to associate with the agent
        """
        try:
            # Validate connection belongs to workspace
            connection = await self.connection_repository.get_connection(
                conn_id, workspace_id
            )
            if not connection:
                raise ServiceError(
                    f"Connection {conn_id} not found in workspace {workspace_id}", 404
                )

            await self.agent_connection_repository.create_agent_connection(
                agent_id=agent_id,
                conn_id=conn_id,
            )
        except Exception as e:
            raise ServiceError(f"Failed to create agent connection: {str(e)}", 500)

    async def delete_agent_connection(
        self,
        workspace_id: UUID,
        agent_id: UUID,
        conn_id: UUID,
    ) -> None:
        """
        Delete a connection association from an agent.

        Args:
            workspace_id: ID of the workspace
            agent_id: ID of the agent
            conn_id: Connection ID to remove from the agent
        """
        try:
            # Validate connection belongs to workspace
            connection = await self.connection_repository.get_connection(
                conn_id, workspace_id
            )
            if not connection:
                raise ServiceError(
                    f"Connection {conn_id} not found in workspace {workspace_id}", 404
                )

            await self.agent_connection_repository.remove_agent_connection(
                agent_id=agent_id,
                conn_id=conn_id,
            )
        except Exception as e:
            raise ServiceError(f"Failed to delete agent connection: {str(e)}", 500)
