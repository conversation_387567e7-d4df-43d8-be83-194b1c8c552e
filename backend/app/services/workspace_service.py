import json
from uuid import U<PERSON><PERSON>

from sqlmodel.ext.asyncio.session import AsyncSession

from app.exceptions.legacy import ServiceError
from app.logger import logger
from app.models import (
    AWSAccountDetail,
    CloudProvider,
    GCPAccountDetail,
    Workspace,
    WorkspaceCreate,
    WorkspaceDetail,
    WorkspacePublic,
    WorkspacesPublic,
    WorkspaceUpdate,
)
from app.repositories.workspaces import WorkspaceRepository
from app.services import AgentService, BuiltInToolService


class WorkspaceService:
    def __init__(self, async_session: AsyncSession):
        self.async_session = async_session
        self.workspace_repo = WorkspaceRepository(async_session)
        self.agent_service = AgentService(async_session=async_session)
        self.builtin_tool_service = BuiltInToolService(async_session=async_session)

    async def get_workspaces(
        self, user_id: UUID, skip: int = 0, limit: int = 10
    ) -> WorkspacesPublic:
        try:
            workspaces = await self.workspace_repo.get_workspaces(user_id, skip, limit)
            workspaces_public = [
                WorkspacePublic(
                    name=workspace.name,
                    description=workspace.description,
                    provider=workspace.provider,
                    id=workspace.id,
                    is_default=workspace.is_default,
                    is_deleted=workspace.is_deleted,
                    created_at=workspace.created_at,
                    updated_at=workspace.updated_at,
                )
                for workspace in workspaces
            ]
            return WorkspacesPublic(data=workspaces_public, count=len(workspaces))
        except Exception as e:
            logger.error(f"Error getting workspaces: {e}")
            raise ServiceError(
                "An unexpected error occurred while getting workspaces.",
                status_code=500,
            )

    async def get_workspace_details(
        self, user_id: UUID, workspace_id: UUID
    ) -> WorkspaceDetail:
        try:
            workspace = await self.workspace_repo.get_workspace(workspace_id)
            logger.info(f"Workspace: {workspace}")
            if workspace is None:
                raise ServiceError("Workspace is not found", status_code=404)

            if not self.workspace_repo.can_view_workspace(user_id, workspace_id):
                raise ServiceError(
                    "You don't have permission to view this workspace.", status_code=403
                )

            aws_account = workspace.aws_account if workspace.aws_account else None

            if aws_account and aws_account.credential:
                credential = aws_account.credential
                access_key_id = "****************" + credential.access_key_id[-4:]
                secret_access_key = (
                    "****************" + credential.secret_access_key[-4:]
                )
                account_id = "********" + aws_account.account_id[-4:]


            gcp_account = workspace.gcp_account if workspace.gcp_account else None
            if gcp_account and gcp_account.credential:
                credential = gcp_account.credential
                key_public = json.loads(credential.key)
                for k, v in key_public.items():
                    key_public[k] = "****************" + v[-4:]
                key_public = json.dumps(key_public, indent=2)

            return WorkspaceDetail(
                id=workspace.id,
                is_default=workspace.is_default,
                is_deleted=workspace.is_deleted,
                created_at=workspace.created_at,
                updated_at=workspace.updated_at,
                name=workspace.name,
                description=workspace.description,
                provider=workspace.provider,
                aws_account=AWSAccountDetail(
                    name=aws_account.name,
                    description=aws_account.description,
                    environment=aws_account.environment,
                    workspace_id=workspace.id,
                    id=aws_account.id,
                    account_id=account_id,
                    access_key_id=access_key_id,
                    secret_access_key=secret_access_key,
                )
                if aws_account
                else None,
                gcp_account=GCPAccountDetail(
                    name=gcp_account.name,
                    description=gcp_account.description,
                    environment=gcp_account.environment,
                    workspace_id=workspace.id,
                    id=gcp_account.id,
                    key=key_public,
                )
                if gcp_account
                else None,
                settings=workspace.settings,
            )
        except Exception as e:
            logger.error(f"Error getting workspace details: {e}")
            raise ServiceError(
                "An unexpected error occurred while getting workspace details.",
                status_code=500,
            )

    async def create_workspace(
        self,
        user_id: UUID,
        workspace_in: WorkspaceCreate,
        default_workspace: bool = False,
        is_on_onboarding: bool = False,
    ) -> WorkspacePublic:
        """
        Create a new workspace.
        If is_on_onboarding is True, the workspace will be created as the default workspace.
        """
        try:
            if not self.workspace_repo.can_create_workspaces(
                user_id, default_workspace, is_on_onboarding
            ):
                raise ServiceError(
                    "You don't have permission to create workspaces. Only workspace owners can create new workspaces.",
                    status_code=403,
                )

            # Create workspace
            workspace = Workspace.model_validate(
                workspace_in, update={"owner_id": user_id}
            )
            workspace = await self.workspace_repo.create_workspace(workspace)

            # Store workspace ID to avoid lazy loading issues after async operations
            workspace_id = workspace.id

            # Create default workspace built-in tools
            await self.builtin_tool_service.init_default_workspace_built_in_tools(
                workspace_id
            )

            # Create default agents with default tools
            await self.agent_service.init_default_agents(workspace_id)

            await self.async_session.refresh(workspace)
            return WorkspacePublic(
                name=workspace.name,
                description=workspace.description,
                provider=workspace.provider,
                id=workspace.id,
                is_default=workspace.is_default,
                is_deleted=workspace.is_deleted,
                created_at=workspace.created_at,
                updated_at=workspace.updated_at,
            )
        except Exception as e:
            logger.exception(f"Error creating workspace: {e}")
            raise ServiceError(
                "An unexpected error occurred while creating workspace.",
                status_code=500,
            )

    async def update_workspace(
        self,
        user_id: UUID,
        workspace_id: UUID,
        workspace_in: WorkspaceUpdate,
    ) -> WorkspacePublic:
        try:
            if not self.workspace_repo.can_manage_workspace(user_id, workspace_id):
                raise ServiceError(
                    "You don't have permission to update workspaces. Only workspace owners can update workspaces.",
                    status_code=403,
                )

            workspace = await self.workspace_repo.get_workspace(workspace_id)
            if workspace is None:
                raise ServiceError("Workspace is not found", status_code=404)

            # Update workspace
            workspace_data = workspace_in.model_dump(exclude_unset=True)
            for key, value in workspace_data.items():
                setattr(workspace, key, value)

            workspace = await self.workspace_repo.update_workspace(workspace)
            return WorkspacePublic(
                name=workspace.name,
                description=workspace.description,
                provider=workspace.provider,
                id=workspace.id,
                is_default=workspace.is_default,
                is_deleted=workspace.is_deleted,
                created_at=workspace.created_at,
                updated_at=workspace.updated_at,
            )
        except Exception as e:
            logger.error(f"Error updating workspace: {e}")
            raise ServiceError(
                "An unexpected error occurred while updating workspace.",
                status_code=500,
            )

    async def update_workspace_cloud_provider(
        self, workspace_id: UUID, cloud_provider: CloudProvider
    ) -> WorkspacePublic:
        try:
            workspace = await self.workspace_repo.get_workspace(workspace_id)
            if not workspace:
                raise ServiceError("Workspace not found", status_code=404)

            workspace.provider = cloud_provider
            workspace = await self.workspace_repo.update_workspace(workspace)
            return WorkspacePublic(
                name=workspace.name,
                description=workspace.description,
                provider=workspace.provider,
                id=workspace.id,
                is_default=workspace.is_default,
                is_deleted=workspace.is_deleted,
                created_at=workspace.created_at,
                updated_at=workspace.updated_at,
            )
        except Exception as e:
            logger.error(f"Error updating workspace cloud provider setting: {e}")
            raise ServiceError(
                "An unexpected error occurred while updating workspace cloud provider setting.",
                status_code=500,
            )

    async def delete_workspace(self, user_id: UUID, workspace_id: UUID) -> None:
        try:
            if not self.workspace_repo.can_manage_workspace(user_id, workspace_id):
                raise ServiceError(
                    "You don't have permission to delete workspaces. Only workspace owners can delete workspaces.",
                    status_code=403,
                )

            workspace = await self.workspace_repo.get_workspace(workspace_id)
            if not workspace:
                raise ServiceError("Workspace is not found", status_code=404)

            if workspace.is_deleted:
                raise ServiceError("Workspace is already deleted", status_code=400)

            if workspace.is_default:
                raise ServiceError(
                    "You can't delete the default workspace", status_code=400
                )

            await self.workspace_repo.delete_workspace(workspace)
        except Exception as e:
            logger.error(f"Error deleting workspace: {e}")
            raise ServiceError(
                "An unexpected error occurred while deleting workspace.",
                status_code=500,
            )
