import asyncio
from functools import wraps
from typing import Any

from app.exceptions.kb_exceptions import PointLimitExceeded
import celery

from app.core.celery_app import HIGH_PRIORITY, celery_app
from app.core.config import settings
from app.core.db_session import get_task_session
from app.logger import logger
from app.models import AsyncTaskStatus
from app.repositories.kb import KBRepository
from app.repositories.object_storage.provider import get_object_storage_repository
from app.services.kb.base import create_kb_services


def async_to_sync(f):
    """Decorator to convert async functions to sync for Celery tasks."""

    @wraps(f)
    def wrapper(*args, **kwargs):
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        return loop.run_until_complete(f(*args, **kwargs))

    return wrapper


@celery_app.task(
    name="kb_tasks.ingest_from_website",
    bind=True,
    priority=HIGH_PRIORITY,
)
@async_to_sync
async def ingest_from_website_task(
    self: celery.Task,
    doc_ids: list[str],
    kb_id: str,
    user_id: str | None = None,
    workspace_id: str | None = None,
) -> dict[str, Any]:
    self.update_state(
        state="PROGRESS",
        meta={
            "progress": 0,
            "message": "Ingesting website content",
            "source_type": "website",
        },
    )

    try:
        from uuid import UUID

        async with get_task_session() as session:
            kb_services = create_kb_services(session=session)
            kb_service_manager = kb_services["manager"]

            result = await kb_service_manager.ingest_from_website(
                session=session,
                doc_ids=doc_ids,
                kb_id=kb_id,
                call_back=self.update_state,
                user_id=UUID(user_id) if user_id else None,
                workspace_id=UUID(workspace_id) if workspace_id else None,
            )

        return {
            "task_id": self.request.id,
            "status": AsyncTaskStatus.COMPLETED,
            "result": result,
        }

    except Exception as e:
        error_message = str(e)
        if isinstance(e, PointLimitExceeded):
            error_message = f"Point limit exceeded: {error_message}"

        logger.error(f"Error in ingest_from_website_task: {error_message}")
        self.update_state(
            state="FAILURE",
            meta={
                "progress": 0,
                "status": AsyncTaskStatus.FAILED,
                "error": error_message,
            },
        )
        raise


@celery_app.task(name="kb_tasks.batch_ingest_files", bind=True, priority=HIGH_PRIORITY)
@async_to_sync
async def ingest_from_files_task(
    self: celery.Task,
    doc_ids: list[str],
    kb_id: str,
    user_id: str | None = None,
    workspace_id: str | None = None,
) -> dict[str, Any]:
    self.update_state(
        state="PROGRESS",
        meta={
            "progress": 0,
            "message": "Starting ingestion",
        },
    )

    try:
        from uuid import UUID

        async with get_task_session() as session:
            kb_services = create_kb_services(session=session)
            kb_service_manager = kb_services["manager"]

            result = await kb_service_manager.ingest_from_files(
                session=session,
                doc_ids=doc_ids,
                call_back=self.update_state,
                kb_id=kb_id,
                user_id=UUID(user_id) if user_id else None,
                workspace_id=UUID(workspace_id) if workspace_id else None,
            )

        return {
            "task_id": self.request.id,
            "status": AsyncTaskStatus.COMPLETED,
            "result": result,
        }

    except Exception as e:
        error_message = str(e)
        if isinstance(e, PointLimitExceeded):
            error_message = f"Point limit exceeded: {error_message}"

        logger.error(f"Error in ingest_from_files_task: {error_message}")
        self.update_state(
            state="FAILURE",
            meta={
                "progress": 0,
                "status": AsyncTaskStatus.FAILED,
                "error": error_message,
            },
        )
        raise


@celery_app.task(
    name="kb_tasks.delete_document",
    bind=True,
    priority=HIGH_PRIORITY,
    autoretry_for=(Exception,),
    retry_kwargs={"max_retries": 3, "countdown": 60},
    retry_backoff=True,
    retry_backoff_max=300,
    retry_jitter=True,
)
@async_to_sync
async def delete_document_task(
    self: celery.Task,
    workspace_id: str,
    document_id: str,
    object_name: str,
) -> dict[str, Any]:
    try:
        # 1. Delete the document from the object storage
        os_repo = get_object_storage_repository()
        await os_repo.delete_file(
            object_name=object_name, bucket_name=settings.KB_BUCKET
        )

        # 2. Delete the document from the vector store
        async with get_task_session() as session:
            kb_services = create_kb_services(session=session)
            kb_service_manager = kb_services["manager"]
            await kb_service_manager.delete_documents_by_document_id(
                document_id=document_id
            )

            # 3. Delete the document from the database
            kb_repo = KBRepository(session)
            await kb_repo.soft_delete_document(document_id)

        result = None

        return {
            "task_id": self.request.id,
            "status": AsyncTaskStatus.COMPLETED,
            "result": result,
        }

    except Exception as e:
        logger.error(f"Error in delete_document_task: {str(e)}")
        raise


@celery_app.task(
    name="kb_tasks.delete_kb",
    bind=True,
    priority=HIGH_PRIORITY,
    autoretry_for=(Exception,),
    retry_kwargs={"max_retries": 3, "countdown": 60},
    retry_backoff=True,
    retry_backoff_max=300,
    retry_jitter=True,
)
@async_to_sync
async def delete_kb_task(
    self: celery.Task,
    kb_id: str,
    workspace_id: str,
) -> dict[str, Any]:
    try:
        # 1. Delete KB documents from the workspace vector store
        async with get_task_session() as session:
            kb_services = create_kb_services(session=session)
            kb_service_manager = kb_services["manager"]
            await kb_service_manager.delete_kb_documents(workspace_id, kb_id)

        # 2. Delete the KB from object storage
        os_repo = get_object_storage_repository()
        await os_repo.delete_multiple_files(
            object_names=[f"{kb_id}/*"],
            bucket_name=settings.KB_BUCKET,
        )

        result = None

        return {
            "task_id": self.request.id,
            "status": AsyncTaskStatus.COMPLETED,
            "result": result,
        }

    except Exception as e:
        logger.error(f"Error in delete_kb_task: {str(e)}")
        raise
