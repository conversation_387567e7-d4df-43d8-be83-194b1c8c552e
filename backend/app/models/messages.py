import uuid
from datetime import datetime
from enum import Enum
from typing import TYPE_CHECKING, Optional
from uuid import UUID, uuid4

from pydantic import BaseModel
from sqlmodel import (
    JSON,
    Column,
    DateTime,
    Field,
    Float,
    Relationship,
    SQLModel,
    Text,
    UniqueConstraint,
    func,
)

if TYPE_CHECKING:
    from .conversations import Conversation
    from .tokens import TokenUsage
    from .users import User


class MessageDisplayComponentType(str, Enum):
    """
    Enum for display component types (currently supporting only tables and charts)
    """

    TABLE = "table"
    CHART = "chart"


class ChartType(str, Enum):
    """
    Enum for different types of charts available in the system
    """

    LINE = "line"
    BAR = "bar"
    PIE = "pie"
    DOUGHNUT = "doughnut"
    AREA = "area"
    SCATTER = "scatter"
    RADAR = "radar"
    STEP_AREA = "step_area"
    SANKEY = "sankey"


class MessageBase(SQLModel):
    content: str = Field(sa_column=Column(Text))
    role: str = Field(
        max_length=50, default="user", index=True
    )  # Can be "user", "assistant", "system", etc.
    is_interrupt: bool = Field(default=False, index=True)
    interrupt_message: str | None = Field(default=None)


class Message(MessageBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    conversation_id: uuid.UUID = Field(
        foreign_key="conversation.id", ondelete="CASCADE", index=True
    )
    conversation: "Conversation" = Relationship(back_populates="messages")
    thoughts: list["MessageAgentThought"] = Relationship(
        back_populates="message",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    checkpoint: Optional["MessageCheckpoint"] = Relationship(
        back_populates="message",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    token_usage: "TokenUsage" = Relationship(back_populates="message")
    display_components: list["MessageDisplayComponent"] = Relationship(
        back_populates="message",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    feedback: Optional["MessageFeedback"] = Relationship(
        back_populates="message",
        sa_relationship_kwargs={"cascade": "all, delete-orphan", "uselist": False},
    )
    created_at: datetime = Field(default_factory=datetime.now, index=True)
    updated_at: datetime = Field(default_factory=datetime.now, index=True)
    message_metadata: dict = Field(default={}, sa_column=Column(JSON))
    is_deleted: bool = Field(default=False, index=True)
    attachments: list["MessageAttachment"] = Relationship(back_populates="message")


class MessageAttachment(SQLModel, table=True):
    """Model for storing file attachments associated with messages"""

    __tablename__ = "message_attachments"  # type: ignore

    id: UUID = Field(default_factory=uuid4, primary_key=True)
    message_id: UUID | None = Field(
        default=None, foreign_key="message.id", ondelete="CASCADE", index=True
    )
    owner_id: UUID = Field(foreign_key="user.id", index=True)

    # File information
    filename: str = Field(max_length=255)
    original_filename: str = Field(max_length=255)
    file_type: str = Field(max_length=100)  # MIME type
    file_size: int = Field(description="File size in bytes")

    # Storage information
    storage_key: str = Field(max_length=500, description="Object storage key")
    thumbnail_key: str | None = Field(default=None, description="Thumbnail storage key")

    # Relationships
    message: Optional["Message"] = Relationship(back_populates="attachments")
    owner: "User" = Relationship(back_populates="attachments")

    # Audit
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class MessageDisplayComponent(SQLModel, table=True):
    """
    Model for storing display components (tables and charts) associated with messages
    """

    id: UUID = Field(default_factory=uuid4, primary_key=True)
    message_id: UUID = Field(foreign_key="message.id", ondelete="CASCADE")
    type: MessageDisplayComponentType
    chart_type: ChartType | None = Field(
        default=None, description="Specific type of chart when type is CHART"
    )
    title: str | None = Field(default=None, max_length=255)
    description: str | None = Field(default=None, sa_column=Column(Text))
    data: dict = Field(default={}, sa_column=Column(JSON))
    config: dict = Field(default={}, sa_column=Column(JSON))
    position: int = Field(default=0)  # Order of appearance in the message
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

    # Relationship
    message: "Message" = Relationship(back_populates="display_components")


class MessageDisplayComponentPublic(SQLModel):
    """
    Public schema for message display components
    """

    id: UUID
    type: MessageDisplayComponentType
    chart_type: ChartType | None
    title: str | None
    description: str | None
    data: dict
    config: dict
    position: int
    created_at: datetime


class MessageCheckpoint(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    message_id: uuid.UUID = Field(foreign_key="message.id", ondelete="CASCADE")
    message: "Message" = Relationship(back_populates="checkpoint")
    start_checkpoint_id: str | None = Field(default=None)
    end_checkpoint_id: str | None = Field(default=None)


class MessageAgentThoughtBase(SQLModel):
    position: int
    tool_name: str = Field(max_length=255)
    tool_input: dict = Field(default={}, sa_column=Column(JSON))
    tool_output: str = Field(sa_column=Column(Text))
    tool_runtime: float = Field(default=0, sa_column=Column(Float))
    content: str = Field(sa_column=Column(Text))
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class MessageAgentThought(MessageAgentThoughtBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    message_id: uuid.UUID = Field(
        foreign_key="message.id", ondelete="CASCADE", index=True
    )
    message: "Message" = Relationship(back_populates="thoughts")


class MessageAgentThoughtPublic(SQLModel):
    id: UUID
    position: int
    tool_name: str
    tool_input: dict
    tool_output: str
    tool_runtime: float = 0
    content: str
    created_at: datetime


class MessageAttachmentPublic(SQLModel):
    id: UUID
    filename: str
    original_filename: str
    file_type: str
    file_size: int
    storage_key: str
    thumbnail_key: str | None = None
    created_at: datetime


class MessagePublic(SQLModel):
    id: UUID
    content: str
    role: str
    is_interrupt: bool
    interrupt_message: str | None = None
    thoughts: list[MessageAgentThoughtPublic]
    display_components: list[MessageDisplayComponentPublic] | None = None
    attachments: list[MessageAttachmentPublic] | None = None
    created_at: datetime


class MessageStreamInput(SQLModel):
    content: str
    resume: bool
    approve: bool
    display_components: list[MessageDisplayComponentPublic] | None = None
    attachment_ids: list[uuid.UUID] | None = None
    resource_id: str | None = None


class MessagePublicList(BaseModel):
    messages: list[MessagePublic]
    resource_id: UUID | None = None
    has_report: bool = False
    has_dashboard: bool = False
    total: int
    is_streaming_active: bool = False
    stream_status: str = "completed"
    last_stream_position: int = 0


class FeedbackType(str, Enum):
    """
    Enumeration for feedback types on agent responses.
    """

    GOOD = "good"
    BAD = "bad"


class MessageFeedbackBase(SQLModel):
    """
    Base model for message feedback containing common attributes.
    """

    feedback_type: FeedbackType = Field(description="Type of feedback (good/bad)")
    reason: str | None = Field(
        default=None,
        max_length=1000,
        description="Optional reason for the feedback, required when feedback_type is BAD",
    )
    additional_comments: str | None = Field(
        default=None,
        max_length=2000,
        description="Additional optional comments from the user",
    )


class MessageFeedback(MessageFeedbackBase, table=True):
    """
    Model for storing user feedback on agent messages.

    Attributes:
        id: Unique identifier for the feedback record
        message_id: Reference to the message being rated
        user_id: Reference to the user who provided the feedback
        feedback_type: Whether the feedback is positive or negative
        reason: Optional reason for the feedback (required for bad feedback)
        additional_comments: Optional additional comments
        created_at: Timestamp when feedback was created
        updated_at: Timestamp when feedback was last updated
    """

    __tablename__ = "message_feedback"  # type: ignore

    id: UUID = Field(
        default_factory=uuid4,
        primary_key=True,
        title="ID",
        description="Unique identifier for the feedback record",
    )

    message_id: UUID = Field(
        foreign_key="message.id",
        index=True,
        title="Message ID",
        description="Reference to the message being rated",
    )

    user_id: UUID = Field(
        foreign_key="user.id",
        index=True,
        title="User ID",
        description="Reference to the user who provided the feedback",
    )

    created_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), server_default=func.now()),
        title="Created At",
        description="Timestamp when feedback was created",
    )

    updated_at: datetime = Field(
        sa_column=Column(
            DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
        ),
        title="Updated At",
        description="Timestamp when feedback was last updated",
    )

    # Relationships
    message: "Message" = Relationship(
        back_populates="feedback", sa_relationship_kwargs={"lazy": "joined"}
    )

    # Ensure one feedback per message
    __table_args__ = (UniqueConstraint("message_id", name="uq_message_feedback"),)


class MessageFeedbackCreate(MessageFeedbackBase):
    """Schema for creating message feedback"""

    message_id: UUID


class MessageFeedbackUpdate(SQLModel):
    """Schema for updating message feedback"""

    feedback_type: FeedbackType | None = None
    reason: str | None = None
    additional_comments: str | None = None


class MessageFeedbackPublic(MessageFeedbackBase):
    """Public schema for message feedback responses"""

    id: UUID
    message_id: UUID
    user_id: UUID
    created_at: datetime
    updated_at: datetime


class MessageFeedbackListResponse(SQLModel):
    """Response model for paginated message feedback list"""

    data: list[MessageFeedbackPublic]
    count: int
