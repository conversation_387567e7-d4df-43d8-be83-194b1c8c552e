from datetime import datetime
from enum import Enum
from uuid import UUID, uuid4

from sqlmodel import (
    Field,
    SQLModel,
)


class AlertSeverity(str, Enum):
    CRITICAL = "CRITICAL"
    HIGH = "HIGH"
    MEDIUM = "MEDIUM"
    LOW = "LOW"
    INFO = "INFO"


class AlertStatus(str, Enum):
    OPEN = "OPEN"
    ACKNOWLEDGED = "ACKNOWLEDGED"
    RESOLVED = "RESOLVED"
    CLOSED = "CLOSED"


class Alert(SQLModel, table=True):
    id: UUID = Field(default_factory=uuid4, primary_key=True)
    workspace_id: UUID | None = Field(
        default=None,
        foreign_key="workspace.id",
        index=True,
        nullable=True,
        ondelete="CASCADE",
    )
    severity: AlertSeverity = Field(...)
    title: str = Field(..., max_length=200)
    description: str = Field(...)
    status: AlertStatus = Field(default=AlertStatus.OPEN)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime | None = None
