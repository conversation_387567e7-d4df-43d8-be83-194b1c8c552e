import uuid
from datetime import datetime
from enum import <PERSON>r<PERSON><PERSON>
from typing import TYPE_CHECKING

from pydantic import BaseModel
from sqlmodel import (
    Column,
    Field,
    Relationship,
    SQLModel,
    Text,
)

from .builtin_tools import Agent<PERSON><PERSON>InTool
from .connections import AgentConnection

if TYPE_CHECKING:
    from .builtin_tools import WorkspaceBuiltInTool
    from .connections import Connection
    from .conversations import Conversation
    from .workspaces import Workspace


class AgentType(StrEnum):
    """Defines the supported types of agents in the system."""

    CONVERSATION_AGENT = "conversation_agent"
    AUTONOMOUS_AGENT = "autonomous_agent"


class AgentBase(SQLModel):
    """Base model for Agent containing common attributes."""

    alias: str = Field(
        min_length=1,
        max_length=255,
        description="The alias of the agent",
    )
    title: str = Field(
        min_length=1,
        max_length=255,
        index=True,
        description="The title/name of the agent",
    )
    role: str = Field(
        min_length=1,
        max_length=255,
        description="The role of the agent",
    )
    goal: str = Field(
        min_length=1,
        max_length=2048,
        description="The goal of the agent",
    )
    type: AgentType = Field(
        default=AgentType.CONVERSATION_AGENT,
        index=True,
        description="Type of the agent",
    )
    instructions: str | None = Field(
        default=None,
        sa_column=Column(Text),
        description="Custom instructions for the agent",
    )
    is_active: bool = Field(default=True, index=True)


class Agent(AgentBase, table=True):
    """Agent model with database configuration."""

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True, index=True)
    created_at: datetime = Field(default_factory=datetime.now, nullable=False)
    updated_at: datetime = Field(default_factory=datetime.now, nullable=False)
    is_deleted: bool = Field(default=False, index=True)

    # Relationships
    workspace_id: uuid.UUID = Field(
        foreign_key="workspace.id", index=True, ondelete="CASCADE"
    )
    workspace: "Workspace" = Relationship(
        back_populates="agents",
        sa_relationship_kwargs={"lazy": "joined", "cascade": "all"},
    )
    conversations: list["Conversation"] = Relationship(
        back_populates="agent",
        sa_relationship_kwargs={"cascade": "all, delete-orphan", "lazy": "selectin"},
    )
    builtin_tools: list["WorkspaceBuiltInTool"] = Relationship(
        back_populates="agents",
        link_model=AgentBuiltInTool,
        sa_relationship_kwargs={"lazy": "selectin"},
    )
    connections: list["Connection"] = Relationship(
        link_model=AgentConnection, sa_relationship_kwargs={"lazy": "selectin"}
    )


class StreamResponse(BaseModel):
    type: str
    content: str | None = None
    message_id: uuid.UUID | None = None
