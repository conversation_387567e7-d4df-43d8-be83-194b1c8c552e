import uuid
from datetime import datetime
from enum import Enum
from typing import TYPE_CHECKING

from sqlmodel import (
    Column,
    Field,
    Relationship,
    SQLModel,
    Text,
)

if TYPE_CHECKING:
    from .clouds import CloudProvider
    from .resources import Resource, ResourceCategory
    from .shared import PaginationMeta


class RecommendationStatus(str, Enum):
    PENDING = "pending"
    IMPLEMENTED = "implemented"
    IGNORED = "ignored"
    IN_PROGRESS = "in_progress"


class RecommendationBase(SQLModel):
    type: str
    title: str = Field(max_length=255)
    description: str = Field(sa_column=Column(Text))
    potential_savings: float
    effort: str = Field(max_length=50)
    risk: str = Field(max_length=50)
    status: RecommendationStatus = Field(
        default=RecommendationStatus.PENDING, index=True
    )


class RecommendationCreate(RecommendationBase):
    resource_id: uuid.UUID


class RecommendationUpdate(RecommendationBase):
    type: str | None = None  # type: ignore
    title: str | None = Field(default=None, max_length=255)  # type: ignore
    description: str | None = Field(default=None, max_length=1000)  # type: ignore
    potential_savings: float | None = None  # type: ignore
    effort: str | None = Field(default=None, max_length=50)  # type: ignore
    risk: str | None = Field(default=None, max_length=50)  # type: ignore
    status: RecommendationStatus | None = None  # type: ignore


class Recommendation(RecommendationBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    resource_id: uuid.UUID = Field(foreign_key="resource.id", ondelete="CASCADE")
    resource: "Resource" = Relationship(back_populates="recommendations")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    implemented_at: datetime | None = Field(default=None)
    implemented_by: uuid.UUID | None = Field(default=None, foreign_key="user.id")


class RecommendationPublic(RecommendationBase):
    id: uuid.UUID
    resource_id: uuid.UUID
    resource_name: str
    resource_type: str
    resource_provider: "CloudProvider"
    resource_category: "ResourceCategory"


class RecommendationOveralPublic(SQLModel):
    total_resource_scanned: int
    total_well_optimized: int
    total_optimization_opportunities: int
    total_estimated_saving_amount: float


class RecommendationsPublic(SQLModel):
    data: list[RecommendationPublic]
    meta: "PaginationMeta"

