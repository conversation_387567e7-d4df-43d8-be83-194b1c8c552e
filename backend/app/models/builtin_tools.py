import uuid
from datetime import datetime
from typing import TYPE_CHECKING
from uuid import UUI<PERSON>, uuid4

from sqlmodel import (
    Column,
    Field,
    Relationship,
    SQLModel,
    Text,
)

if TYPE_CHECKING:
    from .agents import Agent
    from .workspaces import Workspace


class BuiltInTool(SQLModel, table=True):
    id: UUID = Field(default_factory=uuid4, primary_key=True)
    name: str = Field(
        unique=True, index=True, description="Unique identifier for the connector"
    )
    display_name: str = Field(description="Human-readable name for the connector")
    description: str = Field(default="", sa_column=Column(Text))
    default_required_permission: bool = Field(default=False)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    workspaces_builtin_tools: list["WorkspaceBuiltInTool"] = Relationship(
        back_populates="builtin_tool",
        sa_relationship_kwargs={
            "primaryjoin": "WorkspaceBuiltInTool.builtin_tool_id == BuiltInTool.id",
            "cascade": "all, delete",
        },
    )


class BuiltinInstallRequest(SQLModel):
    """Request model for installing builtin connections with custom configuration"""

    config_override: dict | None = Field(
        default=None,
        description="Configuration override for the builtin connection (e.g., environment variables)",
    )

class AgentBuiltInTool(SQLModel, table=True):
    __tablename__ = "agent_builtin_tools"  # type: ignore

    agent_id: uuid.UUID = Field(
        foreign_key="agent.id", primary_key=True, ondelete="CASCADE"
    )
    workspace_builtin_tool_id: uuid.UUID = Field(
        foreign_key="workspacebuiltintool.id", primary_key=True, ondelete="CASCADE"
    )
    is_active: bool = Field(default=True, index=True)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)




class WorkspaceBuiltInTool(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    required_permission: bool = Field(default=False)
    workspace_id: uuid.UUID = Field(foreign_key="workspace.id", ondelete="CASCADE")
    builtin_tool_id: uuid.UUID = Field(foreign_key="builtintool.id", ondelete="CASCADE")

    # Relationships
    workspace: "Workspace" = Relationship(back_populates="builtin_tools")
    builtin_tool: "BuiltInTool" = Relationship(
        back_populates="workspaces_builtin_tools"
    )
    agents: list["Agent"] = Relationship(
        back_populates="builtin_tools",
        link_model=AgentBuiltInTool,
        sa_relationship_kwargs={"lazy": "selectin"},
    )

