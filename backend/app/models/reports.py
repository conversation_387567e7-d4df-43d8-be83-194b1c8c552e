import uuid
from datetime import UTC, datetime
from uuid import UUID

from sqlmodel import (
    JSON,
    Column,
    Field,
    SQLModel,
)


class Report(SQLModel, table=True):
    __tablename__ = "reports"  # type: ignore

    id: UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    conversation_id: UUID
    workspace_id: UUID
    title: str
    description: str | None = None
    sections: dict = Field(default={}, sa_column=Column(JSON))
    executive_summary: dict = Field(default={}, sa_column=Column(JSON))
    created_at: datetime = Field(default_factory=lambda: datetime.now(UTC))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(UTC))
