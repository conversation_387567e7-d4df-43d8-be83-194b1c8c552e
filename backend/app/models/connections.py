import uuid
from datetime import datetime
from enum import Enum
from typing import TYPE_CHECKING
from uuid import UUID

from sqlalchemy.dialects.postgresql import ARRAY as PostgreSQLArray
from sqlmodel import (
    JSON,
    Column,
    Field,
    Relationship,
    SQLModel,
    String,
)

if TYPE_CHECKING:
    from .agents import Agent
    from .workspaces import Workspace


class AgentConnection(SQLModel, table=True):
    __tablename__ = "agent_connection"  # type: ignore

    agent_id: uuid.UUID = Field(
        foreign_key="agent.id", primary_key=True, ondelete="CASCADE"
    )
    conn_id: uuid.UUID = Field(
        foreign_key="connection.id", primary_key=True, ondelete="CASCADE"
    )


class ConnectionConfig(SQLModel):
    url: str = Field(max_length=255)
    headers: dict = Field(default={})
    timeout: float = Field(default=5.0)
    sse_read_timeout: float = Field(default=30.0)


class ConnectionStatus(str, Enum):
    CONNECTED = "connected"
    ERROR = "error"


class ConnectionType(str, Enum):
    BUILTIN = "builtin"
    MCP = "mcp"
    CLOUD = "cloud"
    CLI = "cli"


class ConnectionTransport(str, Enum):
    STREAMABLE_HTTP = "streamable_http"
    SSE = "sse"


class ConnectionBase(SQLModel):
    name: str = Field(max_length=255)
    prefix: str = Field(max_length=255)
    type: ConnectionType = Field(default=ConnectionType.MCP)
    transport_type: ConnectionTransport = Field(
        default=ConnectionTransport.STREAMABLE_HTTP
    )
    config: dict = Field(
        default={}, sa_column=Column(JSON)
    )  # Include: url, headers, timeout, sse_read_timeout, etc.
    is_active: bool = Field(default=True, index=True)
    tool_list: list[str] = Field(default=[], sa_column=Column(PostgreSQLArray(String)))
    tool_permissions: list[str] = Field(
        default=[], sa_column=Column(PostgreSQLArray(String))
    )
    tool_enabled: list[str] = Field(
        default=[], sa_column=Column(PostgreSQLArray(String))
    )
    # tools: list[dict] = Field(
    #     default={},
    #     sa_column=Column(JSON),
    #     description="Tool configuration: [{name: str, enabled: bool, required_permission: bool}]"
    # )
    tool_schemas: list[dict] = Field(default=[], sa_column=Column(JSON))
    # Status
    status: ConnectionStatus = Field(default=ConnectionStatus.CONNECTED)
    status_message: str = Field(default="")
    status_updated_at: datetime = Field(default=datetime.now())


class ConnectionCreate(ConnectionBase):
    pass


class ConnectionUpdate(SQLModel):
    name: str | None = None
    prefix: str | None = None
    type: ConnectionType | None = None
    transport_type: ConnectionTransport | None = None
    config: dict | None = None
    is_active: bool | None = None
    tool_list: list[str] | None = None
    tool_permissions: list[str] | None = None
    tool_enabled: list[str] | None = None
    tool_schemas: list[dict] | None = None
    status: ConnectionStatus | None = None
    status_message: str | None = None
    status_updated_at: datetime | None = None


class Connection(ConnectionBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    workspace_id: uuid.UUID | None = Field(
        foreign_key="workspace.id", ondelete="CASCADE", index=True
    )
    workspace: "Workspace" = Relationship(back_populates="connections")
    agents: list["Agent"] = Relationship(
        link_model=AgentConnection, sa_relationship_kwargs={"lazy": "selectin"}
    )
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class ConnectionStatusResponse(SQLModel):
    status: ConnectionStatus
    status_message: str
    tool_list: list[str]


class ConnectionPublic(SQLModel):
    name: str
    prefix: str
    type: ConnectionType
    transport_type: ConnectionTransport
    config: dict
    is_active: bool
    tool_list: list[str]
    tool_permissions: list[str]
    tool_enabled: list[str]
    status: ConnectionStatus
    status_message: str
    status_updated_at: datetime
    id: UUID
    workspace_id: UUID | None
    created_at: datetime
    updated_at: datetime


class ConnectionsPublic(SQLModel):
    data: list[ConnectionPublic]
    count: int


class ConnectionUpdateParams(SQLModel):
    """Parameter object for connection update operations"""

    conn_id: UUID
    workspace_id: UUID
    connection_data: ConnectionUpdate | None = None


class ConnectionTestParams(SQLModel):
    """Parameter object for connection test operations"""

    server_id: UUID
    workspace_id: UUID
    updates: ConnectionUpdate | None = None


class ConnectionTestResult(SQLModel):
    """Result object for connection test operations"""

    status: ConnectionStatus | None = None
    status_message: str | None = None
    tool_list: list[str] | None = None
    tool_schemas: list[dict] | None = None
    status_updated_at: datetime | None = None
