
from datetime import datetime
from enum import Enum
from typing import TYPE_CHECKING
from uuid import UUID, uuid4

from sqlmodel import (
    JSON,
    Column,
    Field,
    Relationship,
    SQLModel,
    Text,
)

if TYPE_CHECKING:
    from .users import User


class NotificationType(str, Enum):
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    INTERRUPT = "interrupt"


class NotificationStatus(str, Enum):
    UNREAD = "unread"
    READ = "read"
    ARCHIVED = "archived"


class NotificationBase(SQLModel):
    title: str = Field(max_length=255)
    message: str = Field(sa_column=Column(Text))
    type: NotificationType = Field(default=NotificationType.INFO)
    status: NotificationStatus = Field(default=NotificationStatus.UNREAD)
    notification_metadata: dict = Field(
        default_factory=dict,
        sa_column=Column(JSON),
        description="Metadata for the notification",
    )
    requires_action: bool = Field(default=False)
    action_url: str | None = Field(default=None, description="URL for direct action")


class Notification(NotificationBase, table=True):
    id: UUID = Field(default_factory=uuid4, primary_key=True)
    user_id: UUID = Field(foreign_key="user.id", index=True)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    read_at: datetime | None = None
    expires_at: datetime | None = None
    message_id: UUID | None = Field(default=None, description="Message ID")
    # Relationships
    user: "User" = Relationship(back_populates="notifications")


class NotificationCreate(NotificationBase):
    user_id: UUID
    message_id: UUID | None = None


class NotificationUpdate(SQLModel):
    title: str | None = None
    message: str | None = None
    type: NotificationType | None = None
    status: NotificationStatus | None = None
    notification_metadata: dict | None = None
    requires_action: bool | None = None
    expires_at: datetime | None = None


class NotificationResponse(NotificationBase):
    id: UUID
    user_id: UUID
    created_at: datetime
    updated_at: datetime
    read_at: datetime | None = None
    expires_at: datetime | None = None


class NotificationList(SQLModel):
    """Response model for paginated notifications list.

    Attributes:
        data: List of notification items
        count: Total number of items available (before pagination)
    """

    data: list[NotificationResponse]
    count: int  # Changed from total to count for consistency

