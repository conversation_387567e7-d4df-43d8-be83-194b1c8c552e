import uuid
from datetime import datetime
from enum import Enum
from typing import TYPE_CHECKING

from sqlmodel import (
    JSON,
    Column,
    Field,
    Relationship,
    SQLModel,
)

from .clouds import CloudProvider

if TYPE_CHECKING:
    from .conversations import Conversation
    from .recommendations import Recommendation
    from .shared import PaginationMeta
    from .workspaces import Workspace


class ResourceStatus(str, Enum):
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    FOUND = "found"
    DELETED = "deleted"


# Cloud-agnostic resource categories
class ResourceCategory(str, Enum):
    COMPUTE = "COMPUTE"
    DATABASE = "DATABASE"
    STORAGE = "STORAGE"
    NETWORKING = "NETWORKING"
    SERVERLESS = "SERVERLESS"
    CONTAINER = "CONTAINER"
    MESSAGING = "MESSAGING"
    MONITORING = "MONITORING"
    SECURITY = "SECURITY"
    MANAGEMENT = "MANAGEMENT"
    ANALYTICS = "ANALYTICS"
    AI_ML = "AI_ML"
    OTHER = "OTHER"


# AWS-specific resource types
class AWSResourceType(str, Enum):
    # Compute Services
    EC2 = "EC2"
    LAMBDA = "LAMBDA"
    ECS = "ECS"
    EKS = "EKS"
    BATCH = "BATCH"
    EC2_AUTO_SCALING = "EC2_AUTO_SCALING"
    ELASTIC_BEANSTALK = "ELASTIC_BEANSTALK"
    APP_RUNNER = "APP_RUNNER"

    # Database Services
    RDS = "RDS"
    DYNAMODB = "DYNAMODB"
    ELASTICACHE = "ELASTICACHE"
    NEPTUNE = "NEPTUNE"
    DOCUMENTDB = "DOCUMENTDB"
    OPENSEARCH = "OPENSEARCH"
    REDSHIFT = "REDSHIFT"

    # Storage Services
    S3 = "S3"
    EBS = "EBS"
    EFS = "EFS"
    BACKUP = "BACKUP"

    # Networking & Content Delivery
    VPC = "VPC"
    ELB = "ELB"

    # Management & Governance
    CLOUDFORMATION = "CLOUDFORMATION"
    CLOUDWATCH = "CLOUDWATCH"
    SQS = "SQS"
    SNS = "SNS"


# GCP-specific resource types
class GCPResourceType(str, Enum):
    # Compute Services
    COMPUTE_ENGINE = "COMPUTE_ENGINE"
    CLOUD_FUNCTIONS = "CLOUD_FUNCTIONS"
    CLOUD_RUN = "CLOUD_RUN"
    GKE = "GKE"
    APP_ENGINE = "APP_ENGINE"

    # Database Services
    CLOUD_SQL = "CLOUD_SQL"
    FIRESTORE = "FIRESTORE"
    BIGTABLE = "BIGTABLE"
    SPANNER = "SPANNER"
    MEMORYSTORE = "MEMORYSTORE"

    # Storage Services
    CLOUD_STORAGE = "CLOUD_STORAGE"
    PERSISTENT_DISK = "PERSISTENT_DISK"
    FILESTORE = "FILESTORE"

    # Networking
    VPC_NETWORK = "VPC_NETWORK"
    LOAD_BALANCER = "LOAD_BALANCER"

    # Management & Monitoring
    CLOUD_MONITORING = "CLOUD_MONITORING"
    CLOUD_LOGGING = "CLOUD_LOGGING"
    PUB_SUB = "PUB_SUB"


# Azure-specific resource types
class AzureResourceType(str, Enum):
    # Compute Services
    VIRTUAL_MACHINES = "VIRTUAL_MACHINES"
    AZURE_FUNCTIONS = "AZURE_FUNCTIONS"
    CONTAINER_INSTANCES = "CONTAINER_INSTANCES"
    AKS = "AKS"
    APP_SERVICE = "APP_SERVICE"

    # Database Services
    SQL_DATABASE = "SQL_DATABASE"
    COSMOS_DB = "COSMOS_DB"
    REDIS_CACHE = "REDIS_CACHE"
    POSTGRESQL = "POSTGRESQL"
    MYSQL = "MYSQL"

    # Storage Services
    BLOB_STORAGE = "BLOB_STORAGE"
    DISK_STORAGE = "DISK_STORAGE"
    FILE_STORAGE = "FILE_STORAGE"

    # Networking
    VIRTUAL_NETWORK = "VIRTUAL_NETWORK"
    LOAD_BALANCER = "LOAD_BALANCER"
    APPLICATION_GATEWAY = "APPLICATION_GATEWAY"

    # Management & Monitoring
    MONITOR = "MONITOR"
    LOG_ANALYTICS = "LOG_ANALYTICS"
    SERVICE_BUS = "SERVICE_BUS"


# Resource type mapping utilities
class ResourceTypeMapping:
    """Utility class for mapping cloud-specific resource types to categories"""

    AWS_TYPE_TO_CATEGORY = {
        # Compute
        AWSResourceType.EC2: ResourceCategory.COMPUTE,
        AWSResourceType.LAMBDA: ResourceCategory.SERVERLESS,
        AWSResourceType.ECS: ResourceCategory.CONTAINER,
        AWSResourceType.EKS: ResourceCategory.CONTAINER,
        AWSResourceType.BATCH: ResourceCategory.COMPUTE,
        AWSResourceType.EC2_AUTO_SCALING: ResourceCategory.COMPUTE,
        AWSResourceType.ELASTIC_BEANSTALK: ResourceCategory.COMPUTE,
        AWSResourceType.APP_RUNNER: ResourceCategory.SERVERLESS,
        # Database
        AWSResourceType.RDS: ResourceCategory.DATABASE,
        AWSResourceType.DYNAMODB: ResourceCategory.DATABASE,
        AWSResourceType.ELASTICACHE: ResourceCategory.DATABASE,
        AWSResourceType.NEPTUNE: ResourceCategory.DATABASE,
        AWSResourceType.DOCUMENTDB: ResourceCategory.DATABASE,
        AWSResourceType.OPENSEARCH: ResourceCategory.ANALYTICS,
        AWSResourceType.REDSHIFT: ResourceCategory.ANALYTICS,
        # Storage
        AWSResourceType.S3: ResourceCategory.STORAGE,
        AWSResourceType.EBS: ResourceCategory.STORAGE,
        AWSResourceType.EFS: ResourceCategory.STORAGE,
        AWSResourceType.BACKUP: ResourceCategory.STORAGE,
        # Networking
        AWSResourceType.VPC: ResourceCategory.NETWORKING,
        AWSResourceType.ELB: ResourceCategory.NETWORKING,
        # Management & Messaging
        AWSResourceType.CLOUDFORMATION: ResourceCategory.MANAGEMENT,
        AWSResourceType.CLOUDWATCH: ResourceCategory.MONITORING,
        AWSResourceType.SQS: ResourceCategory.MESSAGING,
        AWSResourceType.SNS: ResourceCategory.MESSAGING,
    }

    GCP_TYPE_TO_CATEGORY = {
        # Compute
        GCPResourceType.COMPUTE_ENGINE: ResourceCategory.COMPUTE,
        GCPResourceType.CLOUD_FUNCTIONS: ResourceCategory.SERVERLESS,
        GCPResourceType.CLOUD_RUN: ResourceCategory.SERVERLESS,
        GCPResourceType.GKE: ResourceCategory.CONTAINER,
        GCPResourceType.APP_ENGINE: ResourceCategory.SERVERLESS,
        # Database
        GCPResourceType.CLOUD_SQL: ResourceCategory.DATABASE,
        GCPResourceType.FIRESTORE: ResourceCategory.DATABASE,
        GCPResourceType.BIGTABLE: ResourceCategory.DATABASE,
        GCPResourceType.SPANNER: ResourceCategory.DATABASE,
        GCPResourceType.MEMORYSTORE: ResourceCategory.DATABASE,
        # Storage
        GCPResourceType.CLOUD_STORAGE: ResourceCategory.STORAGE,
        GCPResourceType.PERSISTENT_DISK: ResourceCategory.STORAGE,
        GCPResourceType.FILESTORE: ResourceCategory.STORAGE,
        # Networking
        GCPResourceType.VPC_NETWORK: ResourceCategory.NETWORKING,
        GCPResourceType.LOAD_BALANCER: ResourceCategory.NETWORKING,
        # Management & Messaging
        GCPResourceType.CLOUD_MONITORING: ResourceCategory.MONITORING,
        GCPResourceType.CLOUD_LOGGING: ResourceCategory.MONITORING,
        GCPResourceType.PUB_SUB: ResourceCategory.MESSAGING,
    }

    AZURE_TYPE_TO_CATEGORY = {
        # Compute
        AzureResourceType.VIRTUAL_MACHINES: ResourceCategory.COMPUTE,
        AzureResourceType.AZURE_FUNCTIONS: ResourceCategory.SERVERLESS,
        AzureResourceType.CONTAINER_INSTANCES: ResourceCategory.CONTAINER,
        AzureResourceType.AKS: ResourceCategory.CONTAINER,
        AzureResourceType.APP_SERVICE: ResourceCategory.SERVERLESS,
        # Database
        AzureResourceType.SQL_DATABASE: ResourceCategory.DATABASE,
        AzureResourceType.COSMOS_DB: ResourceCategory.DATABASE,
        AzureResourceType.REDIS_CACHE: ResourceCategory.DATABASE,
        AzureResourceType.POSTGRESQL: ResourceCategory.DATABASE,
        AzureResourceType.MYSQL: ResourceCategory.DATABASE,
        # Storage
        AzureResourceType.BLOB_STORAGE: ResourceCategory.STORAGE,
        AzureResourceType.DISK_STORAGE: ResourceCategory.STORAGE,
        AzureResourceType.FILE_STORAGE: ResourceCategory.STORAGE,
        # Networking
        AzureResourceType.VIRTUAL_NETWORK: ResourceCategory.NETWORKING,
        AzureResourceType.LOAD_BALANCER: ResourceCategory.NETWORKING,
        AzureResourceType.APPLICATION_GATEWAY: ResourceCategory.NETWORKING,
        # Management & Messaging
        AzureResourceType.MONITOR: ResourceCategory.MONITORING,
        AzureResourceType.LOG_ANALYTICS: ResourceCategory.MONITORING,
        AzureResourceType.SERVICE_BUS: ResourceCategory.MESSAGING,
    }

    @classmethod
    def get_category_for_type(
        cls, provider: CloudProvider, resource_type: str
    ) -> ResourceCategory:
        """Get resource category based on provider and resource type"""
        mapping = {
            CloudProvider.AWS: cls.AWS_TYPE_TO_CATEGORY,
            CloudProvider.GCP: cls.GCP_TYPE_TO_CATEGORY,
            CloudProvider.AZURE: cls.AZURE_TYPE_TO_CATEGORY,
        }.get(provider, {})

        return mapping.get(resource_type, ResourceCategory.OTHER)  # type: ignore


class ResourceBase(SQLModel):
    name: str = Field(min_length=1, max_length=255)
    resource_id: str = Field(
        max_length=2048,
        index=True,
        description="Cloud-specific resource identifier (ARN for AWS, Resource ID for GCP/Azure)",
    )
    tags: dict = Field(default={}, sa_column=Column(JSON))
    configurations: dict = Field(default={}, sa_column=Column(JSON))
    description: str | None = Field(default=None, max_length=1000)
    type: str = Field(
        nullable=False, index=True, description="Cloud-specific resource type"
    )
    category: ResourceCategory = Field(
        nullable=True, index=True, description="Cloud-agnostic resource category"
    )
    region: str = Field(max_length=50, index=True)
    status: ResourceStatus = Field(default=ResourceStatus.FOUND)


class ResourceCreate(ResourceBase):
    workspace_id: uuid.UUID
    provider: "CloudProvider" = Field(nullable=False, description="Cloud provider")


class Resource(ResourceBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    workspace_id: uuid.UUID = Field(
        foreign_key="workspace.id", ondelete="CASCADE", index=True
    )
    provider: "CloudProvider" = Field(nullable=False, index=True)
    workspace: "Workspace" = Relationship(back_populates="resources")
    recommendations: list["Recommendation"] = Relationship(
        back_populates="resource",
        sa_relationship_kwargs={"cascade": "all, delete-orphan", "lazy": "selectin"},
    )
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    is_active: bool = Field(default=True)
    conversations: list["Conversation"] = Relationship(
        back_populates="resource",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )

    def get_resource_context(self) -> str:
        return f"""
        Resource Name: {self.name}
        Resource ID: {self.resource_id}
        Cloud Provider: {self.provider}
        Resource Type: {self.type}
        Resource Category: {self.category}
        Resource Region: {self.region}
        Resource Description: {self.description}
        Resource Status: {self.status}
        Resource Tags: {self.tags}
        Resource Configurations: {self.configurations}
        """


class ResourcePublic(SQLModel):
    id: uuid.UUID
    resource_id: str
    name: str
    region: str
    provider: "CloudProvider"
    category: "ResourceCategory"
    type: str
    status: ResourceStatus
    total_recommendation: int
    total_potential_saving: float
    updated_at: datetime


class ResourcePublicSimple(SQLModel):
    id: uuid.UUID
    name: str


class ResourcesPublic(SQLModel):
    data: list[ResourcePublic]
    meta: "PaginationMeta"


class ResourceTypeInfo(SQLModel):
    resource_type: str
    category: "ResourceCategory"
    display_name: str
    description: str


class ResourceTypesResponse(SQLModel):
    cloud_provider: "CloudProvider"
    resource_types: list[ResourceTypeInfo]




# Resource Type Mappings for Cloud Sync Configuration
RESOURCE_TYPE_MAPPINGS = {
    CloudProvider.AWS: {
        AWSResourceType.EC2: ResourceTypeInfo(
            resource_type="EC2",
            category=ResourceCategory.COMPUTE,
            display_name="EC2 Instances",
            description="Virtual compute instances",
        ),
        AWSResourceType.RDS: ResourceTypeInfo(
            resource_type="RDS",
            category=ResourceCategory.DATABASE,
            display_name="RDS Databases",
            description="Managed database instances",
        ),
        AWSResourceType.LAMBDA: ResourceTypeInfo(
            resource_type="LAMBDA",
            category=ResourceCategory.SERVERLESS,
            display_name="Lambda Functions",
            description="Serverless functions",
        ),
        AWSResourceType.ECS: ResourceTypeInfo(
            resource_type="ECS",
            category=ResourceCategory.CONTAINER,
            display_name="ECS Services",
            description="Container services",
        ),
        AWSResourceType.EKS: ResourceTypeInfo(
            resource_type="EKS",
            category=ResourceCategory.CONTAINER,
            display_name="EKS Clusters",
            description="Kubernetes clusters",
        ),
        AWSResourceType.S3: ResourceTypeInfo(
            resource_type="S3",
            category=ResourceCategory.STORAGE,
            display_name="S3 Buckets",
            description="Object storage buckets",
        ),
        AWSResourceType.VPC: ResourceTypeInfo(
            resource_type="VPC",
            category=ResourceCategory.NETWORKING,
            display_name="VPC Networks",
            description="Virtual private clouds",
        ),
        AWSResourceType.DYNAMODB: ResourceTypeInfo(
            resource_type="DYNAMODB",
            category=ResourceCategory.DATABASE,
            display_name="DynamoDB Tables",
            description="NoSQL database tables",
        ),
        AWSResourceType.ELASTICACHE: ResourceTypeInfo(
            resource_type="ELASTICACHE",
            category=ResourceCategory.DATABASE,
            display_name="ElastiCache Clusters",
            description="In-memory caching clusters",
        ),
        AWSResourceType.ELB: ResourceTypeInfo(
            resource_type="ELB",
            category=ResourceCategory.NETWORKING,
            display_name="Load Balancers",
            description="Application and network load balancers",
        ),
        AWSResourceType.CLOUDWATCH: ResourceTypeInfo(
            resource_type="CLOUDWATCH",
            category=ResourceCategory.MONITORING,
            display_name="CloudWatch Resources",
            description="Monitoring and logging resources",
        ),
        AWSResourceType.SQS: ResourceTypeInfo(
            resource_type="SQS",
            category=ResourceCategory.MESSAGING,
            display_name="SQS Queues",
            description="Message queues",
        ),
        AWSResourceType.SNS: ResourceTypeInfo(
            resource_type="SNS",
            category=ResourceCategory.MESSAGING,
            display_name="SNS Topics",
            description="Notification topics",
        ),
    },
    CloudProvider.GCP: {
        GCPResourceType.COMPUTE_ENGINE: ResourceTypeInfo(
            resource_type="COMPUTE_ENGINE",
            category=ResourceCategory.COMPUTE,
            display_name="Compute Engine Instances",
            description="Virtual machine instances",
        ),
        GCPResourceType.CLOUD_FUNCTIONS: ResourceTypeInfo(
            resource_type="CLOUD_FUNCTIONS",
            category=ResourceCategory.SERVERLESS,
            display_name="Cloud Functions",
            description="Serverless functions",
        ),
        GCPResourceType.CLOUD_RUN: ResourceTypeInfo(
            resource_type="CLOUD_RUN",
            category=ResourceCategory.SERVERLESS,
            display_name="Cloud Run Services",
            description="Containerized serverless services",
        ),
        GCPResourceType.GKE: ResourceTypeInfo(
            resource_type="GKE",
            category=ResourceCategory.CONTAINER,
            display_name="GKE Clusters",
            description="Kubernetes clusters",
        ),
        GCPResourceType.CLOUD_SQL: ResourceTypeInfo(
            resource_type="CLOUD_SQL",
            category=ResourceCategory.DATABASE,
            display_name="Cloud SQL Instances",
            description="Managed SQL database instances",
        ),
        GCPResourceType.CLOUD_STORAGE: ResourceTypeInfo(
            resource_type="CLOUD_STORAGE",
            category=ResourceCategory.STORAGE,
            display_name="Cloud Storage Buckets",
            description="Object storage buckets",
        ),
        GCPResourceType.VPC_NETWORK: ResourceTypeInfo(
            resource_type="VPC_NETWORK",
            category=ResourceCategory.NETWORKING,
            display_name="VPC Networks",
            description="Virtual private cloud networks",
        ),
        GCPResourceType.LOAD_BALANCER: ResourceTypeInfo(
            resource_type="LOAD_BALANCER",
            category=ResourceCategory.NETWORKING,
            display_name="Load Balancers",
            description="HTTP(S) and network load balancers",
        ),
        GCPResourceType.PUB_SUB: ResourceTypeInfo(
            resource_type="PUB_SUB",
            category=ResourceCategory.MESSAGING,
            display_name="Pub/Sub Topics",
            description="Messaging topics and subscriptions",
        ),
    },
    CloudProvider.AZURE: {
        AzureResourceType.VIRTUAL_MACHINES: ResourceTypeInfo(
            resource_type="VIRTUAL_MACHINES",
            category=ResourceCategory.COMPUTE,
            display_name="Virtual Machines",
            description="Virtual machine instances",
        ),
        AzureResourceType.AZURE_FUNCTIONS: ResourceTypeInfo(
            resource_type="AZURE_FUNCTIONS",
            category=ResourceCategory.SERVERLESS,
            display_name="Azure Functions",
            description="Serverless functions",
        ),
        AzureResourceType.AKS: ResourceTypeInfo(
            resource_type="AKS",
            category=ResourceCategory.CONTAINER,
            display_name="AKS Clusters",
            description="Azure Kubernetes Service clusters",
        ),
        AzureResourceType.SQL_DATABASE: ResourceTypeInfo(
            resource_type="SQL_DATABASE",
            category=ResourceCategory.DATABASE,
            display_name="SQL Databases",
            description="Azure SQL database instances",
        ),
        AzureResourceType.BLOB_STORAGE: ResourceTypeInfo(
            resource_type="BLOB_STORAGE",
            category=ResourceCategory.STORAGE,
            display_name="Blob Storage",
            description="Object storage containers",
        ),
        AzureResourceType.VIRTUAL_NETWORK: ResourceTypeInfo(
            resource_type="VIRTUAL_NETWORK",
            category=ResourceCategory.NETWORKING,
            display_name="Virtual Networks",
            description="Virtual network infrastructure",
        ),
        AzureResourceType.LOAD_BALANCER: ResourceTypeInfo(
            resource_type="LOAD_BALANCER",
            category=ResourceCategory.NETWORKING,
            display_name="Load Balancers",
            description="Application and network load balancers",
        ),
        AzureResourceType.SERVICE_BUS: ResourceTypeInfo(
            resource_type="SERVICE_BUS",
            category=ResourceCategory.MESSAGING,
            display_name="Service Bus",
            description="Enterprise messaging service",
        ),
    },
}
