from datetime import datetime
from typing import TYPE_CHECKING
from uuid import UUID, uuid4

from pydantic import field_validator
from sqlmodel import (
    Column,
    DateTime,
    Field,
    Relationship,
    SQLModel,
    func,
)

if TYPE_CHECKING:
    from .messages import Message


class TokenUsage(SQLModel, table=True):
    """
    Tracks token usage for individual messages in conversations.

    Attributes:
        id: Unique identifier for the token usage record
        message_id: Reference to the associated message
        input_tokens: Number of tokens in the input message
        output_tokens: Number of tokens in the output message
        model_id: Identifier for the AI model used
        model_provider: Provider of the AI model used
        created_at: Timestamp of record creation
        updated_at: Timestamp of last update
    """

    __tablename__ = "token_usage"  # type: ignore

    id: UUID = Field(
        default_factory=uuid4,
        primary_key=True,
        title="ID",
        description="Unique identifier for the token usage record",
    )

    message_id: UUID = Field(
        foreign_key="message.id",
        index=True,
        title="Message ID",
        description="Reference to the associated message",
    )

    message: "Message" = Relationship(
        back_populates="token_usage", sa_relationship_kwargs={"lazy": "joined"}
    )

    input_tokens: int = Field(
        ge=0, title="Input Tokens", description="Number of tokens in the input message"
    )

    output_tokens: int = Field(
        ge=0,
        title="Output Tokens",
        description="Number of tokens in the output message",
    )

    model_provider: str = Field(
        min_length=1,
        title="Model Provider",
        description="Provider of the AI model used",
    )

    model_id: str = Field(
        min_length=1, title="Model ID", description="Identifier for the AI model used"
    )

    workspace_id: UUID = Field(
        foreign_key="workspace.id", index=True, title="Workspace ID", ondelete="CASCADE"
    )

    created_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), server_default=func.now()),
        title="Created At",
        description="Timestamp of record creation",
    )

    updated_at: datetime = Field(
        sa_column=Column(
            DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
        ),
        title="Updated At",
        description="Timestamp when feedback was last updated",
    )

    @field_validator("input_tokens", "output_tokens")
    def validate_token_counts(cls, v):
        if v < 0:
            raise ValueError("Token count cannot be negative")
        return v

    def get_total_tokens(self) -> int:
        """Calculate total tokens used."""
        return self.input_tokens + self.output_tokens
