import uuid
from datetime import UTC, datetime
from uuid import UUID

from sqlmodel import (
    JSON,
    Column,
    Field,
    SQLModel,
)


class Dashboard(SQLModel, table=True):
    __tablename__ = "dashboards"  # type: ignore

    id: UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    conversation_id: UUID
    workspace_id: UUID
    title: str
    grid_config: dict = Field(default={"columns": 12}, sa_column=Column(JSON))
    widgets: list = Field(default=[], sa_column=Column(JSON))
    created_at: datetime = Field(default_factory=lambda: datetime.now(UTC))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(UTC))
