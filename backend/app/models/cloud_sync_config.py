from datetime import datetime
from typing import TYPE_CHECKING
from uuid import UUID, uuid4

from sqlalchemy.dialects.postgresql import ARRAY as PostgreSQLArray
from sqlmodel import (
    Column,
    Field,
    Relationship,
    SQLModel,
    String,
    UniqueConstraint,
)

if TYPE_CHECKING:
    from .connections import Connection
    from .workspaces import Workspace


# Cloud Sync Configuration Models
class CloudSyncConfigBase(SQLModel):
    include_stopped_resources: bool = Field(default=False)
    refresh_interval: int = Field(default=60, ge=1, le=10080)  # 1 min to 1 week
    selected_resources: list[str] = Field(
        default=[], sa_column=Column(PostgreSQLArray(String))
    )
    is_enabled: bool = Field(default=True)


class CloudSyncConfig(CloudSyncConfigBase, table=True):
    __tablename__ = "cloud_sync_config"  # type: ignore

    id: UUID = Field(default_factory=uuid4, primary_key=True)
    workspace_id: UUID = Field(
        foreign_key="workspace.id", ondelete="CASCADE", index=True
    )
    connection_id: UUID = Field(
        foreign_key="connection.id", ondelete="CASCADE", index=True
    )
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    last_sync_at: datetime | None = Field(
        default=None, description="Timestamp of last sync execution"
    )

    # Relationships
    workspace: "Workspace" = Relationship()
    connection: "Connection" = Relationship()

    # Unique constraint on workspace_id + connection_id
    __table_args__ = (
        UniqueConstraint(
            "workspace_id", "connection_id", name="uq_workspace_connection_sync_config"
        ),
    )


class CloudSyncConfigCreate(CloudSyncConfigBase):
    connection_id: UUID


class CloudSyncConfigUpdate(SQLModel):
    include_stopped_resources: bool | None = None
    refresh_interval: int | None = Field(default=None, ge=1, le=10080)
    selected_resources: list[str] | None = None
    is_enabled: bool | None = None


class CloudSyncConfigPublic(CloudSyncConfigBase):
    id: UUID
    workspace_id: UUID
    connection_id: UUID
    created_at: datetime
    updated_at: datetime
    last_sync_at: datetime | None = None
