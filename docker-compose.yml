services:
  db:
    image: postgres:12
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      retries: 5
      start_period: 30s
      timeout: 10s
    volumes:
      - app-db-data:/var/lib/postgresql/data/pgdata
    env_file:
      - .env
    environment:
      - PGDATA=/var/lib/postgresql/data/pgdata
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD?Variable not set}
      - POSTGRES_USER=${POSTGRES_USER?Variable not set}
      - POSTGRES_DB=${POSTGRES_DB?Variable not set}

  sandbox-db: # For sandboxed environment
    image: postgres:12
    restart: always
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "pg_isready -U ${SANDBOX_POSTGRES_USER} -d ${SANDBOX_POSTGRES_DB}",
        ]
      interval: 10s
      retries: 5
      start_period: 30s
      timeout: 10s
    volumes:
      - sandbox-db-data:/var/lib/postgresql/data/pgdata
    env_file:
      - .env
    environment:
      - PGDATA=/var/lib/postgresql/data/pgdata
      - POSTGRES_PASSWORD=${SANDBOX_POSTGRES_PASSWORD}
      - POSTGRES_USER=${SANDBOX_POSTGRES_USER}
      - POSTGRES_DB=${SANDBOX_POSTGRES_DB}

  redis:
    image: redis:latest
    restart: always
    volumes:
      - redis-data:/data

  connection-manager:
    image: "${DOCKER_IMAGE_CONNECTION_MANAGER?Variable not set}:${TAG-latest}"
    restart: always
    build:
      context: ./connection-manager
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 10s
      timeout: 5s
      retries: 5
    depends_on:
      sandbox-db:
        condition: service_healthy
        restart: true

  prestart:
    image: "${DOCKER_IMAGE_BACKEND?Variable not set}:${TAG-latest}"
    build:
      context: ./backend
    networks:
      - default
    depends_on:
      db:
        condition: service_healthy
        restart: true
      sandbox-db:
        condition: service_healthy
        restart: true
      connection-manager:
        condition: service_healthy
        restart: true
    command: bash scripts/prestart.sh
    env_file:
      - .env
    environment:
      - DOMAIN=${DOMAIN}
      - FRONTEND_HOST=${FRONTEND_HOST?Variable not set}
      - ENVIRONMENT=${ENVIRONMENT}
      - BACKEND_CORS_ORIGINS=${BACKEND_CORS_ORIGINS}
      - SECRET_KEY=${SECRET_KEY?Variable not set}
      - FIRST_SUPERUSER=${FIRST_SUPERUSER?Variable not set}
      - FIRST_SUPERUSER_PASSWORD=${FIRST_SUPERUSER_PASSWORD?Variable not set}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - EMAILS_FROM_EMAIL=${EMAILS_FROM_EMAIL}
      - POSTGRES_SERVER=db
      - POSTGRES_PORT=${POSTGRES_PORT}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER?Variable not set}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD?Variable not set}
      - SENTRY_DSN=${SENTRY_DSN?Variable not set}

  backend:
    image: "${DOCKER_IMAGE_BACKEND?Variable not set}:${TAG-latest}"
    restart: always
    deploy:
      mode: replicated
      replicas: 1
    networks:
      - default
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
        restart: true
      prestart:
        condition: service_completed_successfully
      redis:
        condition: service_started
      connection-manager:
        condition: service_started
    env_file:
      - .env
    environment:
      - LANGFUSE_TRACING_ENVIRONMENT=${ENVIRONMENT}
      - DOMAIN=${DOMAIN}
      - FRONTEND_HOST=${FRONTEND_HOST?Variable not set}
      - ENVIRONMENT=${ENVIRONMENT}
      - BACKEND_CORS_ORIGINS=${BACKEND_CORS_ORIGINS}
      - SECRET_KEY=${SECRET_KEY?Variable not set}
      - FIRST_SUPERUSER=${FIRST_SUPERUSER?Variable not set}
      - FIRST_SUPERUSER_PASSWORD=${FIRST_SUPERUSER_PASSWORD?Variable not set}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - EMAILS_FROM_EMAIL=${EMAILS_FROM_EMAIL}
      - POSTGRES_SERVER=db
      - POSTGRES_PORT=${POSTGRES_PORT}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER?Variable not set}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD?Variable not set}
      - SENTRY_DSN=${SENTRY_DSN}
      - REDIS_SERVER=redis
      - GOOGLE_LOGIN_CALLBACK=${GOOGLE_LOGIN_CALLBACK}
      - PAYMENT_ENABLED=${PAYMENT_ENABLED}
      - PAYMENT_HOST=${PAYMENT_HOST}
      - GROWTH_TEAM_EMAIL=${GROWTH_TEAM_EMAIL}
      - RECAPTCHA_V3_SECRET_KEY=${RECAPTCHA_V3_SECRET_KEY?Variable not set}
      - DISCORD_INVITE_LINK=${DISCORD_INVITE_LINK?Variable not set}

    healthcheck:
      test:
        [
          "CMD",
          "curl",
          "-f",
          "http://localhost:8000/api/v1/utils/health-check/",
        ]
      interval: 10s
      timeout: 5s
      retries: 5

    build:
      context: ./backend

  scheduler:
    deploy:
      mode: replicated
      replicas: 1
    image: "${DOCKER_IMAGE_BACKEND?Variable not set}:${TAG-latest}"
    restart: always
    command: celery -A app.worker.celery_app beat --loglevel=debug
    networks:
      - default
    depends_on:
      db:
        condition: service_healthy
        restart: true
      prestart:
        condition: service_completed_successfully
      redis:
        condition: service_started
      connection-manager:
        condition: service_started
    env_file:
      - .env
    environment:
      - LANGFUSE_TRACING_ENVIRONMENT=${ENVIRONMENT}
      - DOMAIN=${DOMAIN}
      - FRONTEND_HOST=${FRONTEND_HOST?Variable not set}
      - ENVIRONMENT=${ENVIRONMENT}
      - BACKEND_CORS_ORIGINS=${BACKEND_CORS_ORIGINS}
      - SECRET_KEY=${SECRET_KEY?Variable not set}
      - FIRST_SUPERUSER=${FIRST_SUPERUSER?Variable not set}
      - FIRST_SUPERUSER_PASSWORD=${FIRST_SUPERUSER_PASSWORD?Variable not set}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - EMAILS_FROM_EMAIL=${EMAILS_FROM_EMAIL}
      - POSTGRES_SERVER=db
      - POSTGRES_PORT=${POSTGRES_PORT}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER?Variable not set}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD?Variable not set}
      - SENTRY_DSN=${SENTRY_DSN}
      - REDIS_SERVER=redis
      - GOOGLE_LOGIN_CALLBACK=${GOOGLE_LOGIN_CALLBACK}

    build:
      context: ./backend
    healthcheck:
      test: ["CMD", "python", "/app/app/healthcheck.py"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  worker:
    deploy:
      mode: replicated
      replicas: 1
    image: "${DOCKER_IMAGE_BACKEND?Variable not set}:${TAG-latest}"
    restart: always
    command: celery -A app.worker.celery_app worker --loglevel=info
    networks:
      - default
    depends_on:
      db:
        condition: service_healthy
        restart: true
      prestart:
        condition: service_completed_successfully
      redis:
        condition: service_started
      connection-manager:
        condition: service_started
    env_file:
      - .env
    environment:
      - LANGFUSE_TRACING_ENVIRONMENT=${ENVIRONMENT}
      - DOMAIN=${DOMAIN}
      - FRONTEND_HOST=${FRONTEND_HOST?Variable not set}
      - ENVIRONMENT=${ENVIRONMENT}
      - BACKEND_CORS_ORIGINS=${BACKEND_CORS_ORIGINS}
      - SECRET_KEY=${SECRET_KEY?Variable not set}
      - FIRST_SUPERUSER=${FIRST_SUPERUSER?Variable not set}
      - FIRST_SUPERUSER_PASSWORD=${FIRST_SUPERUSER_PASSWORD?Variable not set}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - EMAILS_FROM_EMAIL=${EMAILS_FROM_EMAIL}
      - POSTGRES_SERVER=db
      - POSTGRES_PORT=${POSTGRES_PORT}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER?Variable not set}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD?Variable not set}
      - SENTRY_DSN=${SENTRY_DSN}
      - REDIS_SERVER=redis
      - GOOGLE_LOGIN_CALLBACK=${GOOGLE_LOGIN_CALLBACK}
    build:
      context: ./backend
    healthcheck:
      test: ["CMD", "python", "/app/app/healthcheck.py"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  executor:
    image: "${DOCKER_IMAGE_EXECUTOR?Variable not set}:${TAG-latest}"
    restart: always
    networks:
      - default
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined
    privileged: true
    healthcheck:
      test:
        [
          "CMD",
          "curl",
          "-f",
          "http://localhost:8000/api/v1/utils/health-check/",
        ]
      interval: 10s
      timeout: 5s
      retries: 5
    depends_on:
      db:
        condition: service_healthy
        restart: true
      prestart:
        condition: service_completed_successfully
      redis:
        condition: service_started

    build:
      context: ./executor
    environment:
      - REDIS_SERVER=redis

  # frontend:
  #   image: "${DOCKER_IMAGE_FRONTEND?Variable not set}:${TAG-latest}"
  #   restart: always
  #   networks:
  #     - default
  #   ports:
  #     - "5173:5173"
  #   build:
  #     context: ./frontend
  #     args:
  #       - NEXT_PUBLIC_API_URL=https://${DOMAIN?Variable not set}
  #       - NEXT_PUBLIC_RECAPTCHA_V3_SITE_KEY=${RECAPTCHA_V3_SITE_KEY?Variable not set}

  payment:
    image: ${DOCKER_IMAGE_PAYMENT?Variable not set}
    restart: always
    networks:
      - default
    depends_on:
      db:
        condition: service_healthy
        restart: true
      payment-prestart:
        condition: service_completed_successfully
      redis:
        condition: service_started
    env_file:
      - .env
    environment:
      - DOMAIN=${DOMAIN}
      - ENVIRONMENT=${ENVIRONMENT}
      - POSTGRES_SERVER=db
      - POSTGRES_PORT=${POSTGRES_PORT}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER?Variable not set}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD?Variable not set}
      - STRIPE_API_KEY=${STRIPE_API_KEY}
      - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}
      - PAYMENT_SCHEMA_NAME=${PAYMENT_SCHEMA_NAME-payment}
      - REDIS_SERVER=redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health-check"]
      interval: 10s
      timeout: 5s
      retries: 5
    build:
      context: ./payment

  payment-prestart:
    image: ${DOCKER_IMAGE_PAYMENT?Variable not set}
    build:
      context: ./payment
    networks:
      - default
    depends_on:
      db:
        condition: service_healthy
        restart: true
    command: bash scripts/prestart.sh
    env_file:
      - .env
    environment:
      - DOMAIN=${DOMAIN}
      - ENVIRONMENT=${ENVIRONMENT}
      - POSTGRES_SERVER=db
      - POSTGRES_PORT=${POSTGRES_PORT}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER?Variable not set}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD?Variable not set}
      - PAYMENT_SCHEMA_NAME=${PAYMENT_SCHEMA_NAME-payment}
      - STRIPE_API_KEY=${STRIPE_API_KEY}
      - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}

  # minio:
  #   image: minio/minio:latest
  #   restart: always
  #   volumes:
  #     - minio-data:/data
  #   networks:
  #     - default
  #   environment:
  #     - MINIO_ROOT_USER=${MINIO_ROOT_USER:-minioadmin}
  #     - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD:-minioadmin}
  #   command: server /data --console-address ":9001"
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3

  qdrant:
    image: qdrant/qdrant:latest
    restart: always
    deploy:
      resources:
        limits:
          memory: 3G
    networks:
      - default
    volumes:
      - qdrant-data:/qdrant/storage
      - ./qdrant/config.yaml:/qdrant/config/config.yaml
    environment:
      - QDRANT_HOST=${QDRANT_HOST}
      - QDRANT_PORT=${QDRANT_PORT}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s

  # slack-integration:
  #   image: "${DOCKER_IMAGE_SLACK_INTEGRATION:-slack-integration}:${TAG-latest}"
  #   restart: always
  #   networks:
  #     - default
  #   ports:
  #     - "3000:3000"
  #   env_file:
  #     - .env
  #   environment:
  #     - SLACK_SIGNING_SECRET=${SLACK_SIGNING_SECRET?Variable not set}
  #     - SLACK_CLIENT_ID=${SLACK_CLIENT_ID?Variable not set}
  #     - SLACK_CLIENT_SECRET=${SLACK_CLIENT_SECRET?Variable not set}
  #     - FRONTEND_DOMAIN=${DOMAIN?Variable not set}
  #     - POSTGRES_SERVER=db
  #     - POSTGRES_PORT=${POSTGRES_PORT}
  #     - POSTGRES_DB=${POSTGRES_DB}
  #     - POSTGRES_USER=${POSTGRES_USER?Variable not set}
  #     - POSTGRES_PASSWORD=${POSTGRES_PASSWORD?Variable not set}
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:3000/slack/health"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #   command: uvicorn app:fastapi_app --host 0.0.0.0 --port 3000
  #   build:
  #     context: ./slack-integration

  litellm:
    platform: linux/amd64
    image: litellm/litellm:v1.74.7-stable
    volumes:
      - ./litellm/config.yaml:/app/config.yaml
      - ./litellm/log_config.conf:/app/log_config.conf
      - litellm-logs:/app/logs
    command:
      [
        "--config",
        "/app/config.yaml",
        "--log_config",
        "/app/log_config.conf",
        "--port",
        "4000",
        "--num_workers",
        "1",
      ]
    env_file:
      - .env
    networks:
      - default

volumes:
  app-db-data:
  redis-data:
  minio-data:
  qdrant-data:
  litellm-logs:
  sandbox-db-data:
