from mcp import StdioServerParameters
from mcp.types import CallToolResult, Tool

from app.core.stdio_server import MCPStdioServer
from app.logger import logger
from app.models import ConnectionConfig


class ConnectionManager:
    async def get_connection_tools(self, connection_config: ConnectionConfig) -> list[Tool]:
        async with MCPStdioServer(
            StdioServerParameters(
                command=connection_config.command,
                args=connection_config.args,
                env=connection_config.env,
            )
        ) as mcp_server:
            try:
                # Ref: https://github.com/langchain-ai/langchain-mcp-adapters/blob/main/langchain_mcp_adapters/tools.py#L124
                current_cursor: str | None = None
                all_tools: list[Tool] = []
                iterations = 0
                while True:
                    iterations += 1
                    if iterations > 1000:
                        raise Exception("Reached max of 1000 iterations while listing tools.")
                    list_tools_page_result = await mcp_server.list_tools(cursor=current_cursor)

                    if list_tools_page_result.tools:
                        all_tools.extend(list_tools_page_result.tools)

                    # Pagination spec: https://modelcontextprotocol.io/specification/2025-06-18/server/utilities/pagination
                    # compatible with None or ""
                    if not list_tools_page_result.nextCursor:
                        break

                    current_cursor = list_tools_page_result.nextCursor
                return all_tools
            except Exception as e:
                logger.exception(f"Error in get_connection_tools({connection_config.command}): {e}")
                raise Exception(
                    f"Error in get_connection_tools({connection_config.command})"
                ) from e

    async def call_connection_tool(
        self, connection_config: ConnectionConfig, tool_name: str, arguments: dict
    ) -> CallToolResult:
        try:
            async with MCPStdioServer(
                StdioServerParameters(
                    command=connection_config.command,
                    args=connection_config.args,
                    env=connection_config.env,
                )
            ) as mcp_server:
                tool_result = await mcp_server.call_tool(tool_name, arguments)
                return tool_result
        except Exception as e:
            logger.exception(
                f"Error in call_connection_tool({connection_config.command}, {tool_name}): {e}"
            )
            raise e
