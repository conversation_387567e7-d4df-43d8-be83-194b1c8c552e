model_list:
  - model_name: us-claude-sonnet-4
    litellm_params:
      model: bedrock/us.anthropic.claude-sonnet-4-20250514-v1:0
      aws_access_key_id: os.environ/AWS_ACCESS_KEY_ID
      aws_secret_access_key: os.environ/AWS_SECRET_ACCESS_KEY
      aws_region_name: "us-east-1"
    temperature: 0.1
    max_tokens: 8192
    reasoning_effort: "low"

  - model_name: apac-claude-sonnet-4
    litellm_params:
      model: bedrock/apac.anthropic.claude-sonnet-4-20250514-v1:0
      aws_access_key_id: os.environ/AWS_ACCESS_KEY_ID
      aws_secret_access_key: os.environ/AWS_SECRET_ACCESS_KEY
      aws_region_name: "ap-southeast-1"
    temperature: 0.1
    max_tokens: 8192
    reasoning_effort: "low"

  - model_name: apac-claude-sonnet-3.5-v2
    litellm_params:
      model: bedrock/apac.anthropic.claude-3-5-sonnet-20241022-v2:0
      aws_access_key_id: os.environ/AWS_ACCESS_KEY_ID
      aws_secret_access_key: os.environ/AWS_SECRET_ACCESS_KEY
      aws_region_name: "ap-southeast-1"
    temperature: 0.1
    max_tokens: 8192

  - model_name: us-claude-haiku-3.5
    litellm_params:
      model: bedrock/us.anthropic.claude-3-5-haiku-20241022-v1:0
      aws_access_key_id: os.environ/AWS_ACCESS_KEY_ID
      aws_secret_access_key: os.environ/AWS_SECRET_ACCESS_KEY
      aws_region_name: "us-east-1"
    temperature: 0.1
    max_tokens: 8192

  - model_name: apac-nova-lite
    litellm_params:
      model: bedrock/apac.amazon.nova-lite-v1:0
      aws_access_key_id: os.environ/AWS_ACCESS_KEY_ID
      aws_secret_access_key: os.environ/AWS_SECRET_ACCESS_KEY
      aws_region_name: "ap-southeast-1"
    temperature: 0.1
    max_tokens: 8192

router_settings:
  allowed_fails: 10
  retry_policy:
    {
      "AuthenticationErrorRetries": 5,
      "TimeoutErrorRetries": 5,
      "RateLimitErrorRetries": 5,
      "ContentPolicyViolationErrorRetries": 5,
      "InternalServerErrorRetries": 5,
    }
  fallbacks:
    [
      {
        "us-claude-sonnet-4":
          ["apac-claude-sonnet-4", "apac-claude-sonnet-3.5-v2"],
      },
      {
        "apac-claude-sonnet-4":
          ["us-claude-sonnet-4", "apac-claude-sonnet-3.5-v2"],
      },
      { "us-claude-haiku-3.5": ["apac-nova-lite"] },
    ]

litellm_settings:
  request_timeout: 600
  cache: False

general_settings:
  background_health_checks: True
  health_check_interval: 300
