# Clean Architecture Layers & Software Evolution Guide

## Executive Summary

This document provides a comprehensive understanding of the three core layers in Clean Architecture (Application, Domain, and Infrastructure), their distinct responsibilities, and best practices for software evolution following a "simple first" approach. Based on 2025 industry standards and proven architectural patterns.

---

## Clean Architecture Layer Structure

Clean Architecture follows the **Dependency Inversion Principle** where dependencies flow inward, creating a stable, maintainable system. The architecture consists of concentric circles where outer layers depend on inner layers, never the reverse.

```mermaid
graph TB
    subgraph "Infrastructure Layer (Outermost)"
        DB[Database]
        API[External APIs]
        FileSystem[File System]
        ThirdParty[Third-party Services]
    end
    
    subgraph "Application Layer (Middle)"
        UseCases[Use Cases]
        Commands[Commands]
        Queries[Queries]
        DTOs[Data Transfer Objects]
        Interfaces[Port Interfaces]
    end
    
    subgraph "Domain Layer (Core)"
        Entities[Business Entities]
        ValueObjects[Value Objects]
        DomainServices[Domain Services]
        BusinessRules[Business Rules]
        DomainEvents[Domain Events]
    end
    
    Infrastructure --> Application
    Application --> Domain
    
    classDef domain fill:#e8f5e8,stroke:#4caf50,stroke-width:3px
    classDef application fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    classDef infrastructure fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    
    class Entities,ValueObjects,DomainServices,BusinessRules,DomainEvents domain
    class UseCases,Commands,Queries,DTOs,Interfaces application
    class DB,API,FileSystem,ThirdParty infrastructure
```

---

## Domain Layer (Core Business Logic)

### **Purpose & Philosophy**
The Domain Layer represents the **heart of your business**. It contains the core business logic, rules, and concepts that define what your application does, independent of how it does it.

### **Key Characteristics**
- **Zero External Dependencies**: Does not depend on any other layer
- **Pure Business Logic**: Contains only enterprise-wide business rules
- **Technology Agnostic**: No framework, database, or UI concerns
- **Long-lived**: Changes only when business requirements change

### **Core Components**

#### **Entities**
```python
@dataclass
class KB:
    """Core business entity with identity and behavior"""
    id: UUID
    title: str
    owner_id: UUID
    workspace_id: UUID
    
    def grant_access(self, user_id: UUID) -> 'KB':
        """Business rule: Grant user access to KB"""
        if user_id in self.allowed_users:
            return self
        
        # Business logic encapsulated in entity
        new_allowed_users = self.allowed_users.copy()
        new_allowed_users.append(user_id)
        
        return self._copy_with_updates(
            allowed_users=new_allowed_users,
            updated_at=datetime.utcnow()
        )
```

#### **Value Objects**
```python
@dataclass(frozen=True)
class DocumentSource:
    """Immutable value object representing document source"""
    source_type: str  # "file", "url", "api"
    location: str
    config: dict
    
    def __post_init__(self):
        # Value object validation
        if not self.source_type or not self.location:
            raise ValueError("Source type and location required")
```

#### **Domain Services**
```python
class AccessControlDomainService:
    """Domain service for complex business rules spanning multiple entities"""
    
    def can_user_access_kb(self, user: User, kb: KB, workspace: Workspace) -> bool:
        """Complex business rule involving multiple entities"""
        # Enterprise-wide access control logic
        if kb.owner_id == user.id:
            return True
            
        if user.id in kb.allowed_users and user.workspace_id == workspace.id:
            return True
            
        return workspace.has_admin_user(user.id)
```

### **Responsibilities**
- ✅ Business entities and their behavior
- ✅ Value objects and business concepts
- ✅ Domain events and business rules
- ✅ Enterprise-wide validation logic
- ✅ Domain exceptions and error handling

### **Anti-Patterns (What NOT to Include)**
- ❌ Database access or ORM code
- ❌ HTTP requests or API calls
- ❌ File system operations
- ❌ Framework-specific code
- ❌ Infrastructure concerns

---

## Application Layer (Use Case Orchestration)

### **Purpose & Philosophy**
The Application Layer **orchestrates** the domain layer to fulfill specific use cases. It defines what the system does without specifying how external concerns are handled.

### **Key Characteristics**
- **Depends Only on Domain**: Can use domain entities and services
- **Defines Interfaces**: Specifies contracts for infrastructure implementations
- **Use Case Focused**: Each service represents a specific business use case
- **Technology Agnostic**: No infrastructure implementation details

### **Core Components**

#### **Use Cases (Application Services)**
```python
class SearchKBUseCase:
    """Orchestrates KB search workflow"""
    
    def __init__(self, 
                 vector_store: VectorStoreProtocol,  # Interface defined here
                 embedding_service: EmbeddingServiceProtocol,  # Interface defined here
                 kb_repository: KBRepositoryInterface):  # Interface defined here
        self._vector_store = vector_store
        self._embedding_service = embedding_service
        self._kb_repository = kb_repository
    
    async def execute(self, query: SearchQuery) -> SearchResult:
        """Orchestrate search use case"""
        # 1. Validate business rules (using domain layer)
        await self._validate_search_access(query)
        
        # 2. Orchestrate search workflow
        query_embedding = await self._embedding_service.generate_query_embedding(query.question)
        raw_results = await self._vector_store.search(query_embedding, query.filters)
        
        # 3. Apply business logic (using domain layer)
        filtered_results = self._apply_business_filters(raw_results, query)
        
        return SearchResult(
            query=query.question,
            sources=filtered_results,
            total_documents_searched=len(raw_results)
        )
```

#### **Port Interfaces (Contracts)**
```python
class VectorStoreProtocol(Protocol):
    """Interface for vector store implementations"""
    
    async def search(self, embedding: List[float], filters: dict) -> List[SearchMatch]:
        """Search for similar vectors"""
        ...
    
    async def upsert(self, documents: List[Document]) -> bool:
        """Insert or update document vectors"""
        ...

class EmbeddingServiceProtocol(Protocol):
    """Interface for embedding service implementations"""
    
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for batch of texts"""
        ...
```

#### **Data Transfer Objects (DTOs)**
```python
@dataclass
class SearchQuery:
    """DTO for search requests - input validation and serialization"""
    question: str
    kb_ids: List[str]
    workspace_id: str
    user_id: str
    max_results: Optional[int] = 10
    
    def __post_init__(self):
        # Input validation and sanitization
        if not self.question.strip():
            raise ValueError("Search question cannot be empty")
        
        if len(self.question) > 1000:
            raise ValueError("Search question too long")
        
        self.question = self.question.strip()
```

### **Responsibilities**
- ✅ Use case orchestration and workflow
- ✅ Interface definitions (ports/contracts)
- ✅ Input validation and DTO management
- ✅ Application-specific business rules
- ✅ Transaction coordination
- ✅ Security and authorization checks

### **Anti-Patterns (What NOT to Include)**
- ❌ Direct database access
- ❌ HTTP client implementations
- ❌ File system operations
- ❌ Framework-specific code
- ❌ Infrastructure implementation details

---

## Infrastructure Layer (External Concerns)

### **Purpose & Philosophy**
The Infrastructure Layer provides **concrete implementations** of the interfaces defined in the Application Layer. It handles all external concerns and technical details.

### **Key Characteristics**
- **Implements Interfaces**: Fulfills contracts defined by Application Layer
- **External Integration**: Handles databases, APIs, file systems, etc.
- **Framework Specific**: Contains framework and technology-specific code
- **Replaceable**: Implementations can be swapped without affecting core logic

### **Core Components**

#### **Repository Implementations**
```python
class SQLAlchemyKBRepository(KBRepositoryInterface):
    """PostgreSQL implementation of KB repository"""
    
    def __init__(self, session: AsyncSession):
        self._session = session
    
    async def create_kb(self, kb: KB) -> KB:
        """SQLAlchemy-specific implementation"""
        kb_model = KBModel(
            id=kb.id,
            title=kb.title,
            owner_id=kb.owner_id,
            workspace_id=kb.workspace_id
        )
        
        self._session.add(kb_model)
        await self._session.commit()
        
        return kb  # Return domain entity
    
    async def find_by_id(self, kb_id: UUID) -> Optional[KB]:
        """Convert from data model to domain entity"""
        kb_model = await self._session.get(KBModel, kb_id)
        if not kb_model:
            return None
        
        # Convert infrastructure model to domain entity
        return KB(
            id=kb_model.id,
            title=kb_model.title,
            owner_id=kb_model.owner_id,
            workspace_id=kb_model.workspace_id,
            allowed_users=kb_model.allowed_users or [],
            created_at=kb_model.created_at,
            updated_at=kb_model.updated_at
        )
```

#### **External Service Implementations**
```python
class BedrockEmbeddingService(EmbeddingServiceProtocol):
    """AWS Bedrock implementation for embeddings"""
    
    def __init__(self, client: BedrockClient, model_config: EmbeddingConfig):
        self._client = client
        self._model_config = model_config
    
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Bedrock-specific implementation"""
        try:
            response = await self._client.invoke_model(
                modelId=self._model_config.model_name,
                body={
                    "inputText": texts,
                    "dimensions": self._model_config.dimensions
                }
            )
            
            return response["embeddings"]
            
        except ClientError as e:
            # Convert infrastructure exception to domain exception
            raise EmbeddingServiceError(f"Failed to generate embeddings: {e}")
```

#### **Configuration and Factories**
```python
class ProviderFactory:
    """Factory for creating infrastructure implementations based on configuration"""
    
    def create_vector_store(self, config: VectorStoreConfig) -> VectorStoreProtocol:
        """Create vector store implementation based on config"""
        match config.provider:
            case "qdrant":
                return QdrantVectorStore(config)
            case "pinecone":
                return PineconeVectorStore(config)
            case "chroma":
                return ChromaVectorStore(config)
            case _:
                raise ValueError(f"Unknown vector store provider: {config.provider}")
    
    def create_embedding_service(self, config: EmbeddingConfig) -> EmbeddingServiceProtocol:
        """Create embedding service implementation based on config"""
        match config.provider:
            case "bedrock":
                return BedrockEmbeddingService(config)
            case "openai":
                return OpenAIEmbeddingService(config)
            case "local":
                return LocalEmbeddingService(config)
            case _:
                raise ValueError(f"Unknown embedding provider: {config.provider}")
```

### **Responsibilities**
- ✅ Database implementations (SQLAlchemy, MongoDB, etc.)
- ✅ External API clients (REST, GraphQL, gRPC)
- ✅ File system and cloud storage operations
- ✅ Message queue implementations
- ✅ Caching implementations
- ✅ Framework-specific configurations
- ✅ Infrastructure logging and monitoring

### **Anti-Patterns (What NOT to Include)**
- ❌ Business logic or business rules
- ❌ Use case orchestration
- ❌ Domain entity creation (except for conversion)
- ❌ Application workflow decisions

---

## Layer Interaction Patterns

### **Dependency Flow Example**
```python
# 1. Infrastructure Layer creates implementations
vector_store = QdrantVectorStore(config)
embedding_service = BedrockEmbeddingService(config)
kb_repository = SQLAlchemyKBRepository(session)

# 2. Application Layer receives interfaces
search_use_case = SearchKBUseCase(
    vector_store=vector_store,          # Interface
    embedding_service=embedding_service, # Interface  
    kb_repository=kb_repository          # Interface
)

# 3. Domain Layer contains pure business logic
kb = KB.create(title="My KB", owner_id=user_id)
kb = kb.grant_access(collaborator_id)  # Pure domain logic

# 4. Application orchestrates domain + infrastructure
result = await search_use_case.execute(SearchQuery(
    question="What is clean architecture?",
    kb_ids=[str(kb.id)],
    user_id=str(user.id)
))
```

### **Cross-Layer Communication Rules**

| From Layer | To Layer | Allowed? | Method |
|------------|----------|----------|---------|
| Infrastructure | Application | ✅ | Implements interfaces |
| Infrastructure | Domain | ✅ | Uses entities/value objects |
| Application | Domain | ✅ | Direct usage |
| Application | Infrastructure | ❌ | Use interfaces only |
| Domain | Application | ❌ | No dependencies |
| Domain | Infrastructure | ❌ | No dependencies |

---

## Software Evolution: Simple First Design Approach

Software should evolve gradually, starting simple and adding complexity only when business needs demand it. This approach ensures maintainability and prevents over-engineering.

### **Core Evolution Principles**

#### **1. YAGNI (You Aren't Gonna Need It)**
```python
# ❌ Over-engineered from start
class ComplexKBService:
    def __init__(self, vector_store, embedding_service, ml_pipeline, 
                 recommendation_engine, analytics_service, cache_service):
        # Too many dependencies from day one
        pass

# ✅ Start simple, add complexity when needed
class SimpleKBService:
    def __init__(self, vector_store: VectorStoreProtocol):
        self._vector_store = vector_store
    
    # Add more dependencies later when business needs arise
```

#### **2. Progressive Enhancement Pattern**
```python
class KBSearchService:
    """Evolution: Simple -> Enhanced -> Advanced"""
    
    # Phase 1: Simple keyword search
    async def simple_search(self, query: str) -> List[Document]:
        return await self._vector_store.keyword_search(query)
    
    # Phase 2: Add semantic search (when needed)
    async def semantic_search(self, query: str) -> List[Document]:
        if self._embedding_service:  # Optional dependency
            embedding = await self._embedding_service.generate_embedding(query)
            return await self._vector_store.vector_search(embedding)
        return await self.simple_search(query)  # Fallback
    
    # Phase 3: Add AI-powered search (when business demands)
    async def ai_enhanced_search(self, query: str) -> SearchResult:
        if self._ai_service:  # Optional enhancement
            enhanced_query = await self._ai_service.enhance_query(query)
            results = await self.semantic_search(enhanced_query)
            return await self._ai_service.synthesize_answer(query, results)
        return SearchResult(sources=await self.semantic_search(query))
```

#### **3. Interface Evolution Strategy**
```python
# Version 1: Simple interface
class VectorStoreV1(Protocol):
    async def search(self, query: str) -> List[Document]: ...

# Version 2: Enhanced interface (backward compatible)
class VectorStoreV2(Protocol):
    async def search(self, query: str, filters: Optional[dict] = None) -> List[Document]: ...
    async def batch_search(self, queries: List[str]) -> List[List[Document]]: ...

# Version 3: Advanced interface (new capabilities)
class VectorStoreV3(Protocol):
    async def search(self, query: str, filters: Optional[dict] = None) -> List[Document]: ...
    async def batch_search(self, queries: List[str]) -> List[List[Document]]: ...
    async def hybrid_search(self, query: str, semantic_weight: float = 0.7) -> List[Document]: ...
```

### **Evolution Strategies**

#### **Feature Flag Pattern**
```python
@dataclass
class KBConfiguration:
    """Configuration-driven feature evolution"""
    enable_semantic_search: bool = False
    enable_ai_synthesis: bool = False
    enable_multi_language: bool = False
    
    # Evolution flags
    experimental_features: dict = field(default_factory=dict)

class EvolvableKBService:
    def __init__(self, config: KBConfiguration, dependencies: dict):
        self._config = config
        self._dependencies = dependencies
    
    async def search(self, query: SearchQuery) -> SearchResult:
        """Evolving search method based on configuration"""
        
        # Phase 1: Basic search (always available)
        basic_results = await self._basic_search(query)
        
        # Phase 2: Semantic enhancement (feature flag)
        if self._config.enable_semantic_search:
            basic_results = await self._enhance_with_semantics(basic_results, query)
        
        # Phase 3: AI synthesis (feature flag)
        if self._config.enable_ai_synthesis:
            return await self._synthesize_with_ai(basic_results, query)
        
        return SearchResult(sources=basic_results)
```

#### **Plugin Architecture Evolution**
```python
class PluggableKBService:
    """Service that can evolve through plugins"""
    
    def __init__(self):
        self._search_plugins: List[SearchPlugin] = []
        self._processing_plugins: List[ProcessingPlugin] = []
    
    def register_search_plugin(self, plugin: SearchPlugin):
        """Add new search capabilities without changing core code"""
        self._search_plugins.append(plugin)
    
    async def search(self, query: SearchQuery) -> SearchResult:
        """Search evolves through registered plugins"""
        results = await self._core_search(query)
        
        # Apply registered enhancements
        for plugin in self._search_plugins:
            if plugin.can_handle(query):
                results = await plugin.enhance_results(results, query)
        
        return results
```

### **Migration Strategies**

#### **Strangler Fig Pattern**
```python
class LegacyKBService:
    """Old implementation being gradually replaced"""
    pass

class ModernKBService:
    """New clean architecture implementation"""
    
    def __init__(self, legacy_service: Optional[LegacyKBService] = None):
        self._legacy_service = legacy_service
    
    async def search(self, query: SearchQuery) -> SearchResult:
        """Gradually migrate from legacy to modern"""
        
        # Route some traffic to new implementation
        if self._should_use_modern_search(query):
            return await self._modern_search(query)
        
        # Fallback to legacy for complex cases
        legacy_result = await self._legacy_service.search(query)
        return self._convert_legacy_result(legacy_result)
```

#### **Database Evolution Pattern**
```python
class EvolvingRepository:
    """Repository that can handle multiple data sources during migration"""
    
    def __init__(self, 
                 primary_db: DatabaseConnection,
                 secondary_db: Optional[DatabaseConnection] = None):
        self._primary_db = primary_db
        self._secondary_db = secondary_db
    
    async def save_kb(self, kb: KB) -> KB:
        """Save to primary, optionally sync to secondary"""
        result = await self._primary_db.save(kb)
        
        # During migration, write to both systems
        if self._secondary_db:
            await self._secondary_db.save(kb)
        
        return result
    
    async def find_kb(self, kb_id: UUID) -> Optional[KB]:
        """Read from primary, fallback to secondary"""
        kb = await self._primary_db.find(kb_id)
        
        if not kb and self._secondary_db:
            kb = await self._secondary_db.find(kb_id)
            
            # Migrate found data to primary
            if kb:
                await self._primary_db.save(kb)
        
        return kb
```

### **2025 Evolution Best Practices**

#### **1. Observability-Driven Evolution**
```python
class ObservableKBService:
    """Service with built-in evolution metrics"""
    
    def __init__(self, metrics: MetricsCollector):
        self._metrics = metrics
    
    async def search(self, query: SearchQuery) -> SearchResult:
        """Collect metrics to guide evolution decisions"""
        
        with self._metrics.timer("kb_search_duration"):
            result = await self._perform_search(query)
        
        # Track feature usage for evolution planning
        self._metrics.increment("kb_search_total")
        self._metrics.histogram("result_count", len(result.sources))
        self._metrics.gauge("query_complexity", self._calculate_complexity(query))
        
        return result
```

#### **2. A/B Testing for Evolution**
```python
class ExperimentalKBService:
    """Service with built-in experimentation"""
    
    def __init__(self, ab_testing: ABTesting):
        self._ab_testing = ab_testing
    
    async def search(self, query: SearchQuery) -> SearchResult:
        """Test new features with percentage of users"""
        
        experiment = self._ab_testing.get_experiment("semantic_search_v2")
        
        if experiment.is_user_in_treatment(query.user_id):
            # New algorithm for treatment group
            return await self._semantic_search_v2(query)
        else:
            # Current algorithm for control group
            return await self._semantic_search_v1(query)
```

#### **3. Configuration-Driven Evolution**
```python
# config/features.yaml
features:
  semantic_search:
    enabled: true
    rollout_percentage: 100
  
  ai_synthesis:
    enabled: true
    rollout_percentage: 50
    
  experimental_ranking:
    enabled: false
    rollout_percentage: 5

# Application code
class ConfigurableKBService:
    """Service that evolves through configuration"""
    
    def __init__(self, feature_config: FeatureConfig):
        self._features = feature_config
    
    async def search(self, query: SearchQuery) -> SearchResult:
        """Features enabled through configuration"""
        
        search_strategy = self._select_search_strategy(query)
        
        results = await search_strategy.search(query)
        
        if self._features.is_enabled("ai_synthesis", query.user_id):
            results = await self._enhance_with_ai(results)
        
        return results
```

---

## Key Takeaways

### **Layer Responsibilities Summary**

| Layer | Purpose | Dependencies | Changes When |
|-------|---------|-------------|--------------|
| **Domain** | Business logic & rules | None | Business requirements change |
| **Application** | Use case orchestration | Domain only | Application workflows change |
| **Infrastructure** | External integrations | Application & Domain | Technology choices change |

### **Evolution Principles**

1. **Start Simple**: Begin with minimal viable implementation
2. **Add Complexity Gradually**: Enhance only when business needs require it
3. **Use Feature Flags**: Control rollout of new capabilities
4. **Measure Everything**: Use metrics to guide evolution decisions
5. **Maintain Backward Compatibility**: Evolve interfaces carefully
6. **Plan for Migration**: Design with future changes in mind

### **Architecture Benefits**

- **Maintainability**: Clear separation makes code easier to understand and modify
- **Testability**: Each layer can be tested independently with proper mocking
- **Flexibility**: Infrastructure can be swapped without affecting business logic
- **Scalability**: Application can evolve to handle increased complexity
- **Team Independence**: Different teams can work on different layers simultaneously

This approach ensures your software architecture remains robust, maintainable, and capable of evolving with changing business needs while preserving the core principles of Clean Architecture.