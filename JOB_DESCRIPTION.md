## The Challenge

What if your cloud infrastructure could **think**, **learn**, and **optimize itself**?


We're not building another monitoring tool. We're pioneering **autonomous cloud operations** - where AI agents don't just monitor, but truly *understand*, *learn*, and *act* .
## Who We Seek: **The Curious Mind**
### 🧭 **Thrives in Uncharted Territory**

- Gets excited by problems without Stack Overflow answers

- Sees "impossible" as a starting point

- Believes the best work happens at AI × real-world intersections

### 🔬 **Approaches Problems Like a Scientist**

- Asks "*What if...?*" more than "*How do I...?*"

- Digs three layers deeper because curiosity compels you

- Views failed experiments as valuable data

### 🚀 **Embraces Continuous Evolution**

- Learns new tech because it might unlock possibilities

- Adapts mental models as quickly as code

- Thrives on feedback loops from AI systems and humans

## What You'll Pioneer
- **🧠 Multi-Agent Orchestration**: Design AI agents that collaborate like an expert team - security agents working with cost-optimization agents, performance agents learning from compliance agents

- **⚡ Self-Improving Systems**: Build AI that learns from every interaction and becomes more intelligent with each cloud configuration

- **🎯 Proactive Intelligence**: Create systems that predict issues and optimize before problems occur

## Tech Stacks
**Stacks:** <PERSON><PERSON><PERSON><PERSON>, LlamaIndex, Qdrant, FastAPI, PostgreSQL, Docker

## What We Value Most

- 🧠 **Intellectual Curiosity** over years of experience

- 🌱 **Growth Mindset** over knowing all answers

- 🔍 **System Thinking** over feature implementation

## Bonus Points

- 🎨 Frontend curiosity (Next.js, TypeScript)

- ☁️ Cloud platform experience (AWS, Azure, GCP)

- 🔧 Open source contributions

## Additional Info
- 📍 Location: Ho Chi Minh City, Vietnam
- 🌐 Hybrid: 3 days in office, 2 days remote
- 📅 Start date: ASAP

## How to apply

Please send your resume to [<EMAIL>](mailto:<EMAIL>)
