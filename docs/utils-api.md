# Utils API Documentation

## Overview

The Utils API provides a centralized way to serve default enums, constants, and reference data to the frontend. This eliminates hardcoded values in the frontend and ensures consistency across the application.

## Architecture

### Components

```
backend/app/
├── constants.py                    # Enum definitions and data registry
├── schemas/constants.py            # Pydantic response schemas
├── services/constants_service.py   # Business logic layer
└── api/routes/utils.py            # REST endpoints
```

### Design Principles

- **Single Source of Truth**: All constants defined in one place
- **Type Safety**: Full Pydantic validation and enum support
- **Extensibility**: Easy to add new categories and data
- **Performance**: Cached responses with metadata
- **Search**: Full-text search across all constants

## API Endpoints

### Base URL
```
/api/v1/utils/constants/
```

### Endpoints

#### 1. List All Categories
```http
GET /api/v1/utils/constants/
```

**Response:**
```json
{
  "categories": [
    "cloud_providers",
    "aws_regions", 
    "gcp_regions",
    "azure_locations",
    "resource_types",
    "cost_optimization_categories"
  ],
  "total": 6
}
```

#### 2. Get Category Data
```http
GET /api/v1/utils/constants/{category}
```

**Parameters:**
- `category` (path): Category name (e.g., `aws_regions`)

**Example Response:**
```json
{
  "category": "aws_regions",
  "data": [
    {
      "value": "us-east-1",
      "label": "US East (N. Virginia)",
      "key": "US_EAST_1"
    },
    {
      "value": "us-west-2", 
      "label": "US West (Oregon)",
      "key": "US_WEST_2"
    }
  ],
  "description": "Amazon Web Services regions",
  "total": 12,
  "last_updated": "2024-01-01T00:00:00Z"
}
```

#### 3. Get All Constants
```http
GET /api/v1/utils/constants/all/
```

**Response:**
```json
{
  "constants": {
    "cloud_providers": {
      "category": "cloud_providers",
      "data": [...],
      "description": "Supported cloud providers",
      "total": 3,
      "last_updated": "2024-01-01T00:00:00Z"
    },
    "aws_regions": {
      "category": "aws_regions", 
      "data": [...],
      "description": "Amazon Web Services regions",
      "total": 12,
      "last_updated": "2024-01-01T00:00:00Z"
    }
  },
  "total_categories": 6,
  "generated_at": "2024-01-01T00:00:00Z"
}
```

#### 4. Search Constants
```http
GET /api/v1/utils/constants/search/?q={query}&categories={category1,category2}
```

**Parameters:**
- `q` (query): Search term
- `categories` (query, optional): Comma-separated list of categories to search

**Example:**
```http
GET /api/v1/utils/constants/search/?q=us-east&categories=aws_regions
```

**Response:**
```json
{
  "query": "us-east",
  "categories_searched": ["aws_regions"],
  "results": {
    "aws_regions": {
      "data": [
        {
          "value": "us-east-1",
          "label": "US East (N. Virginia)",
          "key": "US_EAST_1"
        }
      ],
      "description": "Amazon Web Services regions",
      "total": 1,
      "last_updated": "2024-01-01T00:00:00Z"
    }
  },
  "total_matches": 1,
  "searched_at": "2024-01-01T00:00:00Z"
}
```

#### 5. Validate Constant Value
```http
GET /api/v1/utils/constants/validate/{category}/{value}
```

**Parameters:**
- `category` (path): Category name
- `value` (path): Value to validate

**Example:**
```http
GET /api/v1/utils/constants/validate/aws_regions/us-east-1
```

**Response:**
```json
{
  "category": "aws_regions",
  "value": "us-east-1", 
  "is_valid": true,
  "validated_at": "2024-01-01T00:00:00Z"
}
```

#### 6. Get System Metadata
```http
GET /api/v1/utils/constants/metadata/
```

**Response:**
```json
{
  "total_categories": 6,
  "categories": {
    "cloud_providers": {
      "count": 3,
      "description": "Supported cloud providers",
      "last_updated": "2024-01-01T00:00:00Z"
    },
    "aws_regions": {
      "count": 12,
      "description": "Amazon Web Services regions", 
      "last_updated": "2024-01-01T00:00:00Z"
    }
  },
  "generated_at": "2024-01-01T00:00:00Z",
  "version": "1.0.0"
}
```

## Available Categories

### Cloud Providers
**Category:** `cloud_providers`
**Values:** `aws`, `gcp`, `azure`

### AWS Regions  
**Category:** `aws_regions`
**Values:** `us-east-1`, `us-west-2`, `eu-west-1`, etc.
**Labels:** Human-readable region names

### GCP Regions
**Category:** `gcp_regions`  
**Values:** `us-central1`, `europe-west1`, `asia-east1`, etc.
**Labels:** Human-readable region names with locations

### Azure Locations
**Category:** `azure_locations`
**Values:** `East US`, `West Europe`, `Southeast Asia`, etc.

### Resource Types
**Category:** `resource_types`
**Values:** `compute`, `storage`, `database`, `network`, `security`

### Cost Optimization Categories  
**Category:** `cost_optimization_categories`
**Values:** `unused_resources`, `rightsizing`, `reserved_instances`, etc.

## Frontend Usage Examples

### JavaScript/TypeScript

```javascript
// Get all categories
const categories = await fetch('/api/v1/utils/constants/')
  .then(res => res.json());

// Get specific category
const awsRegions = await fetch('/api/v1/utils/constants/aws_regions')
  .then(res => res.json());

// Search constants
const searchResults = await fetch('/api/v1/utils/constants/search/?q=us-east')
  .then(res => res.json());

// Validate value
const validation = await fetch('/api/v1/utils/constants/validate/aws_regions/us-east-1')
  .then(res => res.json());

// Use in dropdown
const regionOptions = awsRegions.data.map(region => ({
  value: region.value,
  label: region.label
}));
```

### React Hook Example

```typescript
import { useState, useEffect } from 'react';

interface ConstantOption {
  value: string;
  label: string;
  key: string;
}

interface ConstantCategory {
  category: string;
  data: ConstantOption[];
  description: string;
  total: number;
  last_updated: string;
}

export const useConstants = (category: string) => {
  const [data, setData] = useState<ConstantCategory | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchConstants = async () => {
      try {
        const response = await fetch(`/api/v1/utils/constants/${category}`);
        if (!response.ok) throw new Error('Failed to fetch constants');
        const result = await response.json();
        setData(result);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchConstants();
  }, [category]);

  return { data, loading, error };
};

// Usage in component
const RegionSelector = () => {
  const { data: regions, loading } = useConstants('aws_regions');

  if (loading) return <div>Loading...</div>;

  return (
    <select>
      {regions?.data.map(region => (
        <option key={region.key} value={region.value}>
          {region.label}
        </option>
      ))}
    </select>
  );
};
```

## Adding New Constants

### 1. Define Enum in `constants.py`

```python
class NewCategory(str, Enum):
    VALUE_1 = "value1"
    VALUE_2 = "value2"
```

### 2. Add to Registry

```python
CONSTANTS_REGISTRY["new_category"] = {
    "data": _enum_to_options(NewCategory),
    "description": "Description of new category",
    "last_updated": datetime.utcnow().isoformat(),
}
```

### 3. Custom Label Transform (Optional)

```python
def _custom_label_transform(value: str) -> str:
    # Custom logic to transform values to labels
    return value.replace("_", " ").title()

# Use in registry
"data": _enum_to_options(NewCategory, _custom_label_transform)
```

## Error Handling

All endpoints return standard HTTP status codes:

- **200**: Success
- **404**: Category not found
- **500**: Internal server error

Error responses follow this format:

```json
{
  "detail": "Category 'invalid_category' not found."
}
```

## Performance Considerations

- Constants are cached in memory
- Use `/constants/all/` for bulk operations
- Search is performed in-memory for fast results  
- Metadata endpoint provides cache information

## Security

- No authentication required (public reference data)
- Read-only operations only
- Input validation on all parameters
- Rate limiting recommended at API gateway level

## Versioning

- Current version: `1.0.0`
- Breaking changes will increment major version
- Backward compatibility maintained within major versions
- Version info available at `/constants/metadata/`