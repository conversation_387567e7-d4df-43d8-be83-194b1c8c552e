/*
* theme.css
*
* Shadcn UI theme
* Use this file to add any custom styles or override existing Shadcn UI styles
 */

/* container utility */

/* Shadcn UI theme */
@theme {
  --breakpoint-xs: 480px;

  --color-brand-teal: var(--brand-teal);
  --color-brand-blue: var(--brand-blue);
  --color-brand-green: var(--brand-green);

  --color-background: var(--background);
  --color-foreground: var(--foreground);

  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);

  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);

  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);

  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);

  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);

  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);

  --color-info: var(--info);
  --color-info-foreground: var(--info-foreground);

  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);

  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);

  --color-error-50: var(--error-50);
  --color-error-100: var(--error-100);
  --color-error-200: var(--error-200);
  --color-error-300: var(--error-300);
  --color-error-400: var(--error-400);
  --color-error-500: var(--error-500);
  --color-error-600: var(--error-600);
  --color-error-700: var(--error-700);
  --color-error-800: var(--error-800);
  --color-error-900: var(--error-900);

  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);

  /* --color-chart-1: var(--chart-1); */
  /* --color-chart-2: var(--chart-2); */
  /* --color-chart-3: var(--chart-3); */
  /* --color-chart-4: var(--chart-4); */
  /* --color-chart-5: var(--chart-5); */

  /* --radius-radius: var(--radius); */

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);

  --font-sans: -apple-system, var(--font-sans);
  /* --font-heading: var(--font-heading); */

  --color-sidebar: var(--sidebar-background);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --animate-accordion-down: 'accordion-down 0.2s ease-out';
  --animate-accordion-up: 'accordion-up 0.2s ease-out';
  --animate-fade-in: 'fade-in 0.3s ease-out';
  --animate-fade-out: 'fade-out 0.3s ease-out';
  --animate-ping-slow: 'ping-slow 3s cubic-bezier(0, 0, 0.2, 1) infinite';

  /* --animate-fade-up: fade-up 0.5s; */
  /* --animate-fade-down: fade-down 0.5s; */

  @keyframes accordion-down {
    from {
      height: 0;
    }

    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }

    to {
      height: 0;
    }
  }

  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fade-out {
    from {
      opacity: 1;
      transform: translateY(0);
    }
    to {
      opacity: 0;
      transform: translateY(10px);
    }
  }

  @keyframes ping-slow {
    0%,
    100% {
      transform: scale(1);
      opacity: 0.2;
    }
    50% {
      transform: scale(1.5);
      opacity: 0;
    }
  }

  /* @keyframes fade-up { */
  /* 0% { */
  /* opacity: 0; */
  /* transform: translateY(10px); */
  /* } */
  /* 80% { */
  /* opacity: 0.6; */
  /* } */
  /* 100% { */
  /* opacity: 1; */
  /* transform: translateY(0px); */
  /* } */
  /* } */
  /*  */
  /* @keyframes fade-down { */
  /* 0% { */
  /* opacity: 0; */
  /* transform: translateY(-10px); */
  /* } */
  /* 80% { */
  /* opacity: 0.6; */
  /* } */
  /* 100% { */
  /* opacity: 1; */
  /* transform: translateY(0px); */
  /* } */
  /* } */
}
