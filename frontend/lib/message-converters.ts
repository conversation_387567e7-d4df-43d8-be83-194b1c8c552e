import {
  Message as ChatMessage,
  Session,
  ToolCall,
} from '@/components/chat/types';
import {
  SchemaConversationPublic,
  SchemaMessageAgentThoughtPublic,
  SchemaMessagePublicList,
} from '@/openapi-ts/gens';

export const convertToUIMessage = (
  apiMessage: SchemaMessagePublicList,
): ChatMessage => {
  let toolCalls: ToolCall[] = [];
  if (apiMessage.thoughts) {
    apiMessage = apiMessage as SchemaMessagePublicList;
    toolCalls =
      apiMessage.thoughts?.map((thought: SchemaMessageAgentThoughtPublic) => ({
        id: thought.id,
        name: thought.tool_name || 'thought',
        arguments: thought.tool_input,
        output: thought.tool_output,
        status: thought.tool_output ? 'completed' : 'running',
        position: thought.position,
        thought: thought.content,
      })) || [];
  } else {
    toolCalls =
      apiMessage.agent_thoughts?.map((thought) => ({
        id: thought.id,
        name: thought.tool || 'thought',
        arguments: thought.tool_input,
        output: thought.observation,
        status: thought.observation ? 'completed' : 'running',
        position: thought.position,
        thought: thought.thought,
      })) || [];
  }

  return {
    id: apiMessage.id,
    content: apiMessage.content,
    role: apiMessage.role,
    timestamp: new Date(apiMessage.created_at * 1000),
    toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
    displayComponents: apiMessage.display_components || undefined,
    isInterrupt: apiMessage.is_interrupt,
    interruptMessage: apiMessage.interrupt_message,
    attachments: apiMessage.attachments || undefined,
  };
};

export const convertToSession = (
  conversation: SchemaConversationPublic,
): Session => ({
  id: conversation.id,
  title: conversation.name || 'New Chat',
  timestamp: new Date(conversation.created_at),
  resource: conversation.resource
    ? {
        id: conversation.resource.id || '',
        name: conversation.resource.name,
        arn: conversation.resource.arn,
        type: conversation.resource.type,
        region: conversation.resource.region,
        description: conversation.resource.description,
      }
    : null,
});
