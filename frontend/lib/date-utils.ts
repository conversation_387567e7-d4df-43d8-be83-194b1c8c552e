import { createElement } from 'react';

import {
  format,
  formatDistanceToNow,
  isToday,
  isYesterday,
  parseISO,
} from 'date-fns';

/**
 * Parse an ISO timestamp to a Date object
 * Handles both backend UTC timestamps and client-generated dates
 */
export const parseTimestamp = (
  timestamp: string | Date | null | undefined,
): Date | null => {
  if (!timestamp) return null;

  try {
    // If already a Date object
    if (timestamp instanceof Date) {
      return isNaN(timestamp.getTime()) ? null : timestamp;
    }

    // For string timestamps, ensure UTC interpretation
    if (typeof timestamp === 'string') {
      // If the string doesn't end with Z, append it to ensure UTC interpretation
      const utcString = timestamp.endsWith('Z') ? timestamp : `${timestamp}Z`;
      const date = parseISO(utcString);
      return isNaN(date.getTime()) ? null : date;
    }

    // Fallback for other cases
    const date = new Date(timestamp);
    return isNaN(date.getTime()) ? null : date;
  } catch (error) {
    console.error('Error parsing timestamp:', error);
    return null;
  }
};

/**
 * Format types for consistent date display throughout the application
 */
export enum DateFormat {
  // Standard formats
  SHORT_DATE = 'MMM d, yyyy', // Aug 5, 2023
  LONG_DATE = 'MMMM d, yyyy', // August 5, 2023
  TIME = 'h:mm a', // 3:30 PM
  DATE_TIME = 'MM/dd/yyyy h:mm a', // 08/05/2023 3:30 PM (12-hour format with AM/PM)
  DATE_TIME_24H = 'MM/dd/yyyy HH:mm', // 08/05/2023 15:30 (24-hour format)
  LOCAL_DATE_TIME = 'MMM d, yyyy h:mm a', // Aug 5, 2023 3:30 PM (more readable local format)
  FULL_DATE_TIME = 'MMMM d, yyyy h:mm a', // August 5, 2023 3:30 PM
  ISO_DATE = 'yyyy-MM-dd', // 2023-08-05

  // Special formats
  COMPACT = 'dd/MM/yy', // 05/08/23
  FILE_NAME = 'yyyy-MM-dd_HH-mm', // 2023-08-05_15-30
  API_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'Z'", // 2023-08-05T15:30:00Z

  // UI friendly formats
  PRETTY = 'PPP', // Aug 5, 2023
  VERBOSE = 'PPpp', // Aug 5, 2023, 3:30 PM
}

/**
 * Convert a timestamp to a consistent formatted string
 */
export const formatDate = (
  timestamp: string | Date | null | undefined,
  formatType: DateFormat = DateFormat.SHORT_DATE,
  fallback: string = '-',
): string => {
  const date = parseTimestamp(timestamp);
  if (!date) return fallback;

  try {
    return format(date, formatType);
  } catch (error) {
    console.error('Error formatting date:', error);
    return fallback;
  }
};

/**
 * Format a timestamp as a relative time (e.g., "2 hours ago")
 */
export const formatRelativeTime = (
  timestamp: string | Date | null | undefined,
  fallback: string = '-',
): string => {
  const date = parseTimestamp(timestamp);
  if (!date) return fallback;

  try {
    return formatDistanceToNow(date, { addSuffix: true });
  } catch (error) {
    console.error('Error formatting relative time:', error);
    return fallback;
  }
};

/**
 * Convert a UTC timestamp to local time using proper timezone handling
 * This properly handles timezone conversions while preserving the exact time
 */
export const convertUtcToLocalTime = (
  timestamp: string | Date | null | undefined,
): Date | null => {
  const date = parseTimestamp(timestamp);
  if (!date) return null;

  try {
    // The parseTimestamp already handles UTC parsing correctly
    // JavaScript Date objects automatically display in the user's local timezone
    return date;
  } catch (error) {
    console.error('Error converting UTC to local time:', error);
    return date; // Fall back to original date if conversion fails
  }
};

/**
 * Format a UTC timestamp from the backend to local time
 * This ensures the displayed time is in the user's local timezone
 */
export const formatUtcDate = (
  utcTimestamp: string | Date | null | undefined,
  formatType: DateFormat = DateFormat.SHORT_DATE,
  fallback: string = '-',
): string => {
  // Ensure we treat the timestamp as UTC if it's a string
  let processedTimestamp = utcTimestamp;

  // If it's a string timestamp from the backend and doesn't have timezone info, treat as UTC
  if (
    typeof utcTimestamp === 'string' &&
    !utcTimestamp.includes('Z') &&
    !utcTimestamp.includes('+') &&
    !utcTimestamp.includes('-')
  ) {
    processedTimestamp = `${utcTimestamp}Z`;
  }

  const localDate = convertUtcToLocalTime(processedTimestamp);
  if (!localDate) return fallback;

  return formatDate(localDate, formatType, fallback);
};

/**
 * Format a UTC timestamp specifically for local display with clear local time indication
 * Uses 12-hour format with AM/PM for better readability
 */
export const formatLocalDateTime = (
  utcTimestamp: string | Date | null | undefined,
  fallback: string = '-',
): string => {
  return formatUtcDate(utcTimestamp, DateFormat.LOCAL_DATE_TIME, fallback);
};

/**
 * Format just the local time portion from a UTC timestamp
 */
export const formatLocalTime = (
  utcTimestamp: string | Date | null | undefined,
  fallback: string = '-',
): string => {
  return formatUtcDate(utcTimestamp, DateFormat.TIME, fallback);
};

type DateValue = Date | string | number;

const formatDateFp = (formatString: string) => (date: DateValue) =>
  format(date, formatString);

export const formatFullDateTime = formatDateFp('MM/dd/yyyy, HH:mm');
export const formatDateOnly = formatDateFp('MM/dd/yyyy');
export const formatTime = formatDateFp('HH:mm');

/**
 * Creates a function that renders a date cell by applying a formatting callback
 * Returns "N/A" for null or undefined values
 *
 * @param callback - The formatting function to apply to the date value
 * @returns A function that formats the cell value or returns "N/A" if the value is falsy
 */
const renderCell =
  (callback: (value: DateValue) => string) =>
  // eslint-disable-next-line react/display-name
  ({ getValue }: { getValue: () => unknown }) => {
    const value = getValue();

    return value
      ? callback(value as DateValue)
      : createElement(
          'span',
          {
            className: 'text-muted-foreground',
          },
          'N/A',
        );
  };

/**
 * Renders a date cell with full date and time format (MM/dd/yyyy, HH:mm)
 */
export const renderCellToFullDateTime = renderCell(formatFullDateTime);

/**
 * Formats a number of seconds into a MM:SS string, or just SS if minutes are 00
 *
 * @param seconds - The number of seconds to format
 * @returns A string in the format "M:SS" or "SS" if minutes are 00
 */
export const formatToMinutesAndSeconds = (seconds: number) => {
  // Get minutes and seconds
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  // If minutes are 0, only show seconds
  if (minutes === 0) {
    return `${remainingSeconds}s`;
  }

  // Otherwise show minutes and seconds (no leading zero for minutes)
  return `${minutes}m${remainingSeconds.toString().padStart(2, '0')}s`;
};

/**
 * Formats a date value with smart relative formatting
 *
 * @param date - The date value to format (Date, string, or number)
 * @returns A string representation of the date:
 *   - "Today" if the date is today
 *   - "Yesterday" if the date was yesterday
 *   - Relative time format (e.g., "2 days ago", "3 weeks ago") for other dates
 *
 * @example
 * ```typescript
 * formatSmartDate(new Date()) // "Today"
 * formatSmartDate(yesterdayDate) // "Yesterday"
 * formatSmartDate(lastWeekDate) // "7 days ago"
 * ```
 */
export function formatSmartDate(date: DateValue): string {
  if (isToday(date)) return 'Today';
  if (isYesterday(date)) return 'Yesterday';
  return formatDistanceToNow(date, { addSuffix: true });
}

/**
 * Formats a date as relative time with hours precision
 *
 * @param date - The date to format
 * @returns A string showing the relative time distance from now (e.g., "2 hours ago")
 *
 * @example
 * ```typescript
 * getHoursAgoLabel(new Date(Date.now() - 2 * 60 * 60 * 1000)) // "2 hours ago"
 * ```
 */
export function getHoursAgoLabel(date: DateValue) {
  return formatDistanceToNow(date, {
    addSuffix: true, // adds "ago" or "in ..." suffix
  });
}
