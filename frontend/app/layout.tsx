import type { Metadata } from 'next';

import { Inter as FontSans } from 'next/font/google';

import Providers from '@/components/layout/providers';
import { Toaster } from '@/components/ui/sonner';
import { cn } from '@/lib/utils';
import NextTopLoader from 'nextjs-toploader';

import '../styles/globals.css';

export const metadata: Metadata = {
  title: 'Cloud Thinker',
  description: 'Stop overpaying for cloud services',
};

const lato = FontSans({
  subsets: ['latin'],
  variable: '--font-sans',
  preload: true,
  fallback: ['system-ui', 'Helvetica Neue', 'Helvetica', 'Arial'],
  weight: ['400', '500', '600', '700'],
  display: 'swap',
});

const theme = 'dark' as const;

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html
      lang="en"
      className={cn(theme, lato.className)}
      suppressHydrationWarning={true}
    >
      <body className={'overflow-hidden'}>
        <NextTopLoader showSpinner={false} />
        <Providers theme={theme}>{children}</Providers>

        <Toaster richColors theme={theme} />
      </body>
    </html>
  );
}
