'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Settings } from 'lucide-react';
import { CloudSyncConfigDialog } from '@/features/cloud-sync-config';

export function ResourceActions() {
  const [isConfigDialogOpen, setIsConfigDialogOpen] = useState(false);

  return (
    <>
      <Button 
        variant="outline" 
        size="sm"
        onClick={() => setIsConfigDialogOpen(true)}
      >
        <Settings className="mr-2 h-4 w-4" />
        Configure
      </Button>

      <CloudSyncConfigDialog
        open={isConfigDialogOpen}
        onOpenChange={setIsConfigDialogOpen}
      />
    </>
  );
}