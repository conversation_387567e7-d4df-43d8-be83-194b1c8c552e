import { use } from 'react';

import Link from 'next/link';

import { NewPageContainer } from '@/components/layout/new-page-container';
import { PageHeader } from '@/components/layout/page-header';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RecommendationFilters } from '@/features/recommendation/components/recommendation-filters';
import { RecommendationOveral } from '@/features/recommendation/components/recommendation-overal';
import { RecommendationTable } from '@/features/recommendation/components/recommendation-table';
import { RecommendationQueryParams } from '@/features/recommendation/models/recommendation.type';
import { withPaginationDefaults } from '@/utils/with-pagination-defaults';

import { resourceTabs } from '../_config/resource-tabs.config';

export const metadata = {
  title: 'Suggestion Recommendations',
};

type PageProps = {
  searchParams: Promise<RecommendationQueryParams>;
};

export default function Page(props: PageProps) {
  const searchParams = withPaginationDefaults(use(props.searchParams));

  return (
    <NewPageContainer>
      <PageHeader
        title="Recommendations"
        description="Identified recommendation for resources"
      />

      <Tabs
        value={resourceTabs[1].label}
        className="flex grow flex-col overflow-auto"
      >
        <TabsList>
          {resourceTabs.map((tab) => (
            <TabsTrigger key={tab.label} value={tab.label} asChild>
              <Link href={tab.link} prefetch>
                {tab.label}
              </Link>
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent
          value={resourceTabs[1].label}
          className="flex flex-col gap-4"
        >
          <RecommendationOveral />

          <RecommendationFilters defaultValues={searchParams} />
          <div className="grow overflow-auto">
            <RecommendationTable searchParams={searchParams} />
          </div>
        </TabsContent>
      </Tabs>
    </NewPageContainer>
  );
}
