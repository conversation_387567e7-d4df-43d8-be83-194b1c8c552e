import { Metadata } from 'next';

import Link from 'next/link';

import { NewPageContainer } from '@/components/layout/new-page-container';
import { PageHeader } from '@/components/layout/page-header';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import pathsConfig from '@/config/paths.config';
import { ToolList } from '@/features/builtin-tools/components/tool-list';
import { ConnectionList } from '@/features/connection';
import { ConnectionType } from '@/openapi-ts/gens';

type Props = {
  searchParams: Promise<{
    tab: string;
  }>;
};

export const metadata: Metadata = {
  title: 'Connectors',
};

export default async function Page(props: Props) {
  const searchParams = await props.searchParams;

  const tabs = [
    {
      value: 'builtin-connections',
      label: 'Builtin Connections',
      content: <ConnectionList type={ConnectionType.builtin} />,
    },
    {
      value: 'builtin-tools',
      label: 'Builtin Tools',
      content: <ToolList />,
    },
    {
      value: 'mcp',
      label: 'MCP',
      content: <ConnectionList type={ConnectionType.mcp} />,
    },
  ];

  return (
    <NewPageContainer>
      <PageHeader
        title="Connectors"
        description="Manage connectors and MCP servers for your environment"
      />

      <Tabs
        value={searchParams.tab ?? tabs[0].value}
        className="flex grow flex-col overflow-auto"
      >
        <TabsList>
          {tabs.map((tab) => (
            <TabsTrigger key={tab.value} value={tab.value} asChild>
              <Link
                href={`${pathsConfig.app.connectors}?tab=${tab.value}`}
                prefetch
              >
                {tab.label}
              </Link>
            </TabsTrigger>
          ))}
        </TabsList>

        {tabs.map((tab) => (
          <TabsContent key={tab.value} value={tab.value}>
            {tab.content}
          </TabsContent>
        ))}
      </Tabs>
    </NewPageContainer>
  );
}
