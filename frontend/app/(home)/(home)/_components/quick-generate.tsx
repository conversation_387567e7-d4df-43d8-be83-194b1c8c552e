'use client';

import {
  forwardRef,
  useEffect,
  useImperativeH<PERSON>le,
  useRef,
  useState,
} from 'react';

import { useRouter } from 'next/navigation';

// Import mention-related components and utilities
import { AgentMentionDropdown } from '@/components/chat/components/agent-mention';
import { AutocompleteSuggestion } from '@/components/chat/components/autocomplete-dropdown';
import { resourceCategories } from '@/components/chat/components/data';
import { ResourceMentionDropdown } from '@/components/chat/components/resource-mention';
import {
  getIncompleteAtCursor,
  highlightMentions,
} from '@/components/chat/utils/mention-highlighting';
import { Icons } from '@/components/icons';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { builtinToolsQuery } from '@/features/builtin-tools/hooks/builtin-tools.query';
import { useUserContext } from '@/features/user/provider/user-provider';
import { useKnowledgeBases } from '@/hooks/use-knowledge-bases';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface ExtendedAutocompleteSuggestion extends AutocompleteSuggestion {
  isLoading?: boolean;
  isEmpty?: boolean;
  category?: string;
}

// Custom placeholder component for styling mentions
const StyledPlaceholder = () => (
  <div className="text-muted-foreground/70 pointer-events-none absolute inset-0 px-5 pt-4">
    Type <span className="text-primary mx-1 font-medium">#</span> to mention a
    tool, or document, or{' '}
    <span className="text-primary mx-1 font-medium">@</span> to mention an
    agent...
  </div>
);

// Function to calculate cursor-based dropdown position for mentions
function calculateMentionDropdownPosition(
  textareaRef: HTMLTextAreaElement,
  cursorPosition: number,
  dropdownType: 'agent' | 'resource' = 'agent',
): { top: number; left: number } {
  const rect = textareaRef.getBoundingClientRect();
  const scrollY = window.scrollY;
  const scrollX = window.scrollX;
  const computedStyle = window.getComputedStyle(textareaRef);

  // Get padding values
  const paddingLeft = parseInt(computedStyle.paddingLeft) || 0;
  const paddingTop = parseInt(computedStyle.paddingTop) || 0;

  // Get the text before cursor to calculate the position
  const textBeforeCursor = textareaRef.value.substring(0, cursorPosition);
  const lines = textBeforeCursor.split('\n');
  const currentLine = lines[lines.length - 1];

  // Create a temporary span to measure text width
  const span = document.createElement('span');
  span.style.visibility = 'hidden';
  span.style.position = 'absolute';
  span.style.whiteSpace = 'pre';
  span.style.font = computedStyle.font;
  span.style.fontSize = computedStyle.fontSize;
  span.style.fontFamily = computedStyle.fontFamily;
  span.style.fontWeight = computedStyle.fontWeight;
  span.style.letterSpacing = computedStyle.letterSpacing;
  span.textContent = currentLine;
  document.body.appendChild(span);

  // Calculate the position based on text width and line height
  const lineHeight =
    parseInt(computedStyle.lineHeight) ||
    parseInt(computedStyle.fontSize) * 1.5;
  const textWidth = span.offsetWidth;
  const lineNumber = lines.length - 1;

  // Clean up
  document.body.removeChild(span);

  // Account for textarea scroll position
  const scrollTop = textareaRef.scrollTop;

  // Calculate the position relative to the textarea with padding and scroll offset
  const top =
    scrollY +
    rect.top +
    paddingTop +
    lineNumber * lineHeight -
    scrollTop +
    lineHeight +
    8;
  // If cursor is at the beginning of line, position dropdown at text start, otherwise at cursor position
  const left =
    scrollX + rect.left + paddingLeft + (textWidth > 0 ? textWidth + 8 : 0);

  // Dropdown dimensions based on type
  const dropdownWidth = dropdownType === 'resource' ? 750 : 500;
  const dropdownHeight = 320;

  // Adjust horizontal position if it would go off-screen
  const maxLeft = window.innerWidth - dropdownWidth - 20;
  const adjustedLeft = Math.min(Math.max(left, scrollX + 20), maxLeft);

  // Adjust vertical position if it would go off-screen
  const maxTop = window.innerHeight + scrollY - dropdownHeight - 20;
  const minTop = scrollY + 20;
  const adjustedTop = Math.min(Math.max(top, minTop), maxTop);

  return {
    top: adjustedTop,
    left: adjustedLeft,
  };
}
// New components for buttons
interface GenerateButtonProps {
  value: string;
  isGenerating: boolean;
  isSending: boolean;
  onGenerate: () => void;
}

const GenerateButton = ({
  value,
  isGenerating,
  isSending,
  onGenerate,
}: GenerateButtonProps) => (
  <Button
    size="icon"
    variant="outline"
    disabled={!value.trim() || isGenerating || isSending}
    onClick={onGenerate}
    className="h-8 w-8 rounded-full transition-all duration-200"
    title="Create a standard task definition format based on your requirements"
  >
    {isGenerating ? (
      <Icons.spinner className="h-4 w-4 animate-spin" />
    ) : (
      <Icons.calendarCog className="h-4 w-4" />
    )}
    <span className="sr-only">Generate</span>
  </Button>
);

interface SendButtonProps {
  value: string;
  isGenerating: boolean;
  isSending: boolean;
  onSend: () => void;
}

const SendButton = ({
  value,
  isGenerating,
  isSending,
  onSend,
}: SendButtonProps) => (
  <Button
    size="icon"
    className={cn(
      'h-8 w-8',
      'bg-primary/90 hover:bg-primary',
      'rounded-full transition-all duration-200',
      (!value.trim() || isSending) && 'opacity-50',
    )}
    disabled={!value.trim() || isGenerating || isSending}
    onClick={onSend}
    title="Send direct message"
  >
    {isSending ? (
      <Icons.spinner className="h-4 w-4 animate-spin" />
    ) : (
      <Icons.send className="h-4 w-4" />
    )}
    <span className="sr-only">Send</span>
  </Button>
);

interface QuickGenerateProps {
  messagesRemaining?: number;
  showGitHubButton?: boolean;
  onGitHubClick?: () => void;
  className?: string;
  setSelectedTask: (task: {
    title: string;
    description: string;
    category?: string;
    isGenerated: boolean;
  }) => void;
  setIsDialogOpen: (open: boolean) => void;
  setAutocompleteFilter: (filter: string) => void;
  setShowAutocomplete: (show: boolean) => void;
}

// Add interface for the ref
export interface QuickGenerateRef {
  handleAutocompleteSelect: (
    suggestion: ExtendedAutocompleteSuggestion,
  ) => void;
}

export const QuickGenerate = forwardRef<QuickGenerateRef, QuickGenerateProps>(
  (
    {
      messagesRemaining = 0,
      showGitHubButton = true,
      onGitHubClick,
      className,
      setSelectedTask,
      setIsDialogOpen,
      setAutocompleteFilter,
      setShowAutocomplete,
    },
    ref,
  ) => {
    // Original state
    const [value, setValue] = useState('');
    const [isSending, setIsSending] = useState(false);
    const { toast } = useToast();

    // Mention-related state
    const [showResourceMention, setShowResourceMention] = useState(false);
    const [resourceMentionPosition, setResourceMentionPosition] = useState(
      () => ({
        top: 0,
        left: 0,
      }),
    );
    const [resourceMentionFilter, setResourceMentionFilter] = useState('');
    const [showAgentMention, setShowAgentMention] = useState(false);
    const [agentMentionPosition, setAgentMentionPosition] = useState(() => ({
      top: 0,
      left: 0,
    }));
    const [agentMentionFilter, setAgentMentionFilter] = useState('');

    // Refs
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const overlayRef = useRef<HTMLDivElement>(null);
    const isSubmittingRef = useRef(false);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    // Fetch builtin tools and knowledge bases
    const { data: builtinTools, isPending: isBuiltinToolsLoading } =
      builtinToolsQuery.query.useList();
    const { kbs, isLoading: kbsLoading } = useKnowledgeBases();

    // Prepare dynamic resource categories
    const dynamicResourceCategories = [
      {
        id: 'tools',
        name: 'Tools',
        icon: <Icons.settings className="h-5 w-5 text-purple-500" />,
        items: isBuiltinToolsLoading
          ? [
              {
                id: 'loading',
                title: 'Loading tools...',
                description: 'Please wait',
              },
            ]
          : builtinTools?.map((tool) => ({
              id: tool.id,
              title: tool.builtin_tool.display_name,
              description: tool.builtin_tool.description,
            })) || [],
      },
      ...resourceCategories.map((category) => {
        if (category.isDynamic && category.source === 'kb_collections') {
          return {
            ...category,
            items: kbsLoading
              ? [
                  {
                    id: 'loading',
                    title: 'Loading collections...',
                    description: 'Please wait',
                  },
                ]
              : kbs,
          };
        }
        return category;
      }),
    ];

    const router = useRouter();
    const { agentId } = useUserContext();

    const handleDirectMessage = async () => {
      if (!value.trim() || isSending) return;

      setIsSending(true);
      try {
        // Navigate to agents page with initial message - conversation will be created implicitly
        const url = new URL(`/agents/${agentId}`, window.location.origin);
        url.searchParams.set('initialMessage', value.trim());
        router.push(url.pathname + url.search);
        setValue('');
      } finally {
        setIsSending(false);
      }
    };

    // Update handleKeyDown to handle mention navigation
    const handleKeyDown = (e: React.KeyboardEvent) => {
      // Let the dropdowns handle their own keyboard events
      if (showResourceMention || showAgentMention) {
        if (e.key === 'Escape') {
          e.preventDefault();
          setShowResourceMention(false);
          setShowAgentMention(false);
          return;
        }
        // Don't handle other keys when dropdowns are open
        return;
      }

      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        if (!isSending && value.trim()) {
          handleDirectMessage();
        }
      }

      // Handle # and @ key presses to trigger mentions
      if (e.key === '#' || e.key === '@') {
        const cursorPos = (e.currentTarget as HTMLTextAreaElement)
          .selectionStart;
        const textBeforeCursor = value.substring(0, cursorPos);

        // Check if the character before is a space or start of line
        const lastChar = textBeforeCursor.slice(-1);
        if (!lastChar || lastChar === ' ' || lastChar === '\n') {
          // Let the character be typed, checkForMentions will handle showing the dropdown
          return;
        }
      }
    };

    // Add effect to handle global keyboard events for dropdowns
    useEffect(() => {
      if (!showResourceMention && !showAgentMention) return;

      const handleGlobalKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          e.preventDefault();
          setShowResourceMention(false);
          setShowAgentMention(false);
        }
      };

      document.addEventListener('keydown', handleGlobalKeyDown);
      return () => {
        document.removeEventListener('keydown', handleGlobalKeyDown);
      };
    }, [showResourceMention, showAgentMention]);

    // Add effect to handle clicks outside dropdowns
    useEffect(() => {
      if (!showResourceMention && !showAgentMention) return;

      const handleClickOutside = (e: MouseEvent) => {
        // Check if click is outside both textarea AND dropdown
        if (
          textareaRef.current &&
          !textareaRef.current.contains(e.target as Node) &&
          dropdownRef.current &&
          !dropdownRef.current.contains(e.target as Node)
        ) {
          setShowResourceMention(false);
          setShowAgentMention(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [showResourceMention, showAgentMention]);

    // Add effect to update mention position on window resize and cursor movement
    useEffect(() => {
      if (!showResourceMention && !showAgentMention) return;

      const updateMentionPosition = () => {
        if (textareaRef.current) {
          const cursorPos = textareaRef.current.selectionStart;

          if (showResourceMention) {
            const position = calculateMentionDropdownPosition(
              textareaRef.current,
              cursorPos,
              'resource',
            );
            setResourceMentionPosition(position);
          }

          if (showAgentMention) {
            const position = calculateMentionDropdownPosition(
              textareaRef.current,
              cursorPos,
              'agent',
            );
            setAgentMentionPosition(position);
          }
        }
      };

      const handleResize = updateMentionPosition;
      const handleScroll = updateMentionPosition;

      window.addEventListener('resize', handleResize);
      window.addEventListener('scroll', handleScroll);

      // Also update position when textarea is scrolled
      const textarea = textareaRef.current;
      if (textarea) {
        textarea.addEventListener('scroll', handleScroll);
      }

      return () => {
        window.removeEventListener('resize', handleResize);
        window.removeEventListener('scroll', handleScroll);
        if (textarea) {
          textarea.removeEventListener('scroll', handleScroll);
        }
      };
    }, [showResourceMention, showAgentMention]);

    const { highlightedText, hasMentions } = highlightMentions(value);

    // Add handlers for mention selection
    const handleResourceSelect = (itemId: string, fullPath: string) => {
      if (textareaRef.current) {
        const cursorPos = textareaRef.current.selectionStart;
        const textBeforeCursor = value.substring(0, cursorPos);
        const textAfterCursor = value.substring(cursorPos);

        const mentionInfo = getIncompleteAtCursor(value, cursorPos);

        if (mentionInfo && mentionInfo.type === 'resource') {
          const cleanPath = fullPath.replace(/\//g, '/').toLowerCase();
          const newText =
            textBeforeCursor.substring(0, mentionInfo.startIndex) +
            `#${cleanPath}` +
            ' ' +
            textAfterCursor;

          setValue(newText);
          setShowResourceMention(false);

          setTimeout(() => {
            if (textareaRef.current) {
              const newCursorPos =
                mentionInfo.startIndex + `#${cleanPath} `.length;
              textareaRef.current.focus();
              textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
            }
          }, 0);
        }
      }
    };

    const handleAgentSelect = (agentId: string, agentName: string) => {
      if (textareaRef.current) {
        const cursorPos = textareaRef.current.selectionStart;
        const textBeforeCursor = value.substring(0, cursorPos);
        const textAfterCursor = value.substring(cursorPos);

        const mentionInfo = getIncompleteAtCursor(value, cursorPos);

        if (mentionInfo && mentionInfo.type === 'agent') {
          const displayName = agentName.split(' (')[0];
          const newText =
            textBeforeCursor.substring(0, mentionInfo.startIndex) +
            `@${displayName}` +
            ' ' +
            textAfterCursor;

          setValue(newText);
          setShowAgentMention(false);

          setTimeout(() => {
            if (textareaRef.current) {
              const newCursorPos =
                mentionInfo.startIndex + `@${displayName} `.length;
              textareaRef.current.focus();
              textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
            }
          }, 0);
        }
      }
    };

    // Add new methods for handling mentions
    const checkForMentions = (text: string, cursorPos: number) => {
      const mentionInfo = getIncompleteAtCursor(text, cursorPos);

      if (!mentionInfo) {
        setShowResourceMention(false);
        setShowAgentMention(false);
        return;
      }

      if (mentionInfo.type === 'resource' && textareaRef.current) {
        setResourceMentionFilter(mentionInfo.filter.toLowerCase());
        const position = calculateMentionDropdownPosition(
          textareaRef.current,
          cursorPos,
          'resource',
        );
        setResourceMentionPosition(position);
        setShowResourceMention(true);
        setShowAgentMention(false);
      } else if (mentionInfo.type === 'agent' && textareaRef.current) {
        setAgentMentionFilter(mentionInfo.filter.toLowerCase());
        const position = calculateMentionDropdownPosition(
          textareaRef.current,
          cursorPos,
          'agent',
        );
        setAgentMentionPosition(position);
        setShowAgentMention(true);
        setShowResourceMention(false);
      }
    };

    // Enhanced handleChange with improved autocomplete logic
    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      if (isSubmittingRef.current) return;

      const newValue = e.target.value;
      setValue(newValue);

      // Check for mentions
      checkForMentions(newValue, e.target.selectionStart);

      // Enhanced autocomplete handling
      const cursorPos = e.target.selectionStart;
      const textBeforeCursor = newValue.substring(0, cursorPos);
      const words = textBeforeCursor.split(/\s+/);
      const lastWord = words[words.length - 1] || '';

      // Show autocomplete if user typed at least 2 characters
      if (
        lastWord.length >= 2 &&
        !lastWord.startsWith('#') &&
        !lastWord.startsWith('@')
      ) {
        setAutocompleteFilter(lastWord);
        setShowAutocomplete(true);
      } else {
        setShowAutocomplete(false);
        setAutocompleteFilter('');
      }
    };

    // Enhanced autocomplete selection handler
    const handleAutocompleteSelect = (
      suggestion: ExtendedAutocompleteSuggestion,
    ) => {
      // Don't select loading or empty state items
      if (suggestion.isEmpty) return;

      if (textareaRef.current) {
        const cursorPos = textareaRef.current.selectionStart;
        const textBeforeCursor = value.substring(0, cursorPos);

        // Find the last word before cursor
        const words = textBeforeCursor.split(/\s+/);
        const lastWord = words[words.length - 1] || '';

        setValue(suggestion.description);
        setShowAutocomplete(false);
        setAutocompleteFilter('');

        // Focus and position cursor
        setTimeout(() => {
          if (textareaRef.current) {
            const newCursorPos =
              textBeforeCursor.length -
              lastWord.length +
              suggestion.description.length +
              1;
            textareaRef.current.focus();
            textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
          }
        }, 0);

        // Show success feedback
        toast({
          title: 'Template applied',
          description: `Applied template: ${suggestion.title}`,
          duration: 2000,
        });
      }
    };

    // Expose the handler through the ref
    useImperativeHandle(ref, () => ({
      handleAutocompleteSelect,
    }));

    // Handle cursor position changes (click, arrow keys, etc.)
    const handleCursorMove = () => {
      if (textareaRef.current && (showResourceMention || showAgentMention)) {
        const cursorPos = textareaRef.current.selectionStart;

        if (showResourceMention) {
          const position = calculateMentionDropdownPosition(
            textareaRef.current,
            cursorPos,
            'resource',
          );
          setResourceMentionPosition(position);
        }

        if (showAgentMention) {
          const position = calculateMentionDropdownPosition(
            textareaRef.current,
            cursorPos,
            'agent',
          );
          setAgentMentionPosition(position);
        }
      }
    };

    // Add syncScroll for overlay synchronization
    const syncScroll = () => {
      if (overlayRef.current && textareaRef.current) {
        // Use RAF for smooth scrolling without flickering
        overlayRef.current.scrollTop = textareaRef.current.scrollTop;
      }
    };

    // UI Components styled like MessageInput
    const renderGitHubButton = () => {
      if (!showGitHubButton) return null;

      return (
        <Button
          type="button"
          variant="ghost"
          size="icon"
          onClick={onGitHubClick}
          className="text-muted-foreground hover:bg-muted/70 hover:text-foreground dark:hover:bg-muted/30 h-8 w-8 rounded-full transition-all duration-200 active:scale-95"
        >
          <Icons.gitHub className="h-4 w-4" />
        </Button>
      );
    };

    const renderMessagesRemaining = () => {
      if (!messagesRemaining) return null;

      return (
        <div
          className={cn(
            'rounded-full px-2.5 py-0.5 text-xs font-medium text-white shadow-xs',
            'bg-blue-400',
          )}
        >
          {messagesRemaining} messages remaining
        </div>
      );
    };

    return (
      <div className={className}>
        <div className="mx-auto max-w-[1000px] px-4">
          <div className="border-primary/10 bg-muted/30 hover:border-primary/40 dark:bg-muted/10 overflow-hidden rounded-lg border-2 shadow-none transition-all duration-300 hover:border-2">
            {/* Resource Mention Dropdown */}
            <ResourceMentionDropdown
              isVisible={showResourceMention}
              position={resourceMentionPosition}
              filter={resourceMentionFilter}
              categories={dynamicResourceCategories}
              onSelect={handleResourceSelect}
              onClose={() => setShowResourceMention(false)}
              dropdownRef={dropdownRef}
            />

            {/* Agent Mention Dropdown */}
            <AgentMentionDropdown
              isVisible={showAgentMention}
              position={agentMentionPosition}
              filter={agentMentionFilter}
              onSelect={handleAgentSelect}
              onClose={() => setShowAgentMention(false)}
            />

            {/* Input Area */}
            <div ref={containerRef} className="relative">
              <Textarea
                ref={textareaRef}
                value={value}
                onChange={handleChange}
                onKeyDown={handleKeyDown}
                onPaste={() => {
                  if (overlayRef.current) {
                    overlayRef.current.scrollTo({
                      top: 100,
                      behavior: 'smooth',
                    });
                  }
                }}
                onScroll={syncScroll}
                onClick={handleCursorMove}
                onKeyUp={handleCursorMove}
                onSelect={handleCursorMove}
                placeholder=""
                disabled={isSending}
                className={cn(
                  'w-full',
                  'max-h-[150px] min-h-[150px]',
                  'custom-scrollbar resize-none overflow-y-auto',
                  'px-4 py-4',
                  'border-0 hover:shadow-none focus-visible:ring-0',
                  'bg-transparent',
                  'text-base',
                  'transition-[height] duration-100 ease-out', // Add smooth height transition
                  hasMentions
                    ? 'caret-foreground selection:bg-primary/20 text-transparent'
                    : 'caret-foreground',
                  'placeholder:opacity-0',
                  'border-none! shadow-none!',
                )}
              />

              {/* Highlighted Text Overlay */}
              <div
                ref={overlayRef}
                className={cn(
                  'custom-scrollbar pointer-events-none absolute inset-0 h-full w-full resize-none overflow-y-auto px-4 py-4 text-base break-words whitespace-pre-wrap transition-[height] duration-100 ease-out',
                  { invisible: !hasMentions },
                )}
              >
                {highlightedText}
              </div>

              {/* Placeholder */}
              {!value && <StyledPlaceholder />}
            </div>

            {/* Bottom Toolbar */}
            <div className="flex items-center gap-2 px-4 py-1">
              <div className="flex items-center gap-2">
                {renderGitHubButton()}
              </div>

              <div className="flex-1" />

              {/* Messages Remaining */}
              {renderMessagesRemaining()}

              {/* Generate Button */}
              <GenerateButton
                value={value}
                isGenerating={false}
                isSending={isSending}
                onGenerate={() => {
                  if (value.trim()) {
                    setSelectedTask({
                      title: value.split('\n')[0] || 'New Task',
                      description: value.trim(),
                      category: 'Direct Message',
                      isGenerated: true,
                    });
                    setIsDialogOpen(true);
                    setValue('');
                  }
                }}
              />

              {/* Send Button */}
              <SendButton
                value={value}
                isGenerating={false}
                isSending={isSending}
                onSend={handleDirectMessage}
              />
            </div>
          </div>
        </div>
      </div>
    );
  },
);

QuickGenerate.displayName = 'QuickGenerate';
