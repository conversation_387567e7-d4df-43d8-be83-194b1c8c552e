import { use } from 'react';

import Link from 'next/link';

import PageContainer from '@/components/layout/page-container';
import { PageHeader } from '@/components/layout/page-header';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { UserListPage } from '@/features/user/components/user-list-page';
import { UserQueryParams } from '@/features/user/models/user.type';
import { withPaginationDefaults } from '@/utils/with-pagination-defaults';

import { workspaceTabs } from '../_config/workpace-tabs.config';

export const metadata = {
  title: 'Users',
};

type Props = {
  searchParams: Promise<UserQueryParams>;
};

export default function Page(props: Props) {
  const searchParams = withPaginationDefaults(use(props.searchParams));

  return (
    <PageContainer>
      <PageHeader
        title="Users"
        description="Manage and monitor user access and permissions"
      />
      <Tabs value={workspaceTabs[1].label}>
        <TabsList>
          {workspaceTabs.map((tab) => (
            <TabsTrigger key={tab.label} value={tab.label} asChild>
              <Link href={tab.link} prefetch>
                {tab.label}
              </Link>
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value={workspaceTabs[1].label}>
          <UserListPage searchParams={searchParams} />
        </TabsContent>
      </Tabs>
    </PageContainer>
  );
}
