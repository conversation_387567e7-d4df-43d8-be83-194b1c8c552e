import type { Metada<PERSON> } from 'next';

import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import AppSidebar from '@/components/layout/app-sidebar';
import MobileNavigation from '@/components/layout/mobile-navigation';
import { SubscriptionProvider } from '@/components/providers/subscription-provider';
import { ScrollArea } from '@/components/ui/scroll-area';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';
import pathsConfig from '@/config/paths.config';
import { getAutonomousAgentId } from '@/features/agent/services/agent.api';
import { OnboardingPage } from '@/features/onboarding/components/onboarding-page';
import { OnboardingProvider } from '@/features/onboarding/provider/onboarding-provider';
import { onboardingApi } from '@/features/onboarding/services/onboarding.api';
import { UserProvider } from '@/features/user/provider/user-provider';
import { getCurrentUser } from '@/features/user/services/get-current-user';
import { getAllWorkspaces } from '@/features/workspaces/services/workspace.api';
import { fetchData } from '@/openapi-ts/openapi-fetch';
import { filter } from 'lodash';

export const metadata: Metadata = {
  title: 'CloudThinker',
  description: 'Cloud Operation Platform powered by Generative AI Agents',
};

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const user = await getCurrentUser();
  if (!user) {
    redirect(pathsConfig.auth.signIn);
  }
  const onboardingStatus = await onboardingApi.getStatus();

  if (!onboardingStatus.is_completed) {
    const awsRegions = await fetchData(onboardingApi.getRegions());

    return (
      <OnboardingProvider awsRegions={awsRegions}>
        <OnboardingPage defaultStep={onboardingStatus.current_step} />
      </OnboardingProvider>
    );
  }

  const workspaces = await getAllWorkspaces();
  // Todo: Remove after completion of onboarding
  const filteredWorkspaces = filter(workspaces, 'provider');

  // Get sidebar state from cookie, default to true if not set
  const cookieStore = await cookies();
  const sidebarCookie = cookieStore.get('sidebar:state');
  const defaultOpen = sidebarCookie ? sidebarCookie.value === 'true' : true;

  const agentId = await getAutonomousAgentId();

  return (
    <UserProvider user={user} workspaces={filteredWorkspaces} agentId={agentId}>
      <SidebarProvider defaultOpen={defaultOpen}>
        <SubscriptionProvider>
          <AppSidebar />
          <SidebarInset className="@container flex h-dvh flex-col">
            <MobileNavigation />
            {/* <Header className="border-b" /> */}
            <ScrollArea className="grow">
              <div className="flex h-full flex-col">{children}</div>
            </ScrollArea>
            {/* page main content ends */}
          </SidebarInset>
        </SubscriptionProvider>
      </SidebarProvider>
    </UserProvider>
  );
}
