/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    "/api/v1/login/access-token": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Login Access Token
         * @description OAuth2 compatible token login, get an access token for future requests
         */
        post: operations["login-login_access_token"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/login/test-token": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Test Token
         * @description Test access token
         */
        post: operations["login-test_token"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/password-recovery/{email}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Recover Password
         * @description Password Recovery
         */
        post: operations["login-recover_password"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/reset-password/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Reset Password
         * @description Reset password
         */
        post: operations["login-reset_password"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/password-recovery-html-content/{email}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Recover Password Html Content
         * @description HTML Content for Password Recovery
         */
        post: operations["login-recover_password_html_content"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/users/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Users
         * @description Retrieve users based on workspace relationship.
         *     Only returns users that belong to the current user's active workspace.
         */
        get: operations["users-read_users"];
        put?: never;
        /**
         * Create User
         * @description Create new user.
         */
        post: operations["users-create_user"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/users/me": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read User Me
         * @description Get current user.
         */
        get: operations["users-read_user_me"];
        put?: never;
        post?: never;
        /**
         * Delete User Me
         * @description Delete own user.
         */
        delete: operations["users-delete_user_me"];
        options?: never;
        head?: never;
        /**
         * Update User Me
         * @description Update own user.
         */
        patch: operations["users-update_user_me"];
        trace?: never;
    };
    "/api/v1/users/me/password": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        /**
         * Update Password Me
         * @description Update own password.
         */
        patch: operations["users-update_password_me"];
        trace?: never;
    };
    "/api/v1/users/{user_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read User By Id
         * @description Get a specific user by id.
         */
        get: operations["users-read_user_by_id"];
        put?: never;
        post?: never;
        /**
         * Delete User
         * @description Delete a user.
         */
        delete: operations["users-delete_user"];
        options?: never;
        head?: never;
        /**
         * Update User
         * @description Update a user.
         */
        patch: operations["users-update_user"];
        trace?: never;
    };
    "/api/v1/users/switch-workspace/{workspace_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Switch Workspace
         * @description Allow user to get new token for a different workspace.
         */
        get: operations["users-switch_workspace"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/utils/health-check/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Health Check */
        get: operations["utils-health_check"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/utils/constants/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Constant Categories
         * @description Get all available constant categories.
         *
         *     Returns a list of all available constant categories that can be queried.
         */
        get: operations["utils-get_constant_categories"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/utils/constants/{category}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Constant Category
         * @description Get constants for a specific category.
         *
         *     Args:
         *         category: The category name (e.g., 'cloud_providers', 'aws_regions')
         *
         *     Returns:
         *         Constants data for the specified category.
         */
        get: operations["utils-get_constant_category"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/utils/constants/all/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get All Constants
         * @description Get all constants data at once.
         *
         *     Returns:
         *         All available constants organized by category.
         */
        get: operations["utils-get_all_constants"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/aws-accounts/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /**
         * Update Aws Account
         * @description Update an AWS account.
         */
        put: operations["aws-accounts-update_aws_account"];
        /**
         * Create Aws Account
         * @description Create new AWS account.
         */
        post: operations["aws-accounts-create_aws_account"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/resources/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Resources
         * @description Retrieve resources with pagination and caching.
         */
        get: operations["resources-read_resources"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/recommendations/overal": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Recomendation Overal
         * @description Get overal recommendation statistics.
         */
        get: operations["recommendations-get_recomendation_overal"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/recommendations/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Recommendations
         * @description Retrieve recommendations.
         */
        get: operations["recommendations-read_recommendations"];
        put?: never;
        /**
         * Create Recommendation
         * @description Create new recommendation.
         */
        post: operations["recommendations-create_recommendation"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/recommendations/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Recommendation
         * @description Get recommendation by ID.
         */
        get: operations["recommendations-read_recommendation"];
        /**
         * Update Recommendation
         * @description Update a recommendation.
         */
        put: operations["recommendations-update_recommendation"];
        post?: never;
        /**
         * Delete Recommendation
         * @description Delete a recommendation.
         */
        delete: operations["recommendations-delete_recommendation"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/recommendations/{id}/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /**
         * Update Recommendation Status
         * @description Update the status of a recommendation.
         */
        put: operations["recommendations-update_recommendation_status"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workspaces/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Workspaces
         * @description Get workspaces - both owned and invited (non-deleted only).
         */
        get: operations["workspaces-get_workspaces"];
        put?: never;
        /**
         * Create Workspace
         * @description Create new workspace. Only users who already own workspaces or superusers can create new ones.
         */
        post: operations["workspaces-create_workspace"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workspaces/{workspace_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Workspace Details
         * @description Get Workspace by ID. Accessible by workspace owner and invited users. Only non-deleted workspaces are returned.
         */
        get: operations["workspaces-get_workspace_details"];
        /**
         * Update Workspace
         * @description Update a workspace. Only workspace owners can perform this action.
         */
        put: operations["workspaces-update_workspace"];
        post?: never;
        /**
         * Delete Workspace
         * @description Delete a workspace. Only workspace owners can perform this action.
         */
        delete: operations["workspaces-delete_workspace"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/console/files": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Files
         * @description Get files for the current user's workspace by proxying to executor
         */
        get: operations["console-proxy-get_files"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/console/files/content": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get File Content
         * @description Get file content from executor for display in the file explorer
         */
        get: operations["console-proxy-get_file_content"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/agents/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Agents
         * @description Retrieve Agents for the current workspace.
         */
        get: operations["agents-read_agents"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/agents/{agent_id}/instructions": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        /**
         * Update Agent Instructions
         * @description Update the instructions of an agent.
         */
        patch: operations["agents-update_agent_instructions"];
        trace?: never;
    };
    "/api/v1/agents/{agent_id}/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        /**
         * Update Agent Status
         * @description Update the status of an agent.
         */
        patch: operations["agents-update_agent_status"];
        trace?: never;
    };
    "/api/v1/tasks/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Tasks
         * @description List tasks with filters.
         */
        get: operations["tasks-list_tasks"];
        put?: never;
        /**
         * Create Task
         * @description Create new task.
         */
        post: operations["tasks-create_task"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tasks/{task_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Task
         * @description Get task by ID.
         */
        get: operations["tasks-get_task"];
        /**
         * Update Task
         * @description Update task.
         */
        put: operations["tasks-update_task"];
        post?: never;
        /**
         * Delete Task
         * @description Delete task.
         */
        delete: operations["tasks-delete_task"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tasks/{task_id}/average-run-time": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Task Average Run Time
         * @description Get the average run time for a task.
         */
        get: operations["tasks-get_task_average_run_time"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tasks/{task_id}/histories": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Task Histories
         * @description Get task histories by task ID.
         */
        get: operations["tasks-get_task_histories"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tasks/{task_id}/enable": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        /**
         * Update Task Enable
         * @description Update task enable.
         */
        patch: operations["tasks-update_task_enable"];
        trace?: never;
    };
    "/api/v1/tasks/{task_id}/stop": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Stop Task Execution
         * @description Stop an executing task.
         */
        post: operations["tasks-stop_task_execution"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/autonomous-agents/conversations/{conversation_id}/name": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /** Rename Conversation */
        put: operations["autonomous-agents-rename_conversation"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/autonomous-agents/conversations/{conversation_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        /**
         * Delete Conversation
         * @description Delete a conversation and its associated LangGraph thread data.
         */
        delete: operations["autonomous-agents-delete_conversation"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/autonomous-agents/conversations": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Conversations
         * @description Get list of conversations with filtering and pagination.
         */
        get: operations["autonomous-agents-get_conversations"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/autonomous-agents/messages/{conversation_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get Messages History */
        get: operations["autonomous-agents-get_messages_history"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/autonomous-agents/chat/{conversation_id}/stream": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Chat Stream */
        post: operations["autonomous-agents-chat_stream"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/autonomous-agents/chat/stream": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Chat Stream */
        post: operations["autonomous-agents-chat_stream"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/autonomous-agents/chat/{conversation_id}/reconnect-stream": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Reconnect To Stream
         * @description Reconnects to an existing stream, sending missed events + continuing live stream.
         *     Used when users return to a conversation page with an active stream.
         */
        get: operations["autonomous-agents-reconnect_to_stream"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/google/login": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Google Login
         * @description Initiate Google OAuth login flow
         */
        get: operations["google-google_login"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/google/callback": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Google Callback
         * @description Handle Google OAuth callback and login/create user
         */
        get: operations["google-google_callback"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/quotas/usage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Create Usage */
        post: operations["quotas-create_usage"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/quotas/message-statistics": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Messages Statistics
         * @description Get message usage statistics for a workspace.
         *
         *     Args:
         *         start_date: Optional start date for filtering
         *         end_date: Optional end date for filtering
         *
         *     Returns:
         *         Message statistics including:
         *         - Total messages and month-over-month change
         *         - Average response time and month-over-month change
         *         - Success rate and month-over-month change
         *         - Average tokens per message (input/output)
         *         - Daily message volume (30-day trend)
         *         - Token distribution by message length
         */
        get: operations["quotas-get_messages_statistics"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/quotas/{user_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Usage Quota
         * @description Get usage quota for a specific workspace.
         *
         *     Args:
         *         user_id: ID of the user
         *
         *     Returns:
         *         Usage quota details
         */
        get: operations["quotas-get_usage_quota"];
        put?: never;
        /** Create Usage Quota */
        post: operations["quotas-create_usage_quota"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/quotas/{user_id}/reset": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Reset User Quota
         * @description Reset usage quota for a user.
         *
         *     Args:
         *         user_id: ID of the user
         *
         *     Returns:
         *         Reset usage quota details
         */
        post: operations["quotas-reset_user_quota"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/quotas/{user_id}/statistics": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Usage Statistics
         * @description Get token usage statistics for a workspace.
         *
         *     Args:
         *         user_id: ID of the user
         *         start_date: Optional start date for filtering
         *         end_date: Optional end date for filtering
         *
         *     Returns:
         *         Usage statistics
         */
        get: operations["quotas-get_usage_statistics"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/quotas/{user_id}/quota-info": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Quota Info
         * @description Get quota information for a user.
         *
         *     Args:
         *         user_id: ID of the user
         */
        get: operations["quotas-get_quota_info"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/reports/{conversation_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get Report By Conversation */
        get: operations["reports-get_report_by_conversation"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/dashboards/{conversation_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Dashboard By Conversation
         * @description Get dashboard by conversation ID for the current user's workspace.
         */
        get: operations["dashboards-get_dashboard_by_conversation"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/task_templates/generate": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Generate
         * @description Generate the task template based on user's input
         */
        post: operations["task_templates-generate"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/task_templates/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Templates
         * @description List task templates with optional category and service filters.
         */
        get: operations["task_templates-list_templates"];
        put?: never;
        /**
         * Create Template
         * @description Create new task template.
         */
        post: operations["task_templates-create_template"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/task_templates/{template_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Template
         * @description Get template by ID.
         */
        get: operations["task_templates-get_template"];
        /**
         * Update Template
         * @description Update template.
         */
        put: operations["task_templates-update_template"];
        post?: never;
        /**
         * Delete Template
         * @description Delete template.
         */
        delete: operations["task_templates-delete_template"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/knowledge_base/kbs": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Kbs
         * @description Get all knowledge bases for the current user
         */
        get: operations["knowledge_base-get_kbs"];
        put?: never;
        /** Create Kb */
        post: operations["knowledge_base-create_kb"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/knowledge_base/kbs/available-users": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Available Users
         * @description Get a list of users available for sharing knowledge bases within the current workspace.
         *     Returns users that are in the same workspace as the current user.
         */
        get: operations["knowledge_base-get_available_users"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/knowledge_base/kbs/point-usage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Point Usage
         * @description Get user's point usage across all knowledge bases
         */
        get: operations["knowledge_base-get_point_usage"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/knowledge_base/kbs/{kb_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Kb By Id
         * @description Get a specific knowledge base by ID
         */
        get: operations["knowledge_base-get_kb_by_id"];
        /** Update Kb */
        put: operations["knowledge_base-update_kb"];
        post?: never;
        /** Delete Kb */
        delete: operations["knowledge_base-delete_kb"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/knowledge_base/kbs/{kb_id}/presigned-urls": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Generate Presigned Urls
         * @description Generate presigned URLs for file uploads.
         *
         *     This endpoint generates presigned URLs that clients can use to upload files
         *     directly to S3, bypassing the backend for better performance and scalability.
         */
        post: operations["knowledge_base-generate_presigned_urls"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/knowledge_base/kbs/{kb_id}/confirm-uploads": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Confirm File Uploads
         * @description Confirm file uploads and start ingestion process.
         *
         *     This endpoint should be called after files have been successfully uploaded
         *     using the presigned URLs to start the document ingestion process.
         */
        post: operations["knowledge_base-confirm_file_uploads"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/knowledge_base/kbs/{kb_id}/documents": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Documents
         * @description List documents in a knowledge base.
         *
         *     User must have access to the knowledge base (owner for personal knowledge bases,
         *     workspace member for workspace knowledge bases).
         */
        get: operations["knowledge_base-list_documents"];
        put?: never;
        /** Upload Urls */
        post: operations["knowledge_base-upload_urls"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/knowledge_base/kbs/{kb_id}/documents/content": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get Document Content */
        get: operations["knowledge_base-get_document_content"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/knowledge_base/kbs/{kb_id}/documents/{document_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        /** Delete Document */
        delete: operations["knowledge_base-delete_document"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/knowledge_base/tasks/{task_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Task Status
         * @description Get the status of an asynchronous task.
         *
         *     This endpoint returns the current status and progress of a Celery task,
         *     such as document ingestion.
         */
        get: operations["knowledge_base-get_task_status"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/subscriptions/plans": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Available Plans
         * @description Get available plans
         */
        get: operations["subscriptions-get_available_plans"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/subscriptions/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get User Subscription Status */
        get: operations["subscriptions-get_user_subscription_status"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/subscriptions/workspace/{workspace_id}/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Workspace Subscription Status
         * @description Get subscription status for a workspace
         */
        get: operations["subscriptions-get_workspace_subscription_status"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/subscriptions/checkout": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create Checkout Session
         * @description Create a checkout session for subscription
         */
        post: operations["subscriptions-create_checkout_session"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/subscriptions/payment-methods": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get User Payment Methods
         * @description Get current user's payment methods
         */
        get: operations["subscriptions-get_user_payment_methods"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/subscriptions/invoices": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get User Invoices
         * @description Get current user's invoices
         */
        get: operations["subscriptions-get_user_invoices"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/subscriptions/enterprise-enquiry": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Submit Enterprise Enquiry
         * @description Submit an enterprise plan enquiry
         */
        post: operations["subscriptions-submit_enterprise_enquiry"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/subscriptions/plan-change": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Submit Plan Change Request
         * @description Submit a plan change request
         */
        post: operations["subscriptions-submit_plan_change_request"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/subscriptions/webhook": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Webhook
         * @description Handle webhook events from payment provider
         */
        post: operations["subscriptions-webhook"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/subscriptions/cancel": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Cancel Subscription
         * @description Cancel subscription for the current user
         */
        post: operations["subscriptions-cancel_subscription"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/module_setting/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Module Settings
         * @description Retrieve all module settings.
         */
        get: operations["module_setting-get_module_settings"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/alerts/summary/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Alert Status Summary
         * @description Get a summary of alerts by status for the last 30 days.
         */
        get: operations["alerts-get_alert_status_summary"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/alerts/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Alerts
         * @description List alerts with optional filters.
         */
        get: operations["alerts-list_alerts"];
        put?: never;
        /**
         * Create Alert
         * @description Create new alert.
         */
        post: operations["alerts-create_alert"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/alerts/{alert_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Alert
         * @description Get alert by ID.
         */
        get: operations["alerts-get_alert"];
        /**
         * Update Alert
         * @description Update alert.
         */
        put: operations["alerts-update_alert"];
        post?: never;
        /**
         * Delete Alert
         * @description Delete alert.
         */
        delete: operations["alerts-delete_alert"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/alerts/{alert_id}/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        /**
         * Update Alert Status
         * @description Update alert status.
         */
        patch: operations["alerts-update_alert_status"];
        trace?: never;
    };
    "/api/v1/alerts/mark-all-acknowledged": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Mark All Alerts Acknowledged
         * @description Mark all alerts as acknowledged for the current workspace.
         */
        post: operations["alerts-mark_all_alerts_acknowledged"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/signup": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Register
         * @description Register new user and send activation email.
         */
        post: operations["auth-register"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/activate/{token}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Activate Account
         * @description Activate a user account using the activation token.
         */
        post: operations["auth-activate_account"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/resend-activation": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Resend Activation
         * @description Resend activation email for unactivated accounts with reCAPTCHA v3 validation.
         */
        post: operations["auth-resend_activation"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/notifications/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** List Notifications */
        get: operations["notifications-list_notifications"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/notifications/{notification_id}/mark-read": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Mark Notification Read */
        post: operations["notifications-mark_notification_read"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/notifications/mark-all-read": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Mark All Notifications Read */
        post: operations["notifications-mark_all_notifications_read"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/message-feedback/message/{message_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Message Feedback
         * @description Get feedback for a specific message.
         */
        get: operations["message-feedback-get_message_feedback"];
        /**
         * Update Message Feedback
         * @description Update feedback for a message.
         */
        put: operations["message-feedback-update_message_feedback"];
        post?: never;
        /**
         * Delete Message Feedback
         * @description Delete feedback for a message.
         */
        delete: operations["message-feedback-delete_message_feedback"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/message-feedback/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create Message Feedback
         * @description Create feedback for a message.
         */
        post: operations["message-feedback-create_message_feedback"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/connection/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get Connections */
        get: operations["connection-get_connections"];
        put?: never;
        /** Create Connection */
        post: operations["connection-create_connection"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/connection/builtin": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Builtin Connections
         * @description Get all available builtin connections.
         */
        get: operations["connection-get_builtin_connections"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/connection/builtin/{builtin_id}/install": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Install Builtin Connection
         * @description Install a builtin connection to the user's workspace.
         */
        post: operations["connection-install_builtin_connection"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/connection/{conn_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get Connection */
        get: operations["connection-get_connection"];
        /** Update Connection */
        put: operations["connection-update_connection"];
        post?: never;
        /** Delete Connection */
        delete: operations["connection-delete_connection"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/cloud-sync-config/resource-types": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Resource Types
         * @description Get available resource types for a specific cloud provider.
         */
        get: operations["cloud-sync-config-get_resource_types"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/cloud-sync-config/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Workspace Configs
         * @description Get all cloud sync configurations for the current workspace.
         */
        get: operations["cloud-sync-config-get_workspace_configs"];
        put?: never;
        /**
         * Create Or Update Config
         * @description Create or update a cloud sync configuration.
         */
        post: operations["cloud-sync-config-create_or_update_config"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/cloud-sync-config/{config_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /**
         * Update Config
         * @description Update a specific cloud sync configuration.
         */
        put: operations["cloud-sync-config-update_config"];
        post?: never;
        /**
         * Delete Config
         * @description Delete a cloud sync configuration.
         */
        delete: operations["cloud-sync-config-delete_config"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/cloud-sync-config/{config_id}/sync": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Trigger Manual Sync
         * @description Trigger a manual sync for a specific configuration.
         */
        post: operations["cloud-sync-config-trigger_manual_sync"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/attachments/attachments/presigned-urls": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Generate Attachment Presigned Urls
         * @description Generate presigned URLs for file uploads.
         *     Clients can use these URLs to upload files directly to the object storage.
         */
        post: operations["attachments-generate_attachment_presigned_urls"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/attachments/attachments/confirm-uploads": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Confirm Attachment Uploads
         * @description Confirm that files have been uploaded and trigger the validation task.
         */
        post: operations["attachments-confirm_attachment_uploads"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/attachments/attachments/tasks/{task_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Attachment Task Status
         * @description Get the status of an asynchronous attachment validation task.
         */
        get: operations["attachments-get_attachment_task_status"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/attachments/attachments/{attachment_id}/download-url": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Attachment Download Url
         * @description Generate a presigned GET URL to download an attachment.
         */
        get: operations["attachments-get_attachment_download_url"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/attachments/attachments/{attachment_id}/metadata": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Attachment Metadata
         * @description Get attachment metadata by attachment ID.
         */
        get: operations["attachments-get_attachment_metadata"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/builtin-tools/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Workspace Builtin Tools
         * @description List all built-in tools for a workspace
         */
        get: operations["builtin-tools-get_workspace_builtin_tools"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/builtin-tools/{workspace_builtin_tool_id}/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        /**
         * Update Workspace Builtin Tool
         * @description Update the permission requirement of a built-in tool for a workspace
         */
        patch: operations["builtin-tools-update_workspace_builtin_tool"];
        trace?: never;
    };
    "/api/v1/share-chat/conversations/{conversation_id}/share": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Share Link
         * @description Get a share link for a conversation
         */
        get: operations["share-chat-get_share_link"];
        put?: never;
        /**
         * Create Share Link
         * @description Create a share link for a conversation
         */
        post: operations["share-chat-create_share_link"];
        /**
         * Revoke Share Link
         * @description Revoke a share link for a conversation
         */
        delete: operations["share-chat-revoke_share_link"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/share-chat/conversations/shared/{share_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Shared Conversation
         * @description Get message history for a shared conversation by share ID (no authentication required)
         */
        get: operations["share-chat-get_shared_conversation"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/gcp-accounts/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /**
         * Update Gcp Account
         * @description Update a GCP account.
         */
        put: operations["gcp-accounts-update_gcp_account"];
        /**
         * Create Gcp Account
         * @description Create new GCP account.
         */
        post: operations["gcp-accounts-create_gcp_account"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/agents-builtin-tools/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Workspace Agent Builtin Tools
         * @description Get built-in tools for all agents in the current workspace
         */
        get: operations["agents-builtin-tools-get_workspace_agent_builtin_tools"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/agents-builtin-tools/{agent_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        /**
         * Bulk Update Agent Builtin Tools
         * @description Bulk update builtin tools for an agent
         */
        patch: operations["agents-builtin-tools-bulk_update_agent_builtin_tools"];
        trace?: never;
    };
    "/api/v1/agents-connections/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Workspace Agent Connections
         * @description Get connections for all agents in the current workspace
         */
        get: operations["agents-connections-get_workspace_agent_connections"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/agents-connections/{agent_id}/connections": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create Agent Connection
         * @description Create a connection for an agent
         */
        post: operations["agents-connections-create_agent_connection"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/agents-connections/{agent_id}/connections/{connection_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        /**
         * Delete Agent Connection
         * @description Delete a connection from an agent
         */
        delete: operations["agents-connections-delete_agent_connection"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/onboarding/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get Onboarding Status */
        get: operations["onboarding-get_onboarding_status"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/onboarding/workspace": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Create Workspace */
        post: operations["onboarding-create_workspace"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/onboarding/connect-aws": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Connect Aws */
        post: operations["onboarding-connect_aws"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/onboarding/connect-gcp": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Connect Gcp */
        post: operations["onboarding-connect_gcp"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/onboarding/connect-azure": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Connect Azure */
        post: operations["onboarding-connect_azure"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/onboarding/task-template": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get Task Template */
        get: operations["onboarding-get_task_template"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/onboarding/complete-task-template": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Complete Task Template
         * @description Complete the task template step by either selecting a template or skipping.
         *     This marks step 3 of onboarding as completed.
         */
        post: operations["onboarding-complete_task_template"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/sample-data/resources": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create Sample Resources
         * @description Create sample resources for the current workspace.
         *
         *     Args:
         *         count: Number of resources to create (1-500, default: 50)
         *
         *     Returns:
         *         Summary of created resources
         */
        post: operations["sample-data-create_sample_resources"];
        /**
         * Clear Sample Resources
         * @description Clear all resources for the current workspace.
         *
         *     ⚠️ WARNING: This will delete ALL resources in the workspace.
         *     Use with extreme caution!
         *
         *     Args:
         *         confirm: Must be set to true to confirm the deletion
         *
         *     Returns:
         *         Summary of deleted resources
         */
        delete: operations["sample-data-clear_sample_resources"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/sample-data/resources/preview": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Preview Sample Resources
         * @description Preview sample resources without creating them in the database.
         *
         *     Args:
         *         count: Number of resources to preview (1-20, default: 5)
         *
         *     Returns:
         *         Preview of sample resources that would be created
         */
        get: operations["sample-data-preview_sample_resources"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        /** AWSAccountCreate */
        AWSAccountCreate: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            environment: components["schemas"]["AccountEnvironement"];
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /** Account Id */
            account_id: string;
            /** Access Key Id */
            access_key_id: string;
            /** Secret Access Key */
            secret_access_key: string;
            /**
             * Regions
             * @default []
             */
            regions: string[];
            /**
             * Types
             * @default []
             */
            types: string[];
            /** Cron Pattern */
            cron_pattern: string;
        };
        /** AWSAccountDetail */
        AWSAccountDetail: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            environment: components["schemas"]["AccountEnvironement"];
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Access Key Id */
            access_key_id: string;
            /** Secret Access Key */
            secret_access_key: string;
            /** Account Id */
            account_id: string;
        };
        /** AWSAccountPublic */
        AWSAccountPublic: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            environment: components["schemas"]["AccountEnvironement"];
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /**
             * Id
             * Format: uuid
             */
            id: string;
        };
        /** AWSAccountUpdate */
        AWSAccountUpdate: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            environment: components["schemas"]["AccountEnvironement"];
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /** Account Id */
            account_id?: string | null;
            /** Access Key Id */
            access_key_id?: string | null;
            /** Secret Access Key */
            secret_access_key?: string | null;
            /**
             * Regions
             * @default []
             */
            regions: string[];
            /**
             * Types
             * @default []
             */
            types: string[];
            /** Cron Pattern */
            cron_pattern: string;
        };
        /** AWSOnboardingCreate */
        AWSOnboardingCreate: {
            /** Aws Access Key Id */
            aws_access_key_id: string;
            /** Aws Secret Access Key */
            aws_secret_access_key: string;
            /** Aws Default Region */
            aws_default_region: string;
        };
        /**
         * AccountEnvironement
         * @enum {string}
         */
        AccountEnvironement: AccountEnvironement;
        /** ActivationResponse */
        ActivationResponse: {
            /** Message */
            message: string;
            /**
             * Expires At
             * Format: date-time
             */
            expires_at: string;
        };
        /** ActivationResult */
        ActivationResult: {
            /** Success */
            success: boolean;
            /** Message */
            message: string;
            /** Redirect Url */
            redirect_url?: string | null;
            /** Welcome Message */
            welcome_message?: string | null;
        };
        /** Address */
        Address: {
            /** City */
            city?: string | null;
            /** Country */
            country?: string | null;
            /** Line1 */
            line1?: string | null;
            /** Line2 */
            line2?: string | null;
            /** Postal Code */
            postal_code?: string | null;
            /** State */
            state?: string | null;
        };
        /** AgentBuiltInToolBulkUpdateResult */
        AgentBuiltInToolBulkUpdateResult: {
            /**
             * Workspace Builtin Tool Id
             * Format: uuid
             */
            workspace_builtin_tool_id: string;
            /** Success */
            success: boolean;
            /** Error Message */
            error_message?: string | null;
        };
        /** AgentBuiltInToolPublic */
        AgentBuiltInToolPublic: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Name */
            name: string;
            /** Display Name */
            display_name: string;
            /** Description */
            description?: string | null;
            /** Required Permission */
            required_permission: boolean;
            /** Is Active */
            is_active: boolean;
        };
        /** AgentBuiltInToolUpdate */
        AgentBuiltInToolUpdate: {
            /**
             * Workspace Builtin Tool Id
             * Format: uuid
             */
            workspace_builtin_tool_id: string;
            /** Is Active */
            is_active: boolean;
        };
        /** AgentBuiltInToolsBulkUpdateResponse */
        AgentBuiltInToolsBulkUpdateResponse: {
            /** Total Count */
            total_count: number;
            /** Success Count */
            success_count: number;
            /** Failed Count */
            failed_count: number;
            /** Results */
            results: components["schemas"]["AgentBuiltInToolBulkUpdateResult"][];
        };
        /** AgentBuiltInToolsPublic */
        AgentBuiltInToolsPublic: {
            /**
             * Agent Id
             * Format: uuid
             */
            agent_id: string;
            /** Tools */
            tools: components["schemas"]["AgentBuiltInToolPublic"][];
        };
        /** AgentBuiltInToolsUpdateRequest */
        AgentBuiltInToolsUpdateRequest: {
            /** Agent Builtin Tools */
            agent_builtin_tools: components["schemas"]["AgentBuiltInToolUpdate"][];
        };
        /**
         * AgentConnectionCreateRequest
         * @description Request model for creating agent connection
         */
        AgentConnectionCreateRequest: {
            /**
             * Connection Id
             * Format: uuid
             */
            connection_id: string;
        };
        /**
         * AgentConnectionResponse
         * @description Response model for single operations
         */
        AgentConnectionResponse: {
            /** Success */
            success: boolean;
            /** Message */
            message: string;
        };
        /**
         * AgentConnectionsPublic
         * @description Public schema for agent connections
         */
        AgentConnectionsPublic: {
            /**
             * Agent Id
             * Format: uuid
             */
            agent_id: string;
            /** Connections */
            connections: components["schemas"]["ConnectionPublic"][];
        };
        /**
         * AgentExecutionConfig
         * @description Configuration for agent execution
         */
        AgentExecutionConfig: {
            /** Message */
            message: string;
            /**
             * Agent Id
             * Format: uuid
             */
            agent_id: string;
            /**
             * Context Ids
             * @default []
             */
            context_ids: string[];
            /** Conversation Name */
            conversation_name?: string | null;
        };
        /** AgentInstructionsUpdate */
        AgentInstructionsUpdate: {
            /** Instructions */
            instructions: string;
        };
        /** AgentPublic */
        AgentPublic: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Alias */
            alias: string;
            /** Title */
            title: string;
            /** Role */
            role: string;
            /** Goal */
            goal: string;
            /** Instructions */
            instructions: string;
            /** Is Active */
            is_active: boolean;
            type: components["schemas"]["AgentType"];
        };
        /** AgentStatusUpdate */
        AgentStatusUpdate: {
            /** Agent Status */
            agent_status: boolean;
        };
        /**
         * AgentType
         * @description Defines the supported types of agents in the system.
         * @enum {string}
         */
        AgentType: AgentType;
        /** AgentTypeUsage */
        AgentTypeUsage: {
            /** Agent Type */
            agent_type: string;
            /** Total Tokens */
            total_tokens: number;
        };
        /** AgentsBuiltInToolsResponse */
        AgentsBuiltInToolsResponse: {
            /** Agents Builtin Tools */
            agents_builtin_tools: components["schemas"]["AgentBuiltInToolsPublic"][];
        };
        /**
         * AgentsConnectionsResponse
         * @description Response model for multiple agents' connections
         */
        AgentsConnectionsResponse: {
            /** Agents Connections */
            agents_connections: components["schemas"]["AgentConnectionsPublic"][];
        };
        /** AgentsPublic */
        AgentsPublic: {
            /** Data */
            data: components["schemas"]["AgentPublic"][];
            /** Count */
            count: number;
        };
        /**
         * AlertCreate
         * @description Schema for creating a new alert
         */
        AlertCreate: {
            /**
             * Title
             * @description Alert title
             */
            title: string;
            /**
             * Description
             * @description Detailed alert description
             */
            description: string;
            /** @description Alert severity level */
            severity: components["schemas"]["AlertSeverity"];
        };
        /**
         * AlertList
         * @description Schema for list of alerts with pagination
         */
        AlertList: {
            /** Data */
            data: components["schemas"]["AlertResponse"][];
            /** Total */
            total: number;
        };
        /**
         * AlertResponse
         * @description Schema for alert response
         */
        AlertResponse: {
            /**
             * Title
             * @description Alert title
             */
            title: string;
            /**
             * Description
             * @description Detailed alert description
             */
            description: string;
            /** @description Alert severity level */
            severity: components["schemas"]["AlertSeverity"];
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            status: components["schemas"]["AlertStatus"];
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /** Updated At */
            updated_at?: string | null;
        };
        /**
         * AlertSeverity
         * @enum {string}
         */
        AlertSeverity: AlertSeverity;
        /**
         * AlertStatus
         * @enum {string}
         */
        AlertStatus: AlertStatus;
        /**
         * AlertStatusSummary
         * @description Summary of alerts by status for the last 30 days
         */
        AlertStatusSummary: {
            /**
             * Status Counts
             * @description Count of alerts by status
             */
            status_counts: {
                [key: string]: number;
            };
            /**
             * Total
             * @description Total number of alerts in the period
             */
            total: number;
        };
        /**
         * AlertUpdate
         * @description Schema for updating an existing alert
         */
        AlertUpdate: {
            /** Title */
            title?: string | null;
            /** Description */
            description?: string | null;
            severity?: components["schemas"]["AlertSeverity"] | null;
            status?: components["schemas"]["AlertStatus"] | null;
        };
        /**
         * AllConstantsResponse
         * @description Schema for returning all constants at once.
         */
        AllConstantsResponse: {
            /** Constants */
            constants: {
                [key: string]: components["schemas"]["ConstantCategory"];
            };
            /** Total Categories */
            total_categories: number;
            /** Generated At */
            generated_at: string;
        };
        /**
         * AsyncTaskStatus
         * @enum {string}
         */
        AsyncTaskStatus: AsyncTaskStatus;
        /** AttachmentConfirmRequest */
        AttachmentConfirmRequest: {
            /**
             * Uploaded Files
             * @description List of files that have been successfully uploaded.
             */
            uploaded_files: components["schemas"]["UploadedAttachmentInfo"][];
        };
        /** AttachmentDownloadResponse */
        AttachmentDownloadResponse: {
            /**
             * Attachment Id
             * Format: uuid
             * @description The ID of the attachment.
             */
            attachment_id: string;
            /**
             * Download Url
             * @description The presigned URL for downloading the file.
             */
            download_url: string;
        };
        /** AttachmentFileInfo */
        AttachmentFileInfo: {
            /**
             * File Id
             * @description Client-side unique ID for the file.
             */
            file_id: string;
            /**
             * Filename
             * @description Original name of the file.
             */
            filename: string;
            /**
             * Content Type
             * @description MIME type of the file.
             */
            content_type: string;
            /**
             * File Size
             * @description Size of the file in bytes.
             */
            file_size: number;
        };
        /** AttachmentMetadataResponse */
        AttachmentMetadataResponse: {
            /**
             * Id
             * Format: uuid
             * @description The ID of the attachment.
             */
            id: string;
            /**
             * Filename
             * @description The name of the file.
             */
            filename: string;
            /**
             * Original Filename
             * @description The original name of the file.
             */
            original_filename: string;
            /**
             * File Type
             * @description The MIME type of the file.
             */
            file_type: string;
            /**
             * File Size
             * @description The size of the file in bytes.
             */
            file_size: number;
            /**
             * Storage Key
             * @description The storage key of the file.
             */
            storage_key: string;
            /**
             * Created At
             * @description The creation timestamp of the attachment.
             */
            created_at: string;
        };
        /** AttachmentPresignedUrlRequest */
        AttachmentPresignedUrlRequest: {
            /**
             * Files
             * @description List of files to generate presigned URLs for.
             */
            files: components["schemas"]["AttachmentFileInfo"][];
        };
        /** AttachmentPresignedUrlResponse */
        AttachmentPresignedUrlResponse: {
            /**
             * Presigned Urls
             * @description List of presigned URLs and associated file info.
             */
            presigned_urls: components["schemas"]["app__schemas__message_attachment__PresignedUrlInfo"][];
        };
        /** AvailableUser */
        AvailableUser: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Email */
            email: string;
            /** Full Name */
            full_name: string;
        };
        /** AvailableUsersCurrentWorkspace */
        AvailableUsersCurrentWorkspace: {
            /** Data */
            data: components["schemas"]["AvailableUser"][];
            /** Count */
            count: number;
        };
        /** AzureOnboardingCreate */
        AzureOnboardingCreate: {
            /** App Id */
            app_id: string;
            /** Client Secret */
            client_secret: string;
            /** Tenant */
            tenant: string;
        };
        /** BillingDetails */
        BillingDetails: {
            address: components["schemas"]["Address"];
            /** Email */
            email?: string | null;
            /** Name */
            name?: string | null;
            /** Phone */
            phone?: string | null;
        };
        /** Body_login-login_access_token */
        "Body_login-login_access_token": {
            /** Grant Type */
            grant_type?: string;
            /** Username */
            username: string;
            /** Password */
            password: string;
            /**
             * Scope
             * @default
             */
            scope: string;
            /** Client Id */
            client_id?: string | null;
            /** Client Secret */
            client_secret?: string | null;
            /**
             * Slackoauth
             * @default false
             */
            slackOAuth: boolean;
            /** Appid */
            appId?: string | null;
            /** Teamid */
            teamId?: string | null;
        };
        /** Body_notifications-list_notifications */
        "Body_notifications-list_notifications": {
            /** Type */
            type?: components["schemas"]["NotificationType"][] | null;
            /** Status */
            status?: components["schemas"]["NotificationStatus"][] | null;
        };
        /** BuiltInToolPublic */
        BuiltInToolPublic: {
            /** Name */
            name: string;
            /** Display Name */
            display_name: string;
            /** Description */
            description: string;
        };
        /** BuiltInToolUpdateRequest */
        BuiltInToolUpdateRequest: {
            /** Required Permission */
            required_permission: boolean;
        };
        /**
         * BuiltinInstallRequest
         * @description Request model for installing builtin connections with custom configuration
         */
        BuiltinInstallRequest: {
            /**
             * Config Override
             * @description Configuration override for the builtin connection (e.g., environment variables)
             */
            config_override?: Record<string, never> | null;
        };
        /** CardDetails */
        CardDetails: {
            /** Brand */
            brand: string;
            /** Country */
            country: string;
            /** Display Brand */
            display_brand: string;
            /** Exp Month */
            exp_month: number;
            /** Exp Year */
            exp_year: number;
            /** Last4 */
            last4: string;
        };
        /**
         * ChartType
         * @description Enum for different types of charts available in the system
         * @enum {string}
         */
        ChartType: ChartType;
        /** CheckoutSessionResponse */
        CheckoutSessionResponse: {
            /** Checkout Session Url */
            checkout_session_url: string;
        };
        /**
         * CloudProvider
         * @enum {string}
         */
        CloudProvider: CloudProvider;
        /** CloudSyncConfigCreate */
        CloudSyncConfigCreate: {
            /**
             * Include Stopped Resources
             * @default false
             */
            include_stopped_resources: boolean;
            /**
             * Refresh Interval
             * @default 60
             */
            refresh_interval: number;
            /**
             * Selected Resources
             * @default []
             */
            selected_resources: string[];
            /**
             * Is Enabled
             * @default true
             */
            is_enabled: boolean;
            /**
             * Connection Id
             * Format: uuid
             */
            connection_id: string;
        };
        /** CloudSyncConfigPublic */
        CloudSyncConfigPublic: {
            /**
             * Include Stopped Resources
             * @default false
             */
            include_stopped_resources: boolean;
            /**
             * Refresh Interval
             * @default 60
             */
            refresh_interval: number;
            /**
             * Selected Resources
             * @default []
             */
            selected_resources: string[];
            /**
             * Is Enabled
             * @default true
             */
            is_enabled: boolean;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /**
             * Connection Id
             * Format: uuid
             */
            connection_id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
            /** Last Sync At */
            last_sync_at?: string | null;
        };
        /** CloudSyncConfigUpdate */
        CloudSyncConfigUpdate: {
            /** Include Stopped Resources */
            include_stopped_resources?: boolean | null;
            /** Refresh Interval */
            refresh_interval?: number | null;
            /** Selected Resources */
            selected_resources?: string[] | null;
            /** Is Enabled */
            is_enabled?: boolean | null;
        };
        /**
         * ConfirmUploadsRequest
         * @description Request to confirm file uploads and start ingestion
         */
        ConfirmUploadsRequest: {
            /**
             * Uploaded Files
             * @description Information about successfully uploaded files
             */
            uploaded_files: components["schemas"]["UploadedFileInfo"][];
        };
        /** ConnectionCreate */
        ConnectionCreate: {
            /** Name */
            name: string;
            /** Prefix */
            prefix: string;
            /** @default mcp */
            type: components["schemas"]["ConnectionType"];
            /** @default streamable_http */
            transport_type: components["schemas"]["ConnectionTransport"];
            /**
             * Config
             * @default {}
             */
            config: Record<string, never>;
            /**
             * Is Active
             * @default true
             */
            is_active: boolean;
            /**
             * Tool List
             * @default []
             */
            tool_list: string[];
            /**
             * Tool Permissions
             * @default []
             */
            tool_permissions: string[];
            /**
             * Tool Enabled
             * @default []
             */
            tool_enabled: string[];
            /**
             * Tool Schemas
             * @default []
             */
            tool_schemas: Record<string, never>[];
            /** @default connected */
            status: components["schemas"]["ConnectionStatus"];
            /**
             * Status Message
             * @default
             */
            status_message: string;
            /**
             * Status Updated At
             * Format: date-time
             * @default 2025-07-31T17:27:52.817527
             */
            status_updated_at: string;
        };
        /** ConnectionPublic */
        ConnectionPublic: {
            /** Name */
            name: string;
            /** Prefix */
            prefix: string;
            type: components["schemas"]["ConnectionType"];
            transport_type: components["schemas"]["ConnectionTransport"];
            /** Config */
            config: Record<string, never>;
            /** Is Active */
            is_active: boolean;
            /** Tool List */
            tool_list: string[];
            /** Tool Permissions */
            tool_permissions: string[];
            /** Tool Enabled */
            tool_enabled: string[];
            status: components["schemas"]["ConnectionStatus"];
            /** Status Message */
            status_message: string;
            /**
             * Status Updated At
             * Format: date-time
             */
            status_updated_at: string;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Workspace Id */
            workspace_id: string | null;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
        };
        /**
         * ConnectionStatus
         * @enum {string}
         */
        ConnectionStatus: ConnectionStatus;
        /**
         * ConnectionTransport
         * @enum {string}
         */
        ConnectionTransport: ConnectionTransport;
        /**
         * ConnectionType
         * @enum {string}
         */
        ConnectionType: ConnectionType;
        /** ConnectionUpdate */
        ConnectionUpdate: {
            /** Name */
            name?: string | null;
            /** Prefix */
            prefix?: string | null;
            type?: components["schemas"]["ConnectionType"] | null;
            transport_type?: components["schemas"]["ConnectionTransport"] | null;
            /** Config */
            config?: Record<string, never> | null;
            /** Is Active */
            is_active?: boolean | null;
            /** Tool List */
            tool_list?: string[] | null;
            /** Tool Permissions */
            tool_permissions?: string[] | null;
            /** Tool Enabled */
            tool_enabled?: string[] | null;
            /** Tool Schemas */
            tool_schemas?: Record<string, never>[] | null;
            status?: components["schemas"]["ConnectionStatus"] | null;
            /** Status Message */
            status_message?: string | null;
            /** Status Updated At */
            status_updated_at?: string | null;
        };
        /** ConnectionsPublic */
        ConnectionsPublic: {
            /** Data */
            data: components["schemas"]["ConnectionPublic"][];
            /** Count */
            count: number;
        };
        /**
         * ConstantCategoriesResponse
         * @description Schema for listing all available constant categories.
         */
        ConstantCategoriesResponse: {
            /** Categories */
            categories: string[];
            /** Total */
            total: number;
        };
        /**
         * ConstantCategory
         * @description Schema for a constant category response.
         */
        ConstantCategory: {
            /** Category */
            category: string;
            /** Data */
            data: components["schemas"]["ConstantOption"][];
            /** Description */
            description: string;
            /** Total */
            total: number;
            /** Last Updated */
            last_updated: string;
        };
        /**
         * ConstantOption
         * @description Schema for a single constant option.
         */
        ConstantOption: {
            /** Value */
            value: string;
            /** Label */
            label: string;
            /** Key */
            key: string;
            /** Metadata */
            metadata?: Record<string, never> | null;
        };
        /** ConversationPublic */
        ConversationPublic: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * Agent Id
             * Format: uuid
             */
            agent_id: string;
            /** Name */
            name: string;
            resource?: components["schemas"]["ResourcePublicSimple"] | null;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
        };
        /** ConversationRenameRequest */
        ConversationRenameRequest: {
            /** Name */
            name: string;
        };
        /**
         * ConversationsPublic
         * @description Response model for paginated conversations list.
         */
        ConversationsPublic: {
            /** Data */
            data: components["schemas"]["ConversationPublic"][];
            /** Total */
            total: number;
        };
        /** DailyMessageVolume */
        DailyMessageVolume: {
            /**
             * Date
             * Format: date-time
             */
            date: string;
            /** Message Count */
            message_count: number;
        };
        /** DailyTokenUsage */
        DailyTokenUsage: {
            /**
             * Date
             * Format: date-time
             */
            date: string;
            /** Tokens */
            total_tokens: number;
        };
        /** Dashboard */
        Dashboard: {
            /**
             * Id
             * Format: uuid
             */
            id?: string;
            /**
             * Conversation Id
             * Format: uuid
             */
            conversation_id: string;
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /** Title */
            title: string;
            /**
             * Grid Config
             * @default {
             *       "columns": 12
             *     }
             */
            grid_config: Record<string, never>;
            /**
             * Widgets
             * @default []
             */
            widgets: unknown[];
            /**
             * Created At
             * Format: date-time
             */
            created_at?: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at?: string;
        };
        /** DocumentKBRead */
        DocumentKBRead: {
            /** Name */
            name: string;
            type: components["schemas"]["DocumentType"];
            /** Url */
            url?: string | null;
            /**
             * Deep Crawl
             * @default false
             */
            deep_crawl: boolean;
            /** File Name */
            file_name?: string | null;
            /** File Type */
            file_type?: string | null;
            /** Object Name */
            object_name?: string | null;
            /** @default PENDING */
            embed_status: components["schemas"]["AsyncTaskStatus"];
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * Kb Id
             * Format: uuid
             */
            kb_id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
            /** Is Deleted */
            is_deleted: boolean;
            /** Parent Id */
            parent_id?: string | null;
            /**
             * Children
             * @default []
             */
            children: components["schemas"]["DocumentKBRead"][];
        };
        /**
         * DocumentType
         * @enum {string}
         */
        DocumentType: DocumentType;
        /** DocumentsKBRead */
        DocumentsKBRead: {
            /** Data */
            data: components["schemas"]["DocumentKBRead"][];
            /** Count */
            count: number;
        };
        /**
         * EnterpriseEnquiryMessageResponse
         * @description Response model for enterprise enquiry status messages
         */
        EnterpriseEnquiryMessageResponse: {
            /** Message */
            message: string;
        };
        /** EnterpriseEnquiryRequest */
        EnterpriseEnquiryRequest: {
            /** First Name */
            first_name: string;
            /** Last Name */
            last_name: string;
            /** Work Title */
            work_title: string;
            /** Work Email */
            work_email: string;
            /** Company Name */
            company_name: string;
            /** Estimated Monthly Cost */
            estimated_monthly_cost: string;
            /** Message */
            message: string;
            /**
             * Product Id
             * Format: uuid
             */
            product_id: string;
        };
        /**
         * FeedbackType
         * @description Enumeration for feedback types on agent responses.
         * @enum {string}
         */
        FeedbackType: FeedbackType;
        /**
         * FileContentResponse
         * @description Response for file content requests
         */
        FileContentResponse: {
            /**
             * Content
             * @description File content
             */
            content: string;
            /**
             * Path
             * @description File path
             */
            path: string;
            /**
             * Name
             * @description File name
             */
            name: string;
        };
        /**
         * FileInfo
         * @description Information about a file to generate a presigned URL for
         */
        FileInfo: {
            /**
             * File Id
             * @description Client-side ID for tracking this file
             */
            file_id: string;
            /**
             * Filename
             * @description Original filename
             */
            filename: string;
            /**
             * Content Type
             * @description File MIME type
             */
            content_type: string;
            /**
             * File Size
             * @description File size in bytes
             */
            file_size: number;
        };
        /**
         * FileListResponse
         * @description Response for directory listing
         */
        FileListResponse: {
            /**
             * Files
             * @description List of files and directories
             */
            files: components["schemas"]["FileNode"][];
            /**
             * Current Path
             * @description Current directory path
             */
            current_path: string;
        };
        /**
         * FileNode
         * @description Simplified file node matching frontend interface
         */
        FileNode: {
            /**
             * Id
             * @description Unique identifier for the file/directory
             */
            id: string;
            /**
             * Name
             * @description File or directory name
             */
            name: string;
            /**
             * Path
             * @description Full path
             */
            path: string;
            /** @description File type (file or directory) */
            type: components["schemas"]["FileType"];
            /**
             * Children
             * @description Child nodes for directories
             */
            children?: components["schemas"]["FileNode"][] | null;
            /**
             * Content
             * @description File content for files
             */
            content?: string | null;
        };
        /**
         * FileType
         * @enum {string}
         */
        FileType: FileType;
        /** GCPAccountCreate */
        GCPAccountCreate: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            environment: components["schemas"]["AccountEnvironement"];
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /** Key */
            key: string;
        };
        /** GCPAccountDetail */
        GCPAccountDetail: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            environment: components["schemas"]["AccountEnvironement"];
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Key */
            key: string;
        };
        /** GCPAccountPublic */
        GCPAccountPublic: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            environment: components["schemas"]["AccountEnvironement"];
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /**
             * Id
             * Format: uuid
             */
            id: string;
        };
        /** GCPAccountUpdate */
        GCPAccountUpdate: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            environment: components["schemas"]["AccountEnvironement"];
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /** Key */
            key: string;
        };
        /** GCPOnboardingCreate */
        GCPOnboardingCreate: {
            /** Google Service Account Key */
            google_service_account_key: string;
            /** Provider Id */
            provider_id: string;
        };
        /** HTTPValidationError */
        HTTPValidationError: {
            /** Detail */
            detail?: components["schemas"]["ValidationError"][];
        };
        /** InvoiceResponse */
        InvoiceResponse: {
            /** Id */
            id: string;
            /** Customer */
            customer: string;
            /** Status */
            status: string;
            /** Amount Due */
            amount_due: number;
            /** Amount Paid */
            amount_paid: number;
            /** Amount Remaining */
            amount_remaining: number;
            /** Currency */
            currency: string;
            /** Invoice Pdf */
            invoice_pdf?: string | null;
            /** Created */
            created: number;
            /** Due Date */
            due_date?: number | null;
            /** Paid */
            paid: boolean;
            /** Payment Intent */
            payment_intent?: string | null;
            /** Subscription */
            subscription?: string | null;
            /** Total */
            total: number;
        };
        /**
         * KBAccessLevel
         * @enum {string}
         */
        KBAccessLevel: KBAccessLevel;
        /** KBCreate */
        KBCreate: {
            /** Title */
            title: string;
            /** Description */
            description?: string | null;
            /** @default private */
            access_level: components["schemas"]["KBAccessLevel"];
            /** @default manual */
            usage_mode: components["schemas"]["KBUsageMode"];
            /**
             * Tags
             * @default []
             */
            tags: string[];
            /**
             * Allowed Users
             * @default []
             */
            allowed_users: string[];
        };
        /** KBRead */
        KBRead: {
            /** Title */
            title: string;
            /** Description */
            description?: string | null;
            /** @default private */
            access_level: components["schemas"]["KBAccessLevel"];
            /** @default manual */
            usage_mode: components["schemas"]["KBUsageMode"];
            /**
             * Tags
             * @default []
             */
            tags: string[];
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
            /** Is Deleted */
            is_deleted: boolean;
            /** Allowed Users */
            allowed_users?: string[];
            /**
             * Owner Id
             * Format: uuid
             */
            owner_id: string;
        };
        /** KBUpdate */
        KBUpdate: {
            /** Title */
            title?: string | null;
            /** Description */
            description?: string | null;
            access_level?: components["schemas"]["KBAccessLevel"] | null;
            /** Tags */
            tags?: string[] | null;
            /** Allowed Users */
            allowed_users?: string[] | null;
            usage_mode?: components["schemas"]["KBUsageMode"] | null;
        };
        /**
         * KBUsageMode
         * @enum {string}
         */
        KBUsageMode: KBUsageMode;
        /** KBsRead */
        KBsRead: {
            /** Data */
            data: components["schemas"]["KBRead"][];
            /** Count */
            count: number;
        };
        /** Message */
        Message: {
            /** Content */
            content: string;
            /**
             * Role
             * @default user
             */
            role: string;
            /**
             * Is Interrupt
             * @default false
             */
            is_interrupt: boolean;
            /** Interrupt Message */
            interrupt_message?: string | null;
            /**
             * Id
             * Format: uuid
             */
            id?: string;
            /**
             * Conversation Id
             * Format: uuid
             */
            conversation_id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at?: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at?: string;
            /**
             * Message Metadata
             * @default {}
             */
            message_metadata: Record<string, never>;
            /**
             * Is Deleted
             * @default false
             */
            is_deleted: boolean;
        };
        /** MessageAgentThoughtPublic */
        MessageAgentThoughtPublic: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Position */
            position: number;
            /** Tool Name */
            tool_name: string;
            /** Tool Input */
            tool_input: Record<string, never>;
            /** Tool Output */
            tool_output: string;
            /**
             * Tool Runtime
             * @default 0
             */
            tool_runtime: number;
            /** Content */
            content: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
        };
        /** MessageAttachmentPublic */
        MessageAttachmentPublic: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Filename */
            filename: string;
            /** Original Filename */
            original_filename: string;
            /** File Type */
            file_type: string;
            /** File Size */
            file_size: number;
            /** Storage Key */
            storage_key: string;
            /** Thumbnail Key */
            thumbnail_key?: string | null;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
        };
        /**
         * MessageDisplayComponentPublic
         * @description Public schema for message display components
         */
        MessageDisplayComponentPublic: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            type: components["schemas"]["MessageDisplayComponentType"];
            chart_type: components["schemas"]["ChartType"] | null;
            /** Title */
            title: string | null;
            /** Description */
            description: string | null;
            /** Data */
            data: Record<string, never>;
            /** Config */
            config: Record<string, never>;
            /** Position */
            position: number;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
        };
        /**
         * MessageDisplayComponentType
         * @description Enum for display component types (currently supporting only tables and charts)
         * @enum {string}
         */
        MessageDisplayComponentType: MessageDisplayComponentType;
        /**
         * MessageFeedbackCreate
         * @description Schema for creating message feedback
         */
        MessageFeedbackCreate: {
            /** @description Type of feedback (good/bad) */
            feedback_type: components["schemas"]["FeedbackType"];
            /**
             * Reason
             * @description Optional reason for the feedback, required when feedback_type is BAD
             */
            reason?: string | null;
            /**
             * Additional Comments
             * @description Additional optional comments from the user
             */
            additional_comments?: string | null;
            /**
             * Message Id
             * Format: uuid
             */
            message_id: string;
        };
        /**
         * MessageFeedbackPublic
         * @description Public schema for message feedback responses
         */
        MessageFeedbackPublic: {
            /** @description Type of feedback (good/bad) */
            feedback_type: components["schemas"]["FeedbackType"];
            /**
             * Reason
             * @description Optional reason for the feedback, required when feedback_type is BAD
             */
            reason?: string | null;
            /**
             * Additional Comments
             * @description Additional optional comments from the user
             */
            additional_comments?: string | null;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * Message Id
             * Format: uuid
             */
            message_id: string;
            /**
             * User Id
             * Format: uuid
             */
            user_id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
        };
        /**
         * MessageFeedbackUpdate
         * @description Schema for updating message feedback
         */
        MessageFeedbackUpdate: {
            feedback_type?: components["schemas"]["FeedbackType"] | null;
            /** Reason */
            reason?: string | null;
            /** Additional Comments */
            additional_comments?: string | null;
        };
        /** MessagePublic */
        MessagePublic: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Content */
            content: string;
            /** Role */
            role: string;
            /** Is Interrupt */
            is_interrupt: boolean;
            /** Interrupt Message */
            interrupt_message?: string | null;
            /** Thoughts */
            thoughts: components["schemas"]["MessageAgentThoughtPublic"][];
            /** Display Components */
            display_components?: components["schemas"]["MessageDisplayComponentPublic"][] | null;
            /** Attachments */
            attachments?: components["schemas"]["MessageAttachmentPublic"][] | null;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
        };
        /** MessagePublicList */
        MessagePublicList: {
            /** Messages */
            messages: components["schemas"]["MessagePublic"][];
            /** Resource Id */
            resource_id?: string | null;
            /**
             * Has Report
             * @default false
             */
            has_report: boolean;
            /**
             * Has Dashboard
             * @default false
             */
            has_dashboard: boolean;
            /** Total */
            total: number;
            /**
             * Is Streaming Active
             * @default false
             */
            is_streaming_active: boolean;
            /**
             * Stream Status
             * @default completed
             */
            stream_status: string;
            /**
             * Last Stream Position
             * @default 0
             */
            last_stream_position: number;
        };
        /** MessageResponse */
        MessageResponse: {
            /** Message */
            message: string;
        };
        /** MessageStatistics */
        MessageStatistics: {
            /** Total Messages */
            total_messages: number;
            /** Average Response Time */
            average_response_time: number;
            /** Average Input Tokens per Message */
            average_input_tokens_per_message: number;
            /** Average Output Tokens per Message */
            average_output_tokens_per_message: number;
            /** Daily Message Volume */
            daily_message_volume: components["schemas"]["DailyMessageVolume"][];
            /** Token Distribution by Message Length */
            token_distribution_by_message_length: components["schemas"]["TokenDistributionCategory"][];
        };
        /** MessageStreamInput */
        MessageStreamInput: {
            /** Content */
            content: string;
            /** Resume */
            resume: boolean;
            /** Approve */
            approve: boolean;
            /** Display Components */
            display_components?: components["schemas"]["MessageDisplayComponentPublic"][] | null;
            /** Attachment Ids */
            attachment_ids?: string[] | null;
            /** Resource Id */
            resource_id?: string | null;
        };
        /** ModuleSetting */
        ModuleSetting: {
            /** Key */
            key: string;
            /**
             * Value
             * @default {}
             */
            value: Record<string, never>;
            /**
             * Created At
             * Format: date-time
             */
            created_at?: string;
            /** Updated At */
            updated_at?: string | null;
        };
        /** NewPassword */
        NewPassword: {
            /** Token */
            token: string;
            /** New Password */
            new_password: string;
        };
        /**
         * NotificationList
         * @description Response model for paginated notifications list.
         *
         *     Attributes:
         *         data: List of notification items
         *         count: Total number of items available (before pagination)
         */
        NotificationList: {
            /** Data */
            data: components["schemas"]["NotificationResponse"][];
            /** Count */
            count: number;
        };
        /** NotificationResponse */
        NotificationResponse: {
            /** Title */
            title: string;
            /** Message */
            message: string;
            /** @default info */
            type: components["schemas"]["NotificationType"];
            /** @default unread */
            status: components["schemas"]["NotificationStatus"];
            /**
             * Notification Metadata
             * @description Metadata for the notification
             */
            notification_metadata?: Record<string, never>;
            /**
             * Requires Action
             * @default false
             */
            requires_action: boolean;
            /**
             * Action Url
             * @description URL for direct action
             */
            action_url?: string | null;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * User Id
             * Format: uuid
             */
            user_id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
            /** Read At */
            read_at?: string | null;
            /** Expires At */
            expires_at?: string | null;
        };
        /**
         * NotificationStatus
         * @enum {string}
         */
        NotificationStatus: NotificationStatus;
        /**
         * NotificationType
         * @enum {string}
         */
        NotificationType: NotificationType;
        /** OnboardingStatus */
        OnboardingStatus: {
            /** Is Completed */
            is_completed: boolean;
            /** Current Step */
            current_step: number;
        };
        /** PaginationMeta */
        PaginationMeta: {
            /**
             * Page
             * @description Current page number (1-based)
             */
            page: number;
            /**
             * Take
             * @description Number of items per page
             */
            take: number;
            /**
             * Total Items
             * @description Total number of items available
             */
            total_items: number;
            /**
             * Total Pages
             * @description Total number of pages
             */
            total_pages: number;
            /**
             * Has Previous
             * @description Whether there is a previous page
             */
            has_previous: boolean;
            /**
             * Has Next
             * @description Whether there is a next page
             */
            has_next: boolean;
            /**
             * Start Index
             * @description Starting index of current page items
             */
            start_index: number;
            /**
             * End Index
             * @description Ending index of current page items
             */
            end_index: number;
        };
        /** PaymentMethodResponse */
        PaymentMethodResponse: {
            /** Id */
            id: string;
            billing_details: components["schemas"]["BillingDetails"];
            card: components["schemas"]["CardDetails"];
            /** Created */
            created: number;
            /** Customer */
            customer: string;
            /** Livemode */
            livemode: boolean;
            /** Type */
            type: string;
        };
        /** PlanChangeRequestCreate */
        PlanChangeRequestCreate: {
            /** First Name */
            first_name: string;
            /** Last Name */
            last_name: string;
            /** Work Email */
            work_email: string;
            /** Work Title */
            work_title: string;
            /** Company Name */
            company_name: string;
            /** Reason */
            reason: string;
            /**
             * Current Product Id
             * Format: uuid
             */
            current_product_id: string;
            /**
             * Requested Price Id
             * Format: uuid
             */
            requested_price_id: string;
        };
        /** PlanChangeRequestResponse */
        PlanChangeRequestResponse: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** First Name */
            first_name: string;
            /** Last Name */
            last_name: string;
            /** Work Email */
            work_email: string;
            /** Work Title */
            work_title: string;
            /** Company Name */
            company_name: string;
            /** Reason */
            reason: string;
            /** Status */
            status: string;
            /**
             * Customer Id
             * Format: uuid
             */
            customer_id: string;
            /**
             * Current Product Id
             * Format: uuid
             */
            current_product_id: string;
            /**
             * Requested Product Id
             * Format: uuid
             */
            requested_product_id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
        };
        /**
         * PresignedUrlRequest
         * @description Request to generate presigned URLs for file uploads
         */
        PresignedUrlRequest: {
            /**
             * Kb Id
             * @description ID of the knowledge base to upload files to
             */
            kb_id: string;
            /**
             * Files
             * @description Information about files to generate presigned URLs for
             */
            files: components["schemas"]["FileInfo"][];
        };
        /**
         * PresignedUrlResponse
         * @description Response with presigned URLs for file uploads
         */
        PresignedUrlResponse: {
            /**
             * Kb Id
             * @description Knowledge base ID
             */
            kb_id: string;
            /**
             * Presigned Urls
             * @description Generated presigned URLs
             */
            presigned_urls: components["schemas"]["app__schemas__kb__PresignedUrlInfo"][];
        };
        /** PriceResponse */
        PriceResponse: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Stripe Price Id */
            stripe_price_id: string;
            /**
             * Product Id
             * Format: uuid
             */
            product_id: string;
            /** Active */
            active: boolean;
            /** Amount */
            amount: number;
            /** Currency */
            currency: string;
            /** Interval */
            interval: string;
        };
        /** ProductResponse */
        ProductResponse: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Name */
            name: string;
            /** Description */
            description: string;
            /** Stripe Product Id */
            stripe_product_id: string;
            /** Active */
            active: boolean;
            /**
             * Prices
             * @default []
             */
            prices: components["schemas"]["PriceResponse"][] | null;
            quota_definition?: components["schemas"]["QuotaDefinitionResponse"] | null;
            /** Is Custom */
            is_custom: boolean;
        };
        /** QuotaDefinitionResponse */
        QuotaDefinitionResponse: {
            /** Max Workspaces */
            max_workspaces: number;
            /** Max Members Per Workspace */
            max_members_per_workspace: number;
            /** Max Fast Requests Per Month */
            max_fast_requests_per_month: number | null;
        };
        /** QuotaInfo */
        QuotaInfo: {
            /** Quota Used */
            quota_used: number;
            /** Quota Limit */
            quota_limit: number;
            /** Quota Remaining */
            quota_remaining: number;
            /** Usage Percentage */
            usage_percentage: number;
        };
        /** RecommendationCreate */
        RecommendationCreate: {
            /** Type */
            type: string;
            /** Title */
            title: string;
            /** Description */
            description: string;
            /** Potential Savings */
            potential_savings: number;
            /** Effort */
            effort: string;
            /** Risk */
            risk: string;
            /** @default pending */
            status: components["schemas"]["RecommendationStatus"];
            /**
             * Resource Id
             * Format: uuid
             */
            resource_id: string;
        };
        /** RecommendationOveralPublic */
        RecommendationOveralPublic: {
            /** Total Resource Scanned */
            total_resource_scanned: number;
            /** Total Well Optimized */
            total_well_optimized: number;
            /** Total Optimization Opportunities */
            total_optimization_opportunities: number;
            /** Total Estimated Saving Amount */
            total_estimated_saving_amount: number;
        };
        /** RecommendationPublic */
        RecommendationPublic: {
            /** Type */
            type: string;
            /** Title */
            title: string;
            /** Description */
            description: string;
            /** Potential Savings */
            potential_savings: number;
            /** Effort */
            effort: string;
            /** Risk */
            risk: string;
            /** @default pending */
            status: components["schemas"]["RecommendationStatus"];
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * Resource Id
             * Format: uuid
             */
            resource_id: string;
            /** Resource Name */
            resource_name: string;
            /** Resource Type */
            resource_type: string;
            resource_provider: components["schemas"]["CloudProvider"];
            resource_category: components["schemas"]["ResourceCategory"];
        };
        /**
         * RecommendationStatus
         * @enum {string}
         */
        RecommendationStatus: RecommendationStatus;
        /** RecommendationUpdate */
        RecommendationUpdate: {
            /** Type */
            type?: string | null;
            /** Title */
            title?: string | null;
            /** Description */
            description?: string | null;
            /** Potential Savings */
            potential_savings?: number | null;
            /** Effort */
            effort?: string | null;
            /** Risk */
            risk?: string | null;
            status?: components["schemas"]["RecommendationStatus"] | null;
        };
        /** RecommendationsPublic */
        RecommendationsPublic: {
            /** Data */
            data: components["schemas"]["RecommendationPublic"][];
            meta: components["schemas"]["PaginationMeta"];
        };
        /** Report */
        Report: {
            /**
             * Id
             * Format: uuid
             */
            id?: string;
            /**
             * Conversation Id
             * Format: uuid
             */
            conversation_id: string;
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /** Title */
            title: string;
            /** Description */
            description?: string | null;
            /**
             * Sections
             * @default {}
             */
            sections: Record<string, never>;
            /**
             * Executive Summary
             * @default {}
             */
            executive_summary: Record<string, never>;
            /**
             * Created At
             * Format: date-time
             */
            created_at?: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at?: string;
        };
        /** ResendActivationRequest */
        ResendActivationRequest: {
            /**
             * Email
             * Format: email
             */
            email: string;
            /** Captcha Token */
            captcha_token: string;
        };
        /**
         * ResourceCategory
         * @enum {string}
         */
        ResourceCategory: ResourceCategory;
        /** ResourcePublic */
        ResourcePublic: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Resource Id */
            resource_id: string;
            /** Name */
            name: string;
            /** Region */
            region: string;
            provider: components["schemas"]["CloudProvider"];
            category: components["schemas"]["ResourceCategory"];
            /** Type */
            type: string;
            status: components["schemas"]["ResourceStatus"];
            /** Total Recommendation */
            total_recommendation: number;
            /** Total Potential Saving */
            total_potential_saving: number;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
        };
        /** ResourcePublicSimple */
        ResourcePublicSimple: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Name */
            name: string;
        };
        /**
         * ResourceStatus
         * @enum {string}
         */
        ResourceStatus: ResourceStatus;
        /** ResourceTypeInfo */
        ResourceTypeInfo: {
            /** Resource Type */
            resource_type: string;
            category: components["schemas"]["ResourceCategory"];
            /** Display Name */
            display_name: string;
            /** Description */
            description: string;
        };
        /** ResourceTypesResponse */
        ResourceTypesResponse: {
            cloud_provider: components["schemas"]["CloudProvider"];
            /** Resource Types */
            resource_types: components["schemas"]["ResourceTypeInfo"][];
        };
        /** ResourcesPublic */
        ResourcesPublic: {
            /** Data */
            data: components["schemas"]["ResourcePublic"][];
            meta: components["schemas"]["PaginationMeta"];
        };
        /**
         * RunModeEnum
         * @enum {string}
         */
        RunModeEnum: RunModeEnum;
        /** ShareResponse */
        ShareResponse: {
            /**
             * Share Id
             * Format: uuid
             */
            share_id: string;
            /** Is Shared */
            is_shared: boolean;
            /**
             * Shared At
             * Format: date-time
             */
            shared_at: string;
            /**
             * Shared By
             * Format: uuid
             */
            shared_by: string;
        };
        /** SimpleMessage */
        SimpleMessage: {
            /** Message */
            message: string;
        };
        /** StreamResponse */
        StreamResponse: {
            /** Type */
            type: string;
            /** Content */
            content?: string | null;
            /** Message Id */
            message_id?: string | null;
        };
        /** SubscriptionStatus */
        SubscriptionStatus: {
            /** Id */
            id: string;
            /** Customer Id */
            customer_id: string;
            /** Status */
            status: string;
            /**
             * Current Period End
             * Format: date-time
             */
            current_period_end: string;
            /** Cancel At */
            cancel_at?: string | null;
            /** Product Name */
            product_name: string;
            /** Product Id */
            product_id?: string | null;
            /** Price Amount */
            price_amount: number;
            /** Price Currency */
            price_currency: string;
            /** Price Interval */
            price_interval: string;
        };
        /**
         * TaskCategoryEnum
         * @description Enumeration of possible task categories.
         * @enum {string}
         */
        TaskCategoryEnum: TaskCategoryEnum;
        /**
         * TaskComplexity
         * @enum {string}
         */
        TaskComplexity: TaskComplexity;
        /**
         * TaskCouldEnum
         * @enum {string}
         */
        TaskCouldEnum: TaskCouldEnum;
        /**
         * TaskCreate
         * @description Schema for creating a new task.
         */
        TaskCreate: {
            /** Title */
            title: string;
            /**
             * Description
             * @default
             */
            description: string | null;
            /** @default low */
            priority: components["schemas"]["TaskPriority"];
            /** Tags */
            tags?: string[];
            /** Schedule */
            schedule: string;
            agent_config: components["schemas"]["AgentExecutionConfig"];
        };
        /**
         * TaskDeleteResponse
         * @description Schema for task delete response.
         */
        TaskDeleteResponse: {
            /**
             * Status
             * @default success
             */
            status: string;
        };
        /**
         * TaskEnableRequest
         * @description Schema for task enable request.
         */
        TaskEnableRequest: {
            /**
             * Enable
             * @description Whether to enable or disable the task
             */
            enable: boolean;
        };
        /**
         * TaskExecutionStatus
         * @description Enumeration of execution statuses for task.
         *
         *     Attributes:
         *         RUNNING: Currently executing
         *         SUCCEEDED: Successfully completed
         *         FAILED: Execution failed
         *         CANCELLED: Execution cancelled
         *         REQUIRED_APPROVAL: Execution requires approval
         * @enum {string}
         */
        TaskExecutionStatus: TaskExecutionStatus;
        /**
         * TaskHistoriesResponse
         * @description Schema for task histories response.
         */
        TaskHistoriesResponse: {
            /** Data */
            data: components["schemas"]["TaskHistoryResponse"][];
            /** Total */
            total: number;
        };
        /**
         * TaskHistoryResponse
         * @description Schema for task history.
         */
        TaskHistoryResponse: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * Task Id
             * Format: uuid
             */
            task_id: string;
            /**
             * Conversation Id
             * Format: uuid
             */
            conversation_id: string;
            status: components["schemas"]["TaskExecutionStatus"];
            /** Message */
            message?: string | null;
            /** Celery Task Id */
            celery_task_id?: string | null;
            /**
             * Start Time
             * Format: date-time
             */
            start_time: string;
            /** End Time */
            end_time?: string | null;
            /** Run Time */
            run_time?: number | null;
        };
        /**
         * TaskList
         * @description Schema for paginated task list.
         */
        TaskList: {
            /** Data */
            data?: components["schemas"]["TaskResponse"][];
            /**
             * Total
             * @default 0
             */
            total: number;
        };
        /**
         * TaskPriority
         * @description Enumeration of task priority levels.
         *
         *     Attributes:
         *         LOW: Regular priority, no urgency
         *         MEDIUM: Moderate priority, should be done soon
         *         HIGH: High priority, urgent attention needed
         *         CRITICAL: Critical priority, requires immediate attention
         * @enum {string}
         */
        TaskPriority: TaskPriority;
        /**
         * TaskResponse
         * @description Schema for task response.
         */
        TaskResponse: {
            /** Title */
            title: string;
            /**
             * Description
             * @default
             */
            description: string | null;
            /** @default low */
            priority: components["schemas"]["TaskPriority"];
            /** Tags */
            tags?: string[];
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /**
             * Owner Id
             * Format: uuid
             */
            owner_id: string;
            scheduled_status?: components["schemas"]["TaskScheduledStatus"] | null;
            execution_status?: components["schemas"]["TaskExecutionStatus"] | null;
            /** Error */
            error?: string | null;
            /** Last Run */
            last_run?: string | null;
            /** Next Run */
            next_run?: string | null;
            /** Schedule */
            schedule: string;
            agent_config: components["schemas"]["AgentExecutionConfig"];
            /**
             * Enable
             * @default true
             */
            enable: boolean;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
            /**
             * Created By
             * Format: uuid
             */
            created_by: string;
            /**
             * Updated By
             * Format: uuid
             */
            updated_by: string;
        };
        /**
         * TaskScheduledStatus
         * @description Enumeration of scheduled statuses for task.
         * @enum {string}
         */
        TaskScheduledStatus: TaskScheduledStatus;
        /**
         * TaskServiceEnum
         * @description Enumeration of possible task services.
         * @enum {string}
         */
        TaskServiceEnum: TaskServiceEnum;
        /**
         * TaskStopRequest
         * @description Schema for task stop request.
         */
        TaskStopRequest: {
            /**
             * Conversation Id
             * Format: uuid
             * @description Conversation ID to stop
             */
            conversation_id: string;
        };
        /**
         * TaskStopResponse
         * @description Schema for task stop response.
         */
        TaskStopResponse: {
            /**
             * Task Id
             * Format: uuid
             */
            task_id: string;
            /**
             * Conversation Id
             * Format: uuid
             */
            conversation_id: string;
            /** Status */
            status: string;
        };
        /** TaskTemplate */
        TaskTemplate: {
            /** Title */
            title: string;
            /** Description */
            description: string;
            /** Icon */
            icon: string;
            /** Impact */
            impact: string;
            complexity: components["schemas"]["TaskComplexity"];
            /** Duration */
            duration: string;
        };
        /** TaskTemplateCreate */
        TaskTemplateCreate: {
            /** Task */
            task: string;
            /** @default OTHER */
            category: components["schemas"]["TaskCategoryEnum"] | null;
            /** @default OTHER */
            service: components["schemas"]["TaskServiceEnum"] | null;
            /**
             * Service Name
             * @default
             */
            service_name: string | null;
            cloud: components["schemas"]["TaskCouldEnum"];
            run_mode: components["schemas"]["RunModeEnum"];
            /** Schedule */
            schedule?: string | null;
            /** Context */
            context: string;
        };
        /** TaskTemplateList */
        TaskTemplateList: {
            /** Data */
            data: components["schemas"]["app__schemas__task_template__TaskTemplateResponse"][];
            /** Total */
            total: number;
        };
        /** TaskTemplateSelection */
        TaskTemplateSelection: {
            /** Selected Template Title */
            selected_template_title?: string | null;
            /** Action */
            action: string;
        };
        /** TaskTemplateUpdate */
        TaskTemplateUpdate: {
            /** Task */
            task?: string | null;
            category?: components["schemas"]["TaskCategoryEnum"] | null;
            service?: components["schemas"]["TaskServiceEnum"] | null;
            run_mode?: components["schemas"]["RunModeEnum"] | null;
            /** Schedule */
            schedule?: string | null;
            /** Context */
            context?: string | null;
        };
        /**
         * TaskUpdate
         * @description Schema for updating an existing task.
         */
        TaskUpdate: {
            /** Title */
            title?: string | null;
            /**
             * Description
             * @default
             */
            description: string | null;
            /** Schedule */
            schedule?: string | null;
        };
        /** Token */
        Token: {
            /** Access Token */
            access_token: string;
            /**
             * Token Type
             * @default bearer
             */
            token_type: string;
            /** Workspace Id */
            workspace_id?: string | null;
            /**
             * Is First Login
             * @default false
             */
            is_first_login: boolean;
            /**
             * Slack Oauth
             * @default false
             */
            slack_oauth: boolean;
            /** App Id */
            app_id?: string | null;
            /** Team Id */
            team_id?: string | null;
        };
        /** TokenDistributionCategory */
        TokenDistributionCategory: {
            /** Category */
            category: string;
            /** Percentage */
            percentage: number;
        };
        /** TokenUsageCreate */
        TokenUsageCreate: {
            /**
             * Message ID
             * Format: uuid
             * @description Unique identifier of the associated message
             */
            message_id: string;
            /**
             * Input Tokens
             * @description Number of tokens in the input text
             * @example 100
             */
            input_tokens: number;
            /**
             * Output Tokens
             * @description Number of tokens in the output text
             * @example 150
             */
            output_tokens: number;
            /**
             * Model ID
             * @description Identifier of the AI model used
             * @example gpt-4
             */
            model_id: string;
        };
        /**
         * TokenUsageResponse
         * @description Schema for token usage response.
         *
         *     Attributes:
         *         id: Unique identifier for the usage record
         *         message_id: ID of the associated message
         *         input_tokens: Number of tokens in input text
         *         output_tokens: Number of tokens in output text
         *         model_id: ID of the AI model used
         *         total_tokens: Total number of tokens used
         *         created_at: Timestamp of record creation
         */
        TokenUsageResponse: {
            /**
             * ID
             * Format: uuid
             * @description Unique identifier for the usage record
             */
            id: string;
            /**
             * Message ID
             * Format: uuid
             * @description ID of the associated message
             */
            message_id: string;
            /**
             * Input Tokens
             * @description Number of tokens in the input text
             */
            input_tokens: number;
            /**
             * Output Tokens
             * @description Number of tokens in the output text
             */
            output_tokens: number;
            /**
             * Model ID
             * @description Identifier of the AI model used
             */
            model_id: string;
            /**
             * Workspace ID
             * Format: uuid
             * @description ID of the workspace
             */
            workspace_id: string;
            /**
             * Created At
             * Format: date-time
             * @description Timestamp of record creation
             */
            created_at: string;
            /**
             * Updated At
             * @description Timestamp of last update
             */
            updated_at?: string | null;
            /**
             * Total Tokens
             * @description Calculate total tokens from input and output tokens.
             */
            readonly total_tokens: number;
        };
        /** URLsUploadRequest */
        URLsUploadRequest: {
            /**
             * Urls
             * @description URLs to crawl (required if source_type is website)
             */
            urls?: string[] | null;
            /**
             * Deep Crawls
             * @description Whether to enable deep crawling for each URL
             */
            deep_crawls?: boolean[] | null;
        };
        /** UpdatePassword */
        UpdatePassword: {
            /** Current Password */
            current_password: string;
            /** New Password */
            new_password: string;
        };
        /** UploadedAttachmentInfo */
        UploadedAttachmentInfo: {
            /**
             * File Id
             * @description Client-side unique ID for the file.
             */
            file_id: string;
            /**
             * Filename
             * @description Sanitized name of the file.
             */
            filename: string;
            /**
             * Storage Key
             * @description The key for the object in storage.
             */
            storage_key: string;
            /**
             * Content Type
             * @description MIME type of the file.
             */
            content_type: string;
            /**
             * File Size
             * @description Size of the file in bytes.
             */
            file_size: number;
        };
        /**
         * UploadedFileInfo
         * @description Information about a successfully uploaded file
         */
        UploadedFileInfo: {
            /**
             * File Id
             * @description Client-side ID for tracking this file
             */
            file_id: string;
            /**
             * Filename
             * @description Original filename
             */
            filename: string;
            /**
             * Storage Key
             * @description Storage key for the uploaded file
             */
            storage_key: string;
            /**
             * Content Type
             * @description File MIME type
             */
            content_type?: string | null;
            /**
             * File Size
             * @description File size in bytes
             */
            file_size?: number | null;
        };
        /**
         * UsageQuotaResponse
         * @description Response schema for usage quota information.
         */
        UsageQuotaResponse: {
            /**
             * ID
             * Format: uuid
             */
            id: string;
            /**
             * User ID
             * Format: uuid
             */
            user_id: string;
            /** Quota Used Messages */
            quota_used_messages: number;
            /** Quota Used Tokens */
            quota_used_tokens: number;
            /**
             * Reset At
             * Format: date-time
             */
            reset_at: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /** Updated At */
            updated_at?: string | null;
        };
        /**
         * UsageStatistics
         * @description Response schema for usage statistics.
         */
        UsageStatistics: {
            /** Input Tokens */
            input_tokens: number;
            /** Output Tokens */
            output_tokens: number;
            /** Total Tokens */
            total_tokens: number;
            /** Quota Limit */
            quota_limit: number;
            /** Quota Used */
            quota_used: number;
            /** Quota Remaining */
            quota_remaining: number;
            /** Usage Percentage */
            usage_percentage: number;
            /** Daily Token Usage */
            daily_token_usage: components["schemas"]["DailyTokenUsage"][];
            /** Agent Type Stats */
            agent_type_stats: components["schemas"]["AgentTypeUsage"][];
        };
        /** UserCreate */
        UserCreate: {
            /**
             * Email
             * Format: email
             */
            email: string;
            /**
             * Is Active
             * @default false
             */
            is_active: boolean;
            /**
             * Is Email Verified
             * @default false
             */
            is_email_verified: boolean;
            /**
             * Is Superuser
             * @default false
             */
            is_superuser: boolean;
            /** Last Login Time */
            last_login_time?: string | null;
            /** Full Name */
            full_name?: string | null;
            /** Avatar Url */
            avatar_url?: string | null;
            /** Password */
            password: string;
        };
        /** UserDetail */
        UserDetail: {
            /**
             * Email
             * Format: email
             */
            email: string;
            /**
             * Is Active
             * @default false
             */
            is_active: boolean;
            /**
             * Is Email Verified
             * @default false
             */
            is_email_verified: boolean;
            /**
             * Is Superuser
             * @default false
             */
            is_superuser: boolean;
            /** Last Login Time */
            last_login_time?: string | null;
            /** Full Name */
            full_name?: string | null;
            /** Avatar Url */
            avatar_url?: string | null;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Workspaces */
            workspaces?: components["schemas"]["Workspace"][] | null;
            /** Own Workspaces */
            own_workspaces?: components["schemas"]["Workspace"][] | null;
        };
        /** UserPublic */
        UserPublic: {
            /**
             * Email
             * Format: email
             */
            email: string;
            /**
             * Is Active
             * @default false
             */
            is_active: boolean;
            /**
             * Is Email Verified
             * @default false
             */
            is_email_verified: boolean;
            /**
             * Is Superuser
             * @default false
             */
            is_superuser: boolean;
            /** Last Login Time */
            last_login_time?: string | null;
            /** Full Name */
            full_name?: string | null;
            /** Avatar Url */
            avatar_url?: string | null;
            /**
             * Id
             * Format: uuid
             */
            id: string;
        };
        /** UserRegister */
        UserRegister: {
            /**
             * Email
             * Format: email
             */
            email: string;
            /** Password */
            password: string;
            /** Full Name */
            full_name?: string | null;
        };
        /** UserUpdate */
        UserUpdate: {
            /** Email */
            email?: string | null;
            /**
             * Is Active
             * @default false
             */
            is_active: boolean;
            /**
             * Is Email Verified
             * @default false
             */
            is_email_verified: boolean;
            /**
             * Is Superuser
             * @default false
             */
            is_superuser: boolean;
            /** Last Login Time */
            last_login_time?: string | null;
            /** Full Name */
            full_name?: string | null;
            /** Avatar Url */
            avatar_url?: string | null;
            /** Password */
            password?: string | null;
        };
        /** UserUpdateMe */
        UserUpdateMe: {
            /** Full Name */
            full_name?: string | null;
            /** Email */
            email?: string | null;
            /** Avatar Url */
            avatar_url?: string | null;
        };
        /** UsersPublic */
        UsersPublic: {
            /** Data */
            data: components["schemas"]["UserPublic"][];
            /** Count */
            count: number;
        };
        /** ValidationError */
        ValidationError: {
            /** Location */
            loc: (string | number)[];
            /** Message */
            msg: string;
            /** Error Type */
            type: string;
        };
        /** Workspace */
        Workspace: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            /** Organization Name */
            organization_name?: string | null;
            provider?: components["schemas"]["CloudProvider"] | null;
            /**
             * Id
             * Format: uuid
             */
            id?: string;
            /**
             * Owner Id
             * Format: uuid
             */
            owner_id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at?: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at?: string;
            /**
             * Is Default
             * @default false
             */
            is_default: boolean;
            /**
             * Is Deleted
             * @default false
             */
            is_deleted: boolean;
        };
        /** WorkspaceBuiltInToolResponse */
        WorkspaceBuiltInToolResponse: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Required Permission */
            required_permission: boolean;
            builtin_tool: components["schemas"]["BuiltInToolPublic"];
        };
        /** WorkspaceCreate */
        WorkspaceCreate: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            /** Organization Name */
            organization_name?: string | null;
            provider?: components["schemas"]["CloudProvider"] | null;
        };
        /** WorkspaceDetail */
        WorkspaceDetail: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            /** Organization Name */
            organization_name?: string | null;
            provider?: components["schemas"]["CloudProvider"] | null;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Is Default */
            is_default: boolean;
            /**
             * Is Deleted
             * @default false
             */
            is_deleted: boolean;
            /**
             * Created At
             * Format: date-time
             */
            created_at?: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at?: string;
            aws_account?: components["schemas"]["AWSAccountDetail"] | null;
            gcp_account?: components["schemas"]["GCPAccountDetail"] | null;
            settings: components["schemas"]["WorkspaceSetting"] | null;
        };
        /** WorkspacePublic */
        WorkspacePublic: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            /** Organization Name */
            organization_name?: string | null;
            provider?: components["schemas"]["CloudProvider"] | null;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Is Default */
            is_default: boolean;
            /**
             * Is Deleted
             * @default false
             */
            is_deleted: boolean;
            /**
             * Created At
             * Format: date-time
             */
            created_at?: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at?: string;
        };
        /**
         * WorkspaceSetting
         * @description Settings for a workspace
         */
        WorkspaceSetting: {
            /**
             * Id
             * Format: uuid
             */
            id?: string;
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /** @default AWS */
            provider: components["schemas"]["CloudProvider"];
            /**
             * Regions
             * @default []
             */
            regions: string[];
            /**
             * Types
             * @default []
             */
            types: string[];
            /** Cron Pattern */
            cron_pattern: string;
        };
        /** WorkspaceUpdate */
        WorkspaceUpdate: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            /** Organization Name */
            organization_name?: string | null;
            provider?: components["schemas"]["CloudProvider"] | null;
        };
        /** WorkspacesPublic */
        WorkspacesPublic: {
            /** Data */
            data: components["schemas"]["WorkspacePublic"][];
            /** Count */
            count: number;
        };
        /**
         * PresignedUrlInfo
         * @description Information about a generated presigned URL
         */
        app__schemas__kb__PresignedUrlInfo: {
            /**
             * File Id
             * @description Client-side ID for tracking this file
             */
            file_id: string;
            /**
             * Filename
             * @description Original filename
             */
            filename: string;
            /**
             * Storage Key
             * @description Storage key for the file
             */
            storage_key: string;
            /**
             * Presigned Url
             * @description Presigned URL for file upload
             */
            presigned_url: string;
        };
        /**
         * TaskStatusResponse
         * @description Response schema for task status operations
         */
        app__schemas__kb__TaskStatusResponse: {
            /**
             * Task Id
             * @description Celery task ID
             */
            task_id: string;
            /** @description Task status (PENDING, PROGRESS, SUCCESS, FAILURE) */
            status: components["schemas"]["AsyncTaskStatus"];
            /**
             * Progress
             * @description Progress percentage (0-100)
             * @default 0
             */
            progress: number;
            /**
             * Result
             * @description Task result if completed
             */
            result?: Record<string, never> | null;
            /**
             * Error
             * @description Error message if failed
             */
            error?: string | null;
            /**
             * Status Message
             * @description Human-readable status message
             */
            status_message?: string | null;
        };
        /** PresignedUrlInfo */
        app__schemas__message_attachment__PresignedUrlInfo: {
            /**
             * File Id
             * @description Client-side unique ID for the file.
             */
            file_id: string;
            /**
             * Filename
             * @description Sanitized name of the file.
             */
            filename: string;
            /**
             * Storage Key
             * @description The key for the object in storage.
             */
            storage_key: string;
            /**
             * Presigned Url
             * @description The presigned URL for uploading.
             */
            presigned_url: string;
        };
        /** TaskStatusResponse */
        app__schemas__message_attachment__TaskStatusResponse: {
            /** Task Id */
            task_id: string;
            status: components["schemas"]["AsyncTaskStatus"];
            /** Status Message */
            status_message?: string | null;
            /**
             * Progress
             * @default 0
             */
            progress: number;
            /** Result */
            result?: Record<string, never> | null;
            /** Error */
            error?: string | null;
        };
        /** TaskTemplateResponse */
        app__schemas__onboarding__TaskTemplateResponse: {
            /** Cloud Provider */
            cloud_provider: string;
            /** Templates */
            templates: components["schemas"]["TaskTemplate"][];
        };
        /** TaskTemplateResponse */
        app__schemas__task_template__TaskTemplateResponse: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Task */
            task: string;
            category: components["schemas"]["TaskCategoryEnum"];
            service: components["schemas"]["TaskServiceEnum"];
            /** Service Name */
            service_name: string;
            cloud: components["schemas"]["TaskCouldEnum"];
            run_mode: components["schemas"]["RunModeEnum"];
            /** Schedule */
            schedule: string | null;
            /** Context */
            context: string;
            /** Is Default */
            is_default: boolean;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /** Updated At */
            updated_at: string | null;
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type SchemaAwsAccountCreate = components['schemas']['AWSAccountCreate'];
export type SchemaAwsAccountDetail = components['schemas']['AWSAccountDetail'];
export type SchemaAwsAccountPublic = components['schemas']['AWSAccountPublic'];
export type SchemaAwsAccountUpdate = components['schemas']['AWSAccountUpdate'];
export type SchemaAwsOnboardingCreate = components['schemas']['AWSOnboardingCreate'];
export type SchemaAccountEnvironement = components['schemas']['AccountEnvironement'];
export type SchemaActivationResponse = components['schemas']['ActivationResponse'];
export type SchemaActivationResult = components['schemas']['ActivationResult'];
export type SchemaAddress = components['schemas']['Address'];
export type SchemaAgentBuiltInToolBulkUpdateResult = components['schemas']['AgentBuiltInToolBulkUpdateResult'];
export type SchemaAgentBuiltInToolPublic = components['schemas']['AgentBuiltInToolPublic'];
export type SchemaAgentBuiltInToolUpdate = components['schemas']['AgentBuiltInToolUpdate'];
export type SchemaAgentBuiltInToolsBulkUpdateResponse = components['schemas']['AgentBuiltInToolsBulkUpdateResponse'];
export type SchemaAgentBuiltInToolsPublic = components['schemas']['AgentBuiltInToolsPublic'];
export type SchemaAgentBuiltInToolsUpdateRequest = components['schemas']['AgentBuiltInToolsUpdateRequest'];
export type SchemaAgentConnectionCreateRequest = components['schemas']['AgentConnectionCreateRequest'];
export type SchemaAgentConnectionResponse = components['schemas']['AgentConnectionResponse'];
export type SchemaAgentConnectionsPublic = components['schemas']['AgentConnectionsPublic'];
export type SchemaAgentExecutionConfig = components['schemas']['AgentExecutionConfig'];
export type SchemaAgentInstructionsUpdate = components['schemas']['AgentInstructionsUpdate'];
export type SchemaAgentPublic = components['schemas']['AgentPublic'];
export type SchemaAgentStatusUpdate = components['schemas']['AgentStatusUpdate'];
export type SchemaAgentType = components['schemas']['AgentType'];
export type SchemaAgentTypeUsage = components['schemas']['AgentTypeUsage'];
export type SchemaAgentsBuiltInToolsResponse = components['schemas']['AgentsBuiltInToolsResponse'];
export type SchemaAgentsConnectionsResponse = components['schemas']['AgentsConnectionsResponse'];
export type SchemaAgentsPublic = components['schemas']['AgentsPublic'];
export type SchemaAlertCreate = components['schemas']['AlertCreate'];
export type SchemaAlertList = components['schemas']['AlertList'];
export type SchemaAlertResponse = components['schemas']['AlertResponse'];
export type SchemaAlertSeverity = components['schemas']['AlertSeverity'];
export type SchemaAlertStatus = components['schemas']['AlertStatus'];
export type SchemaAlertStatusSummary = components['schemas']['AlertStatusSummary'];
export type SchemaAlertUpdate = components['schemas']['AlertUpdate'];
export type SchemaAllConstantsResponse = components['schemas']['AllConstantsResponse'];
export type SchemaAsyncTaskStatus = components['schemas']['AsyncTaskStatus'];
export type SchemaAttachmentConfirmRequest = components['schemas']['AttachmentConfirmRequest'];
export type SchemaAttachmentDownloadResponse = components['schemas']['AttachmentDownloadResponse'];
export type SchemaAttachmentFileInfo = components['schemas']['AttachmentFileInfo'];
export type SchemaAttachmentMetadataResponse = components['schemas']['AttachmentMetadataResponse'];
export type SchemaAttachmentPresignedUrlRequest = components['schemas']['AttachmentPresignedUrlRequest'];
export type SchemaAttachmentPresignedUrlResponse = components['schemas']['AttachmentPresignedUrlResponse'];
export type SchemaAvailableUser = components['schemas']['AvailableUser'];
export type SchemaAvailableUsersCurrentWorkspace = components['schemas']['AvailableUsersCurrentWorkspace'];
export type SchemaAzureOnboardingCreate = components['schemas']['AzureOnboardingCreate'];
export type SchemaBillingDetails = components['schemas']['BillingDetails'];
export type SchemaBodyLoginLoginAccessToken = components['schemas']['Body_login-login_access_token'];
export type SchemaBodyNotificationsListNotifications = components['schemas']['Body_notifications-list_notifications'];
export type SchemaBuiltInToolPublic = components['schemas']['BuiltInToolPublic'];
export type SchemaBuiltInToolUpdateRequest = components['schemas']['BuiltInToolUpdateRequest'];
export type SchemaBuiltinInstallRequest = components['schemas']['BuiltinInstallRequest'];
export type SchemaCardDetails = components['schemas']['CardDetails'];
export type SchemaChartType = components['schemas']['ChartType'];
export type SchemaCheckoutSessionResponse = components['schemas']['CheckoutSessionResponse'];
export type SchemaCloudProvider = components['schemas']['CloudProvider'];
export type SchemaCloudSyncConfigCreate = components['schemas']['CloudSyncConfigCreate'];
export type SchemaCloudSyncConfigPublic = components['schemas']['CloudSyncConfigPublic'];
export type SchemaCloudSyncConfigUpdate = components['schemas']['CloudSyncConfigUpdate'];
export type SchemaConfirmUploadsRequest = components['schemas']['ConfirmUploadsRequest'];
export type SchemaConnectionCreate = components['schemas']['ConnectionCreate'];
export type SchemaConnectionPublic = components['schemas']['ConnectionPublic'];
export type SchemaConnectionStatus = components['schemas']['ConnectionStatus'];
export type SchemaConnectionTransport = components['schemas']['ConnectionTransport'];
export type SchemaConnectionType = components['schemas']['ConnectionType'];
export type SchemaConnectionUpdate = components['schemas']['ConnectionUpdate'];
export type SchemaConnectionsPublic = components['schemas']['ConnectionsPublic'];
export type SchemaConstantCategoriesResponse = components['schemas']['ConstantCategoriesResponse'];
export type SchemaConstantCategory = components['schemas']['ConstantCategory'];
export type SchemaConstantOption = components['schemas']['ConstantOption'];
export type SchemaConversationPublic = components['schemas']['ConversationPublic'];
export type SchemaConversationRenameRequest = components['schemas']['ConversationRenameRequest'];
export type SchemaConversationsPublic = components['schemas']['ConversationsPublic'];
export type SchemaDailyMessageVolume = components['schemas']['DailyMessageVolume'];
export type SchemaDailyTokenUsage = components['schemas']['DailyTokenUsage'];
export type SchemaDashboard = components['schemas']['Dashboard'];
export type SchemaDocumentKbRead = components['schemas']['DocumentKBRead'];
export type SchemaDocumentType = components['schemas']['DocumentType'];
export type SchemaDocumentsKbRead = components['schemas']['DocumentsKBRead'];
export type SchemaEnterpriseEnquiryMessageResponse = components['schemas']['EnterpriseEnquiryMessageResponse'];
export type SchemaEnterpriseEnquiryRequest = components['schemas']['EnterpriseEnquiryRequest'];
export type SchemaFeedbackType = components['schemas']['FeedbackType'];
export type SchemaFileContentResponse = components['schemas']['FileContentResponse'];
export type SchemaFileInfo = components['schemas']['FileInfo'];
export type SchemaFileListResponse = components['schemas']['FileListResponse'];
export type SchemaFileNode = components['schemas']['FileNode'];
export type SchemaFileType = components['schemas']['FileType'];
export type SchemaGcpAccountCreate = components['schemas']['GCPAccountCreate'];
export type SchemaGcpAccountDetail = components['schemas']['GCPAccountDetail'];
export type SchemaGcpAccountPublic = components['schemas']['GCPAccountPublic'];
export type SchemaGcpAccountUpdate = components['schemas']['GCPAccountUpdate'];
export type SchemaGcpOnboardingCreate = components['schemas']['GCPOnboardingCreate'];
export type SchemaHttpValidationError = components['schemas']['HTTPValidationError'];
export type SchemaInvoiceResponse = components['schemas']['InvoiceResponse'];
export type SchemaKbAccessLevel = components['schemas']['KBAccessLevel'];
export type SchemaKbCreate = components['schemas']['KBCreate'];
export type SchemaKbRead = components['schemas']['KBRead'];
export type SchemaKbUpdate = components['schemas']['KBUpdate'];
export type SchemaKbUsageMode = components['schemas']['KBUsageMode'];
export type SchemaKBsRead = components['schemas']['KBsRead'];
export type SchemaMessage = components['schemas']['Message'];
export type SchemaMessageAgentThoughtPublic = components['schemas']['MessageAgentThoughtPublic'];
export type SchemaMessageAttachmentPublic = components['schemas']['MessageAttachmentPublic'];
export type SchemaMessageDisplayComponentPublic = components['schemas']['MessageDisplayComponentPublic'];
export type SchemaMessageDisplayComponentType = components['schemas']['MessageDisplayComponentType'];
export type SchemaMessageFeedbackCreate = components['schemas']['MessageFeedbackCreate'];
export type SchemaMessageFeedbackPublic = components['schemas']['MessageFeedbackPublic'];
export type SchemaMessageFeedbackUpdate = components['schemas']['MessageFeedbackUpdate'];
export type SchemaMessagePublic = components['schemas']['MessagePublic'];
export type SchemaMessagePublicList = components['schemas']['MessagePublicList'];
export type SchemaMessageResponse = components['schemas']['MessageResponse'];
export type SchemaMessageStatistics = components['schemas']['MessageStatistics'];
export type SchemaMessageStreamInput = components['schemas']['MessageStreamInput'];
export type SchemaModuleSetting = components['schemas']['ModuleSetting'];
export type SchemaNewPassword = components['schemas']['NewPassword'];
export type SchemaNotificationList = components['schemas']['NotificationList'];
export type SchemaNotificationResponse = components['schemas']['NotificationResponse'];
export type SchemaNotificationStatus = components['schemas']['NotificationStatus'];
export type SchemaNotificationType = components['schemas']['NotificationType'];
export type SchemaOnboardingStatus = components['schemas']['OnboardingStatus'];
export type SchemaPaginationMeta = components['schemas']['PaginationMeta'];
export type SchemaPaymentMethodResponse = components['schemas']['PaymentMethodResponse'];
export type SchemaPlanChangeRequestCreate = components['schemas']['PlanChangeRequestCreate'];
export type SchemaPlanChangeRequestResponse = components['schemas']['PlanChangeRequestResponse'];
export type SchemaPresignedUrlRequest = components['schemas']['PresignedUrlRequest'];
export type SchemaPresignedUrlResponse = components['schemas']['PresignedUrlResponse'];
export type SchemaPriceResponse = components['schemas']['PriceResponse'];
export type SchemaProductResponse = components['schemas']['ProductResponse'];
export type SchemaQuotaDefinitionResponse = components['schemas']['QuotaDefinitionResponse'];
export type SchemaQuotaInfo = components['schemas']['QuotaInfo'];
export type SchemaRecommendationCreate = components['schemas']['RecommendationCreate'];
export type SchemaRecommendationOveralPublic = components['schemas']['RecommendationOveralPublic'];
export type SchemaRecommendationPublic = components['schemas']['RecommendationPublic'];
export type SchemaRecommendationStatus = components['schemas']['RecommendationStatus'];
export type SchemaRecommendationUpdate = components['schemas']['RecommendationUpdate'];
export type SchemaRecommendationsPublic = components['schemas']['RecommendationsPublic'];
export type SchemaReport = components['schemas']['Report'];
export type SchemaResendActivationRequest = components['schemas']['ResendActivationRequest'];
export type SchemaResourceCategory = components['schemas']['ResourceCategory'];
export type SchemaResourcePublic = components['schemas']['ResourcePublic'];
export type SchemaResourcePublicSimple = components['schemas']['ResourcePublicSimple'];
export type SchemaResourceStatus = components['schemas']['ResourceStatus'];
export type SchemaResourceTypeInfo = components['schemas']['ResourceTypeInfo'];
export type SchemaResourceTypesResponse = components['schemas']['ResourceTypesResponse'];
export type SchemaResourcesPublic = components['schemas']['ResourcesPublic'];
export type SchemaRunModeEnum = components['schemas']['RunModeEnum'];
export type SchemaShareResponse = components['schemas']['ShareResponse'];
export type SchemaSimpleMessage = components['schemas']['SimpleMessage'];
export type SchemaStreamResponse = components['schemas']['StreamResponse'];
export type SchemaSubscriptionStatus = components['schemas']['SubscriptionStatus'];
export type SchemaTaskCategoryEnum = components['schemas']['TaskCategoryEnum'];
export type SchemaTaskComplexity = components['schemas']['TaskComplexity'];
export type SchemaTaskCouldEnum = components['schemas']['TaskCouldEnum'];
export type SchemaTaskCreate = components['schemas']['TaskCreate'];
export type SchemaTaskDeleteResponse = components['schemas']['TaskDeleteResponse'];
export type SchemaTaskEnableRequest = components['schemas']['TaskEnableRequest'];
export type SchemaTaskExecutionStatus = components['schemas']['TaskExecutionStatus'];
export type SchemaTaskHistoriesResponse = components['schemas']['TaskHistoriesResponse'];
export type SchemaTaskHistoryResponse = components['schemas']['TaskHistoryResponse'];
export type SchemaTaskList = components['schemas']['TaskList'];
export type SchemaTaskPriority = components['schemas']['TaskPriority'];
export type SchemaTaskResponse = components['schemas']['TaskResponse'];
export type SchemaTaskScheduledStatus = components['schemas']['TaskScheduledStatus'];
export type SchemaTaskServiceEnum = components['schemas']['TaskServiceEnum'];
export type SchemaTaskStopRequest = components['schemas']['TaskStopRequest'];
export type SchemaTaskStopResponse = components['schemas']['TaskStopResponse'];
export type SchemaTaskTemplate = components['schemas']['TaskTemplate'];
export type SchemaTaskTemplateCreate = components['schemas']['TaskTemplateCreate'];
export type SchemaTaskTemplateList = components['schemas']['TaskTemplateList'];
export type SchemaTaskTemplateSelection = components['schemas']['TaskTemplateSelection'];
export type SchemaTaskTemplateUpdate = components['schemas']['TaskTemplateUpdate'];
export type SchemaTaskUpdate = components['schemas']['TaskUpdate'];
export type SchemaToken = components['schemas']['Token'];
export type SchemaTokenDistributionCategory = components['schemas']['TokenDistributionCategory'];
export type SchemaTokenUsageCreate = components['schemas']['TokenUsageCreate'];
export type SchemaTokenUsageResponse = components['schemas']['TokenUsageResponse'];
export type SchemaUrLsUploadRequest = components['schemas']['URLsUploadRequest'];
export type SchemaUpdatePassword = components['schemas']['UpdatePassword'];
export type SchemaUploadedAttachmentInfo = components['schemas']['UploadedAttachmentInfo'];
export type SchemaUploadedFileInfo = components['schemas']['UploadedFileInfo'];
export type SchemaUsageQuotaResponse = components['schemas']['UsageQuotaResponse'];
export type SchemaUsageStatistics = components['schemas']['UsageStatistics'];
export type SchemaUserCreate = components['schemas']['UserCreate'];
export type SchemaUserDetail = components['schemas']['UserDetail'];
export type SchemaUserPublic = components['schemas']['UserPublic'];
export type SchemaUserRegister = components['schemas']['UserRegister'];
export type SchemaUserUpdate = components['schemas']['UserUpdate'];
export type SchemaUserUpdateMe = components['schemas']['UserUpdateMe'];
export type SchemaUsersPublic = components['schemas']['UsersPublic'];
export type SchemaValidationError = components['schemas']['ValidationError'];
export type SchemaWorkspace = components['schemas']['Workspace'];
export type SchemaWorkspaceBuiltInToolResponse = components['schemas']['WorkspaceBuiltInToolResponse'];
export type SchemaWorkspaceCreate = components['schemas']['WorkspaceCreate'];
export type SchemaWorkspaceDetail = components['schemas']['WorkspaceDetail'];
export type SchemaWorkspacePublic = components['schemas']['WorkspacePublic'];
export type SchemaWorkspaceSetting = components['schemas']['WorkspaceSetting'];
export type SchemaWorkspaceUpdate = components['schemas']['WorkspaceUpdate'];
export type SchemaWorkspacesPublic = components['schemas']['WorkspacesPublic'];
export type SchemaAppSchemasKbPresignedUrlInfo = components['schemas']['app__schemas__kb__PresignedUrlInfo'];
export type SchemaAppSchemasKbTaskStatusResponse = components['schemas']['app__schemas__kb__TaskStatusResponse'];
export type SchemaAppSchemasMessageAttachmentPresignedUrlInfo = components['schemas']['app__schemas__message_attachment__PresignedUrlInfo'];
export type SchemaAppSchemasMessageAttachmentTaskStatusResponse = components['schemas']['app__schemas__message_attachment__TaskStatusResponse'];
export type SchemaAppSchemasOnboardingTaskTemplateResponse = components['schemas']['app__schemas__onboarding__TaskTemplateResponse'];
export type SchemaAppSchemasTaskTemplateTaskTemplateResponse = components['schemas']['app__schemas__task_template__TaskTemplateResponse'];
export type $defs = Record<string, never>;
export interface operations {
    "login-login_access_token": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/x-www-form-urlencoded": components["schemas"]["Body_login-login_access_token"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Token"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "login-test_token": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserPublic"];
                };
            };
        };
    };
    "login-recover_password": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                email: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "login-reset_password": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["NewPassword"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "login-recover_password_html_content": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                email: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "text/html": string;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "users-read_users": {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UsersPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "users-create_user": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UserCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "users-read_user_me": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserDetail"];
                };
            };
        };
    };
    "users-delete_user_me": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
        };
    };
    "users-update_user_me": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UserUpdateMe"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "users-update_password_me": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdatePassword"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "users-read_user_by_id": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                user_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "users-delete_user": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                user_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "users-update_user": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                user_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UserUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "users-switch_workspace": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workspace_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Token"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "utils-health_check": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": boolean;
                };
            };
        };
    };
    "utils-get_constant_categories": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ConstantCategoriesResponse"];
                };
            };
        };
    };
    "utils-get_constant_category": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                category: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ConstantCategory"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "utils-get_all_constants": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AllConstantsResponse"];
                };
            };
        };
    };
    "aws-accounts-update_aws_account": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AWSAccountUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AWSAccountPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "aws-accounts-create_aws_account": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AWSAccountCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AWSAccountPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "resources-read_resources": {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
                name?: string | null;
                resource_type?: string[];
                status?: string[];
                region?: string[];
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ResourcesPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "recommendations-get_recomendation_overal": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RecommendationOveralPublic"];
                };
            };
        };
    };
    "recommendations-read_recommendations": {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
                search?: string | null;
                resource_id?: string[];
                resource_type?: string[];
                status?: string[];
                start_date?: string | null;
                end_date?: string | null;
                order_by?: string | null;
                order_direction?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RecommendationsPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "recommendations-create_recommendation": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["RecommendationCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RecommendationPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "recommendations-read_recommendation": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RecommendationPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "recommendations-update_recommendation": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["RecommendationUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RecommendationPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "recommendations-delete_recommendation": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SimpleMessage"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "recommendations-update_recommendation_status": {
        parameters: {
            query: {
                status: components["schemas"]["RecommendationStatus"];
            };
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RecommendationPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workspaces-get_workspaces": {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkspacesPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workspaces-create_workspace": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["WorkspaceCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkspacePublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workspaces-get_workspace_details": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workspace_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkspaceDetail"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workspaces-update_workspace": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workspace_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["WorkspaceUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkspacePublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workspaces-delete_workspace": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workspace_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MessageResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "console-proxy-get_files": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["FileListResponse"];
                };
            };
        };
    };
    "console-proxy-get_file_content": {
        parameters: {
            query: {
                /** @description File path */
                path: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["FileContentResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "agents-read_agents": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentsPublic"];
                };
            };
        };
    };
    "agents-update_agent_instructions": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AgentInstructionsUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "agents-update_agent_status": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AgentStatusUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "tasks-list_tasks": {
        parameters: {
            query?: {
                search?: string | null;
                execution_status?: components["schemas"]["TaskExecutionStatus"] | null;
                skip?: number;
                limit?: number;
                timezone?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskList"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "tasks-create_task": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TaskCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "tasks-get_task": {
        parameters: {
            query?: {
                timezone?: string;
            };
            header?: never;
            path: {
                task_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "tasks-update_task": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                task_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TaskUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "tasks-delete_task": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                task_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskDeleteResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "tasks-get_task_average_run_time": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                task_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": number;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "tasks-get_task_histories": {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
                timezone?: string;
            };
            header?: never;
            path: {
                task_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskHistoriesResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "tasks-update_task_enable": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                task_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TaskEnableRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "tasks-stop_task_execution": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                task_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TaskStopRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskStopResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "autonomous-agents-rename_conversation": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                conversation_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ConversationRenameRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ConversationPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "autonomous-agents-delete_conversation": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                conversation_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        [key: string]: string;
                    };
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "autonomous-agents-get_conversations": {
        parameters: {
            query?: {
                agent_id?: string | null;
                resource_id?: string | null;
                search?: string | null;
                /** @description Number of records to skip for pagination */
                skip?: number;
                /** @description Maximum number of records to return */
                limit?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ConversationsPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "autonomous-agents-get_messages_history": {
        parameters: {
            query?: {
                /** @description Number of records to skip for pagination */
                skip?: number;
                /** @description Maximum number of records to return */
                limit?: number;
            };
            header?: never;
            path: {
                conversation_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MessagePublicList"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "autonomous-agents-chat_stream": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                conversation_id: string | null;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MessageStreamInput"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["StreamResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "autonomous-agents-chat_stream": {
        parameters: {
            query?: {
                conversation_id?: string | null;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MessageStreamInput"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["StreamResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "autonomous-agents-reconnect_to_stream": {
        parameters: {
            query?: {
                /** @description Last event position received */
                last_position?: number;
            };
            header?: never;
            path: {
                conversation_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "google-google_login": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    "google-google_callback": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Token"];
                };
            };
        };
    };
    "quotas-create_usage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TokenUsageCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TokenUsageResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "quotas-get_messages_statistics": {
        parameters: {
            query?: {
                start_date?: string | null;
                end_date?: string | null;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MessageStatistics"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "quotas-get_usage_quota": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                user_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UsageQuotaResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "quotas-create_usage_quota": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                user_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UsageQuotaResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "quotas-reset_user_quota": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                user_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UsageQuotaResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "quotas-get_usage_statistics": {
        parameters: {
            query?: {
                start_date?: string | null;
                end_date?: string | null;
            };
            header?: never;
            path: {
                user_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UsageStatistics"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "quotas-get_quota_info": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                user_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["QuotaInfo"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "reports-get_report_by_conversation": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                conversation_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Report"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "dashboards-get_dashboard_by_conversation": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                conversation_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Dashboard"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "task_templates-generate": {
        parameters: {
            query: {
                input: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["app__schemas__task_template__TaskTemplateResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "task_templates-list_templates": {
        parameters: {
            query?: {
                category?: components["schemas"]["TaskCategoryEnum"][] | null;
                services?: components["schemas"]["TaskServiceEnum"][] | null;
                include_defaults?: boolean;
                skip?: number;
                limit?: number;
                search_query?: string | null;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskTemplateList"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "task_templates-create_template": {
        parameters: {
            query?: {
                is_default?: boolean;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TaskTemplateCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["app__schemas__task_template__TaskTemplateResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "task_templates-get_template": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                template_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["app__schemas__task_template__TaskTemplateResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "task_templates-update_template": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                template_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TaskTemplateUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["app__schemas__task_template__TaskTemplateResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "task_templates-delete_template": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                template_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-get_kbs": {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
                search?: string | null;
                access_level?: string | null;
                usage_mode?: string | null;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KBsRead"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-create_kb": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["KBCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KBRead"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-get_available_users": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AvailableUsersCurrentWorkspace"];
                };
            };
        };
    };
    "knowledge_base-get_point_usage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": Record<string, never>;
                };
            };
        };
    };
    "knowledge_base-get_kb_by_id": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KBRead"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-update_kb": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["KBUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KBRead"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-delete_kb": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": Record<string, never>;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-generate_presigned_urls": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PresignedUrlRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PresignedUrlResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-confirm_file_uploads": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ConfirmUploadsRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["app__schemas__kb__TaskStatusResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-list_documents": {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
                search?: string | null;
            };
            header?: never;
            path: {
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DocumentsKBRead"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-upload_urls": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["URLsUploadRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["app__schemas__kb__TaskStatusResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-get_document_content": {
        parameters: {
            query: {
                object_name: string;
            };
            header?: never;
            path: {
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-delete_document": {
        parameters: {
            query: {
                object_name: string;
            };
            header?: never;
            path: {
                kb_id: string;
                document_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": Record<string, never>;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-get_task_status": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                task_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["app__schemas__kb__TaskStatusResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "subscriptions-get_available_plans": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ProductResponse"][];
                };
            };
        };
    };
    "subscriptions-get_user_subscription_status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SubscriptionStatus"][];
                };
            };
        };
    };
    "subscriptions-get_workspace_subscription_status": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workspace_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SubscriptionStatus"][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "subscriptions-create_checkout_session": {
        parameters: {
            query: {
                price_id: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CheckoutSessionResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "subscriptions-get_user_payment_methods": {
        parameters: {
            query?: {
                payment_type?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PaymentMethodResponse"][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "subscriptions-get_user_invoices": {
        parameters: {
            query?: {
                limit?: number;
                status?: string | null;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InvoiceResponse"][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "subscriptions-submit_enterprise_enquiry": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["EnterpriseEnquiryRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["EnterpriseEnquiryMessageResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "subscriptions-submit_plan_change_request": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PlanChangeRequestCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PlanChangeRequestResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "subscriptions-webhook": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    "subscriptions-cancel_subscription": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": Record<string, never>;
                };
            };
        };
    };
    "module_setting-get_module_settings": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ModuleSetting"][];
                };
            };
        };
    };
    "alerts-get_alert_status_summary": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AlertStatusSummary"];
                };
            };
        };
    };
    "alerts-list_alerts": {
        parameters: {
            query?: {
                severity?: components["schemas"]["AlertSeverity"] | null;
                status?: components["schemas"]["AlertStatus"] | null;
                /** @description Field to sort by */
                sort_by?: string;
                /** @description Sort in descending order */
                sort_desc?: boolean;
                skip?: number;
                limit?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AlertList"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "alerts-create_alert": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AlertCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AlertResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "alerts-get_alert": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                alert_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AlertResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "alerts-update_alert": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                alert_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AlertUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AlertResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "alerts-delete_alert": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                alert_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "alerts-update_alert_status": {
        parameters: {
            query: {
                status: components["schemas"]["AlertStatus"];
            };
            header?: never;
            path: {
                alert_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AlertResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "alerts-mark_all_alerts_acknowledged": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    "auth-register": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UserRegister"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ActivationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "auth-activate_account": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                token: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ActivationResult"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "auth-resend_activation": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ResendActivationRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ActivationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "notifications-list_notifications": {
        parameters: {
            query?: {
                requires_action?: boolean | null;
                timeframe?: string | null;
                skip?: number;
                limit?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["Body_notifications-list_notifications"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["NotificationList"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "notifications-mark_notification_read": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                notification_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["NotificationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "notifications-mark_all_notifications_read": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["NotificationType"][] | null;
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "message-feedback-get_message_feedback": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                message_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MessageFeedbackPublic"] | null;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "message-feedback-update_message_feedback": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                message_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MessageFeedbackUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MessageFeedbackPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "message-feedback-delete_message_feedback": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                message_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "message-feedback-create_message_feedback": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MessageFeedbackCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MessageFeedbackPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "connection-get_connections": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ConnectionsPublic"];
                };
            };
        };
    };
    "connection-create_connection": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ConnectionCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ConnectionPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "connection-get_builtin_connections": {
        parameters: {
            query?: {
                connection_type?: string[];
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ConnectionsPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "connection-install_builtin_connection": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                builtin_id: string;
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["BuiltinInstallRequest"] | null;
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ConnectionPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "connection-get_connection": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                conn_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ConnectionPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "connection-update_connection": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                conn_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ConnectionUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ConnectionPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "connection-delete_connection": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                conn_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "cloud-sync-config-get_resource_types": {
        parameters: {
            query: {
                /** @description Cloud provider (AWS, GCP, AZURE) */
                cloud_provider: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ResourceTypesResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "cloud-sync-config-get_workspace_configs": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CloudSyncConfigPublic"][];
                };
            };
        };
    };
    "cloud-sync-config-create_or_update_config": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CloudSyncConfigCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CloudSyncConfigPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "cloud-sync-config-update_config": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                config_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CloudSyncConfigUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CloudSyncConfigPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "cloud-sync-config-delete_config": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                config_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "cloud-sync-config-trigger_manual_sync": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                config_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "attachments-generate_attachment_presigned_urls": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AttachmentPresignedUrlRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AttachmentPresignedUrlResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "attachments-confirm_attachment_uploads": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AttachmentConfirmRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["app__schemas__message_attachment__TaskStatusResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "attachments-get_attachment_task_status": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                task_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["app__schemas__message_attachment__TaskStatusResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "attachments-get_attachment_download_url": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                attachment_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AttachmentDownloadResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "attachments-get_attachment_metadata": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                attachment_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AttachmentMetadataResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "builtin-tools-get_workspace_builtin_tools": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkspaceBuiltInToolResponse"][];
                };
            };
        };
    };
    "builtin-tools-update_workspace_builtin_tool": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workspace_builtin_tool_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["BuiltInToolUpdateRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": boolean;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "share-chat-get_share_link": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                conversation_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ShareResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "share-chat-create_share_link": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                conversation_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ShareResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "share-chat-revoke_share_link": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                conversation_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "share-chat-get_shared_conversation": {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
            };
            header?: never;
            path: {
                share_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MessagePublicList"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "gcp-accounts-update_gcp_account": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["GCPAccountUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GCPAccountPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "gcp-accounts-create_gcp_account": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["GCPAccountCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GCPAccountPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "agents-builtin-tools-get_workspace_agent_builtin_tools": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentsBuiltInToolsResponse"];
                };
            };
        };
    };
    "agents-builtin-tools-bulk_update_agent_builtin_tools": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AgentBuiltInToolsUpdateRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentBuiltInToolsBulkUpdateResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "agents-connections-get_workspace_agent_connections": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentsConnectionsResponse"];
                };
            };
        };
    };
    "agents-connections-create_agent_connection": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AgentConnectionCreateRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentConnectionResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "agents-connections-delete_agent_connection": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
                connection_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentConnectionResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "onboarding-get_onboarding_status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OnboardingStatus"];
                };
            };
        };
    };
    "onboarding-create_workspace": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["WorkspaceCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkspacePublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "onboarding-connect_aws": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AWSOnboardingCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "onboarding-connect_gcp": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["GCPOnboardingCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "onboarding-connect_azure": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AzureOnboardingCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "onboarding-get_task_template": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["app__schemas__onboarding__TaskTemplateResponse"];
                };
            };
        };
    };
    "onboarding-complete_task_template": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TaskTemplateSelection"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "sample-data-create_sample_resources": {
        parameters: {
            query?: {
                /** @description Number of sample resources to create */
                count?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "sample-data-clear_sample_resources": {
        parameters: {
            query?: {
                /** @description Confirm deletion of all resources */
                confirm?: boolean;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "sample-data-preview_sample_resources": {
        parameters: {
            query?: {
                /** @description Number of sample resources to preview */
                count?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
}
export enum AccountEnvironement {
    production = "production",
    staging = "staging",
    development = "development"
}
export enum AgentType {
    conversation_agent = "conversation_agent",
    autonomous_agent = "autonomous_agent"
}
export enum AlertSeverity {
    CRITICAL = "CRITICAL",
    HIGH = "HIGH",
    MEDIUM = "MEDIUM",
    LOW = "LOW",
    INFO = "INFO"
}
export enum AlertStatus {
    OPEN = "OPEN",
    ACKNOWLEDGED = "ACKNOWLEDGED",
    RESOLVED = "RESOLVED",
    CLOSED = "CLOSED"
}
export enum AsyncTaskStatus {
    PENDING = "PENDING",
    PROGRESS = "PROGRESS",
    SUCCESS = "SUCCESS",
    FAILURE = "FAILURE"
}
export enum ChartType {
    line = "line",
    bar = "bar",
    pie = "pie",
    doughnut = "doughnut",
    area = "area",
    scatter = "scatter",
    radar = "radar",
    step_area = "step_area",
    sankey = "sankey"
}
export enum CloudProvider {
    AWS = "AWS",
    GCP = "GCP",
    AZURE = "AZURE"
}
export enum ConnectionStatus {
    connected = "connected",
    error = "error"
}
export enum ConnectionTransport {
    streamable_http = "streamable_http",
    sse = "sse"
}
export enum ConnectionType {
    builtin = "builtin",
    mcp = "mcp",
    cloud = "cloud",
    cli = "cli"
}
export enum DocumentType {
    url = "url",
    file = "file"
}
export enum FeedbackType {
    good = "good",
    bad = "bad"
}
export enum FileType {
    file = "file",
    directory = "directory"
}
export enum KBAccessLevel {
    private = "private",
    shared = "shared"
}
export enum KBUsageMode {
    manual = "manual",
    agent_requested = "agent_requested",
    always = "always"
}
export enum MessageDisplayComponentType {
    table = "table",
    chart = "chart"
}
export enum NotificationStatus {
    unread = "unread",
    read = "read",
    archived = "archived"
}
export enum NotificationType {
    info = "info",
    warning = "warning",
    error = "error",
    interrupt = "interrupt"
}
export enum RecommendationStatus {
    pending = "pending",
    implemented = "implemented",
    ignored = "ignored",
    in_progress = "in_progress"
}
export enum ResourceCategory {
    COMPUTE = "COMPUTE",
    DATABASE = "DATABASE",
    STORAGE = "STORAGE",
    NETWORKING = "NETWORKING",
    SERVERLESS = "SERVERLESS",
    CONTAINER = "CONTAINER",
    MESSAGING = "MESSAGING",
    MONITORING = "MONITORING",
    SECURITY = "SECURITY",
    MANAGEMENT = "MANAGEMENT",
    ANALYTICS = "ANALYTICS",
    AI_ML = "AI_ML",
    OTHER = "OTHER"
}
export enum ResourceStatus {
    stopped = "stopped",
    starting = "starting",
    running = "running",
    found = "found",
    deleted = "deleted"
}
export enum RunModeEnum {
    autonomous = "autonomous",
    agent = "agent"
}
export enum TaskCategoryEnum {
    COST_OPTIMIZE = "COST_OPTIMIZE",
    OPERATIONAL = "OPERATIONAL",
    SCALABILITY = "SCALABILITY",
    SECURITY = "SECURITY",
    OPERATIONAL_EFFICIENCY = "OPERATIONAL_EFFICIENCY",
    OTHER = "OTHER"
}
export enum TaskComplexity {
    Easy = "Easy",
    Medium = "Medium"
}
export enum TaskCouldEnum {
    AWS = "AWS",
    AZURE = "AZURE",
    GCP = "GCP",
    ALL = "ALL"
}
export enum TaskExecutionStatus {
    running = "running",
    succeeded = "succeeded",
    failed = "failed",
    cancelled = "cancelled",
    required_approval = "required_approval"
}
export enum TaskPriority {
    low = "low",
    medium = "medium",
    high = "high",
    critical = "critical"
}
export enum TaskScheduledStatus {
    pending = "pending",
    scheduled = "scheduled"
}
export enum TaskServiceEnum {
    ALL = "ALL",
    OTHER = "OTHER",
    COMPUTE = "COMPUTE",
    STORAGE = "STORAGE",
    SERVERLESS = "SERVERLESS",
    DATABASE = "DATABASE",
    NETWORK = "NETWORK",
    MESSAGING = "MESSAGING",
    MANAGEMENT = "MANAGEMENT",
    BILLING = "BILLING",
    CROSS_SERVICE = "CROSS_SERVICE",
    MONITORING = "MONITORING",
    STREAMING = "STREAMING",
    SECURITY = "SECURITY"
}
