import pathsConfig from '@/config/paths.config';
import { urlConfig } from '@/config/url.config';
import { isBrowser } from '@/utils/is-browser';
import makeFetch from 'openapi-fetch';
import { signOut } from 'next-auth/react';
import { toast } from 'sonner';

import { paths } from './gens';
import { getAccessToken } from './get-access-token';

// Create fetch instance with the base URL from config
export const api = makeFetch<paths>({
  baseUrl: urlConfig.apiUrl,

  fetch: async (request) => {
    // Get access token from NextAuth session
    const accessToken = await getAccessToken();

    if (accessToken) {
      request.headers.set('Authorization', `Bearer ${accessToken}`);
    }
    let response: Response;
    try {
      if ('next' in request) {
        response = await fetch(request);
      } else {
        response = await fetch(request, {
          next: {
            revalidate: 60,
          },
        });
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));

        if (response.status === 401) {
          // Handle 401 unauthorized by signing out through NextAuth
          if (isBrowser()) {
            await signOut({ redirect: true, callbackUrl: '/auth/sign-in' });
          } else {
            const { redirect } = await import('next/navigation');

            redirect(pathsConfig.signOut);
          }
        }

        // Handle 429 rate limit errors with specific messaging
        if (response.status === 429 && isBrowser()) {
          const retryAfter = response.headers.get('Retry-After');
          const retryAfterMinutes = retryAfter
            ? Math.ceil(parseInt(retryAfter) / 60)
            : null;

          let message =
            errorData.detail || 'Rate limit exceeded. Please try again later.';
          if (retryAfterMinutes) {
            message = `Rate limit exceeded. Please wait ${retryAfterMinutes} minute${retryAfterMinutes > 1 ? 's' : ''} before trying again.`;
          }

          toast.error(message, {
            duration: 8000, // Show longer for rate limit messages
          });
        } else if (isBrowser() && response.status !== 429) {
          // Show generic error for non-rate-limit errors
          toast.error?.(errorData.detail || 'API request failed');
        }

        throw new ApiError(
          'API request failed',
          response.status,
          errorData,
          response.headers,
        );
      }

      return response;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      if (error instanceof Error) {
        toast.error?.(error.message);
      }
      throw error;
    }
  },
});

export async function fetchData<T>(fn: Promise<{ data?: T }>): Promise<T> {
  const { data } = await fn;
  return data!;
}

/**
 * Interpolates path parameters into a URL path.
 *
 * @param path - The URL path with placeholders for parameters
 * @param params - An object containing parameter values to replace in the path
 * @returns The interpolated URL path with parameter values replaced
 */
export function interpolatePath(
  path: keyof paths,
  params: Record<string, string | number>,
) {
  return path.replace(/{(\w+)}/g, (_, key) => {
    const value = params[key];
    if (!value) throw new Error(`Missing param: ${key}`);
    return encodeURIComponent(value.toString());
  });
}

export class ApiError extends Error {
  status: number;
  data: any;
  headers?: Headers;

  constructor(message: string, status: number, data: any, headers?: Headers) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
    this.headers = headers;
    // This is necessary for instanceof to work correctly
    Object.setPrototypeOf(this, ApiError.prototype);
  }
}
