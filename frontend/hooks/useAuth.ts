'use client';

import { useState } from 'react';

import { useRouter } from 'next/navigation';

import { <PERSON><PERSON><PERSON><PERSON> } from '@/components/utils/cache-key';
import pathsConfig from '@/config/paths.config';
import { urlConfig } from '@/config/url.config';
import { UserInfo } from '@/types/common.enum';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import clientCookie from 'js-cookie';
import { toast } from 'sonner';

import {
  type Body_login_login_access_token as AccessToken,
  type ApiError,
  AuthService,
  CancelablePromise,
  GoogleGoogleCallbackResponse,
  GoogleGoogleLoginResponse,
  LoginService,
  OpenAPI,
  type UserRegister,
} from '../client';
import { request as __request } from '../client/core/request';

export class GoogleService {
  public static login(): CancelablePromise<GoogleGoogleLoginResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/google/login',
    });
  }

  public static callback(
    params: any,
  ): CancelablePromise<GoogleGoogleCallbackResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/google/callback',
      query: params,
      errors: {
        422: 'Validation Error',
      },
    });
  }
}

const useAuth = () => {
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const queryClient = useQueryClient();

  const signUpMutation = useMutation({
    mutationFn: (data: UserRegister) =>
      AuthService.register({ requestBody: data }),

    onSuccess: () => {
      router.push(pathsConfig.auth.signIn);
      toast.success('Account created.', {
        description:
          'Your account has been created successfully. Please check your inbox to verify your account.',
      });
    },
    onError: (err: ApiError) => {
      let errDetail = (err.body as any)?.detail;

      if (err instanceof AxiosError) {
        errDetail = err.message;
      }
      toast.error('Something went wrong.', {
        description: errDetail,
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });

  const loginMutation = useMutation({
    mutationFn: (data: AccessToken) =>
      LoginService.loginAccessToken({
        formData: data,
      }),
    onSuccess: (response) => {
      clientCookie.set(UserInfo.WorkspacesID, response?.workspace_id ?? '');
      clientCookie.set(UserInfo.AccessToken, response.access_token);
      clientCookie.set(UserInfo.AppID, response.app_id ?? '');
      clientCookie.set(UserInfo.TeamID, response.team_id ?? '');

      if (response?.slack_oauth) {
        router.replace(pathsConfig.auth.slackComplete);
      } else {
        router.replace(pathsConfig.app.home);
      }
    },
    onError: (err: ApiError) => {
      let errDetail = (err.body as any)?.detail;

      if (err instanceof AxiosError) {
        errDetail = err.message;
      }

      if (Array.isArray(errDetail)) {
        errDetail = 'Something went wrong';
      }

      setError(errDetail);
      toast.error('Something went wrong.', {
        description: errDetail,
      });
    },
  });

  const initiateGoogleLoginMutation = useMutation({
    mutationFn: GoogleService.login,
    onSuccess: (response: any) => {
      window.location.href = response.url;
    },
    onError: (err: ApiError) => {
      let errDetail = (err.body as any)?.detail;

      if (err instanceof AxiosError) {
        errDetail = err.message;
      }

      if (Array.isArray(errDetail)) {
        errDetail = 'Failed to initiate Google login';
      }

      setError(errDetail);
      toast.error('Authentication Failed', {
        description: errDetail,
      });
    },
  });

  const googleCallbackMutation = useMutation({
    mutationFn: (params: any) => GoogleService.callback(params),
    onSuccess: (response) => {
      clientCookie.set(UserInfo.WorkspacesID, response?.workspace_id ?? '');
      clientCookie.set(UserInfo.AccessToken, response.access_token);
      clientCookie.set(UserInfo.AppID, response.app_id ?? '');
      clientCookie.set(UserInfo.TeamID, response.team_id ?? '');

      queryClient.invalidateQueries({ queryKey: [CacheKey.CurrentUser] });

      if (response?.slack_oauth) {
        router.push(pathsConfig.auth.slackComplete);
      } else {
        router.push(pathsConfig.app.home);
      }

      toast.success('Successfully logged in with Google');
    },
    onError: (err: ApiError) => {
      let errDetail = (err.body as any)?.detail;

      if (err instanceof AxiosError) {
        errDetail = err.message;
      }

      if (Array.isArray(errDetail)) {
        errDetail = 'Failed to complete Google authentication';
      }

      setError(errDetail);
      toast.error('Authentication Failed', {
        description: errDetail,
      });

      router.push(pathsConfig.auth.signIn);
    },
  });

  const recoverPassword = async (email: string) => {
    await LoginService.recoverPassword({
      email,
    });
  };

  const recoveryPasswordMutation = useMutation({
    mutationFn: recoverPassword,
    onSuccess: () => {
      toast.success('Success.', {
        description: 'A recovery email has been sent to your email address.',
      });

      router.push(pathsConfig.auth.signIn);
    },
    onError: (err: ApiError) => {
      let errDetail = (err.body as any)?.detail;

      if (err instanceof AxiosError) {
        errDetail = err.message;
      }

      if (Array.isArray(errDetail)) {
        errDetail = 'Something went wrong';
      }

      setError(errDetail);
      toast.error('Something went wrong.', {
        description: errDetail,
      });
    },
  });

  const logout = () => {
    clientCookie.remove(UserInfo.AccessToken);
    clientCookie.remove(UserInfo.WorkspacesID);
    queryClient.removeQueries();
    router.push(pathsConfig.auth.signIn);
  };

  const handleGoogleLogin = async (
    slackOAuth?: boolean,
    appId?: string,
    teamId?: string,
  ) => {
    try {
      // Direct redirect to backend Google login endpoint
      const url = new URL(`${urlConfig.apiUrl}/api/v1/google/login`);
      if (slackOAuth) {
        url.searchParams.append('slack_oauth', 'true');
      }
      if (appId) {
        url.searchParams.append('app_id', appId);
      }
      if (teamId) {
        url.searchParams.append('team_id', teamId);
      }
      window.location.href = url.toString();
    } catch (error) {
      toast.error('Failed to initiate Google login');
      console.error('Google login error:', error);
    }
  };

  const handleGoogleCallback = (params: any) => {
    googleCallbackMutation.mutate(params);
  };

  return {
    signUpMutation,
    loginMutation,
    logout,
    error,
    resetError: () => setError(null),
    recoveryPasswordMutation,
    handleGoogleLogin,
    handleGoogleCallback,
    isGoogleLoading:
      initiateGoogleLoginMutation.isPending || googleCallbackMutation.isPending,
  };
};

export default useAuth;
