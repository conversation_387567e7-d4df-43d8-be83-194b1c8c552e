import { KnowledgeBaseService } from '@/client/sdk.gen';
import { ResourceItem } from '@/components/chat/types';
import { DateFormat, formatDate } from '@/lib/date-utils';
import { useQuery } from '@tanstack/react-query';

interface UseKnowledgeBasesResult {
  kbs: ResourceItem[];
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

export function useKnowledgeBases(): UseKnowledgeBasesResult {
  const {
    data: kbs = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['knowledge-bases'],
    queryFn: async () => {
      const response = await KnowledgeBaseService.getKbs();

      // Handle response structure correctly - it has data and count properties
      const kbsArray = response.data || [];

      // Ensure response is an array and map to ResourceItem format with type safety
      const formattedKBs = kbsArray
        .filter(
          (kb) => kb && typeof kb === 'object' && 'id' in kb && !kb.is_deleted,
        )
        .map((kb) => {
          // Format the type for display
          const typeDisplay =
            kb.access_level === 'private' ? 'Personal KB' : 'Workspace KB';

          // Format dates for tooltip display
          const formattedCreatedAt = formatDate(
            kb.created_at,
            DateFormat.SHORT_DATE,
          );
          const formattedUpdatedAt = formatDate(
            kb.updated_at,
            DateFormat.SHORT_DATE,
          );

          // Create a formatted description
          const description = kb.description || 'No description';
          const truncatedDesc =
            description.length > 60
              ? description.substring(0, 57) + '...'
              : description;

          return {
            id: String(kb.id || ''),
            title: typeof kb.title === 'string' ? kb.title : 'Untitled KB',
            description: truncatedDesc,
            subtitle: typeDisplay, // Add type as subtitle
            metadata: {
              access_level:
                kb.access_level === 'private'
                  ? ('private' as const)
                  : ('shared' as const),
              usage_mode: kb.usage_mode || 'manual',
              tags: kb.tags || [],
              createdAt: kb.created_at,
              updatedAt: kb.updated_at,
              formattedCreatedAt: formattedCreatedAt,
              formattedUpdatedAt: formattedUpdatedAt,
              fullDescription: description, // Keep full description in metadata
              document_count: 0, // Placeholder for document count
              allowed_users: kb.allowed_users || [],
            },
          };
        });

      return formattedKBs;
    },
    staleTime: 300000, // Consider data fresh for 5 minutes
    gcTime: 600000, // Keep unused data in cache for 10 minutes
    refetchOnWindowFocus: false,
  });

  return {
    kbs,
    isLoading,
    error:
      error instanceof Error ? error : error ? new Error(String(error)) : null,
    refetch: async () => {
      await refetch();
    },
  };
}
