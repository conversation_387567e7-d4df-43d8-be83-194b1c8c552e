import { convertToUIMessage } from '@/lib/message-converters';
import { generateId } from '@/lib/utils';
import { v4 as uuidv4 } from 'uuid';

import { chatStream } from '../services/api';
import {
  APIMessage,
  ChatMessage,
  MessageDisplayComponentPublic,
} from '../types/chat';
import { InterruptConfirmation } from '../types/interrupt';

export interface ProcessMessageStreamProps {
  conversationId: string;
  setStreamingMessages: (
    messages: ChatMessage[] | ((prev: ChatMessage[]) => ChatMessage[]),
  ) => void;
  setIsStreaming: (isStreaming: boolean) => void;
  setThinkingPhases?: (phases: any[]) => void;
  setInterruptConfirmation: (
    confirmation: InterruptConfirmation | null,
  ) => void;
  isRestoringMessage?: boolean;
  toast: any;
  onRecommendations?: (recommendations: any[]) => void;
  resourceId?: string;
  abortController?: AbortController;
  setAbortController?: (controller: AbortController) => void;
}

export function createInterruptHandler(
  approve: boolean,
  message: APIMessage | null,
  {
    conversationId,
    setInterruptConfirmation,
    setIsStreaming,
    toast,
  }: {
    conversationId: string;
    setInterruptConfirmation: (
      confirmation: InterruptConfirmation | null,
    ) => void;
    setIsStreaming: (isStreaming: boolean) => void;
    toast: any;
  },
  props: ProcessMessageStreamProps,
) {
  return async () => {
    // Clear the confirmation dialog immediately to update UI
    setInterruptConfirmation(null);

    try {
      setIsStreaming(true);

      // Create a new AbortController for resumed stream
      const abortController = new AbortController();

      // If we were provided with an abortControllerRef setter, use it to update the reference
      if (props.setAbortController) {
        props.setAbortController(abortController);
      }

      const response = await chatStream(
        {
          conversationId,
          message: { content: '', resume: true, approve },
        },
        abortController,
      ); // Pass the abort controller to the chatStream

      if (!response.body) throw new Error('No response body received');

      // Attach the abort controller to the props so it can be accessed in message processing
      const enhancedProps = {
        ...props,
        abortController, // Make abort controller available in the stream processor
      };

      await processMessageStream(response.body.getReader(), enhancedProps);
    } catch (error) {
      console.error('Resume stream error:', error);
      // Check if this is an abort error
      if (error instanceof Error && error.name === 'AbortError') {
      } else {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to resume stream',
        });
      }
      setIsStreaming(false);
    }
  };
}

export async function processMessageStream(
  reader: ReadableStreamDefaultReader<Uint8Array>,
  props: ProcessMessageStreamProps,
) {
  const {
    conversationId,
    setStreamingMessages,
    setIsStreaming,
    setThinkingPhases,
    setInterruptConfirmation,
    isRestoringMessage,
    toast,
    onRecommendations,
    resourceId,
    abortController,
  } = props;

  const decoder = new TextDecoder();
  let currentMessage: APIMessage | null = null;
  let hasDisplayComponents = false;
  let readerCancelled = false;

  // Function to cancel the reader properly
  const cancelReader = async () => {
    if (!readerCancelled) {
      try {
        await reader.cancel();
        readerCancelled = true;
      } catch (e) {
        console.error('Error cancelling reader:', e);
      }
    }
  };

  try {
    while (true) {
      try {
        // Check before read if stream has been aborted
        if (abortController?.signal.aborted) {
          await cancelReader();
          break;
        }

        const { value, done } = await reader.read();

        if (done) {
          break;
        }

        // Check again if stream is still active
        if (abortController?.signal.aborted) {
          await cancelReader();
          break;
        }

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (!line.startsWith('data: ')) continue;

          try {
            const data = JSON.parse(line.slice(5));

            // Handle new nested event structure from Redis persistence
            // Backend now sends: {event: {type, content, etc}, timestamp, position}
            // We need to extract the actual event data
            let eventData = data;
            if (data.event && typeof data.event === 'object') {
              // This is the new nested structure, extract the event
              eventData = data.event;
            } else if (data.type) {
              // This is the old flat structure, use as-is
              eventData = data;
            } else {
              // Unknown structure, skip
              console.warn('Unknown event structure:', data);
              continue;
            }

            const content = eventData.content;

            if (eventData.type === 'message') {
              if (!currentMessage) {
                currentMessage = {
                  id: eventData.message_id || generateId(),
                  conversation_id: conversationId,
                  content: '',
                  role: 'assistant',
                  created_at: Math.floor(Date.now() / 1000),
                  agent_thoughts: [],
                  display_components: [],
                };
              }

              if (content.type === 'function_call') {
                const thoughtId = generateId();
                const thought = {
                  id: thoughtId,
                  message_id: currentMessage.id,
                  position: currentMessage.agent_thoughts.length,
                  thought: '',
                  tool: content.name,
                  tool_input: content.content || '',
                  observation: '',
                  created_at: Math.floor(Date.now() / 1000),
                };

                currentMessage.agent_thoughts.push(thought);
                updateStreamingMessages(currentMessage);
              } else if (content.type === 'on_recommendation_generation') {
                handleRecommendationGeneration(content, currentMessage);
              } else if (
                content.type === 'function_result' &&
                currentMessage?.agent_thoughts?.length > 0
              ) {
                handleFunctionResult(content, currentMessage);
              } else if (content.type === 'stream') {
                handleStream(content, currentMessage);
              } else if (content.type === 'display_component') {
                await handleDisplayComponent(content, currentMessage);
              } else if (content.type === 'chart_data') {
                handleChartData(content, currentMessage);
              } else if (content.type === 'error') {
                handleError(content);
                break;
              } else if (content.type === 'interrupt') {
                handleInterrupt(content, currentMessage);
                return;
              }
            } else if (eventData.type === 'complete') {
              setIsStreaming(false);
              // setThinkingPhases([]);
            }
          } catch (error) {
            // Check if the error is related to abort
            if (
              error instanceof Error &&
              (error.name === 'AbortError' ||
                error.message.includes('abort') ||
                error.message.includes('cancel'))
            ) {
              await cancelReader();
              break;
            }

            console.error('Error processing message:', error);
          }
        }
      } catch (error) {
        // Check if this is an abort error
        if (
          error instanceof Error &&
          (error.name === 'AbortError' ||
            error.message.includes('abort') ||
            error.message.includes('cancel'))
        ) {
          await cancelReader();
          break;
        }

        // For other errors, log and continue
        console.error('Error reading stream:', error);
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Error processing stream data',
        });
        break;
      }
    }
  } catch (error) {
    console.error('Fatal error in processMessageStream:', error);
  } finally {
    // Ensure reader is canceled and reset streaming state
    await cancelReader();
    setIsStreaming(false);
  }

  function updateStreamingMessages(message: APIMessage) {
    const convertedMessage = convertToUIMessage(message);
    setStreamingMessages((prev) => {
      if (isRestoringMessage) {
        return [convertedMessage];
      }
      const existingIndex = prev.findIndex(
        (msg) => msg.id === convertedMessage.id,
      );
      if (existingIndex !== -1) {
        return prev.map((msg, i) =>
          i === existingIndex ? convertedMessage : msg,
        );
      }
      return [...prev, convertedMessage];
    });
  }

  function handleRecommendationGeneration(content: any, message: APIMessage) {
    if (!message) {
      message = createNewMessage();
    }
    message.content = content.content;
    updateStreamingMessages(message);
  }

  function handleFunctionResult(content: any, message: APIMessage) {
    const lastThought =
      message.agent_thoughts[message.agent_thoughts.length - 1];
    lastThought.observation = content.content || '';
    updateStreamingMessages(message);
  }

  function handleStream(content: any, message: APIMessage) {
    message.content = (message.content || '') + (content.content || '');
    updateStreamingMessages(message);
  }

  async function handleDisplayComponent(content: any, message: APIMessage) {
    if (!message) {
      message = createNewMessage();
    }

    if (!message.display_components) {
      message.display_components = [];
    }

    const componentExists = message.display_components.some(
      (comp) => comp.id === content.content.id,
    );

    if (!componentExists) {
      message.display_components.push(content.content);
      hasDisplayComponents = true;
      updateStreamingMessages(message);

      // Handle recommendations table
      if (content.content.type === 'table') {
        handleRecommendationsTable(content.content);
      }
    }
  }

  function handleChartData(content: any, message: APIMessage) {
    if (!message) {
      message = createNewMessage();
    }

    if (!message.display_components) {
      message.display_components = [];
    }

    const componentExists = message.display_components.some(
      (comp) => comp.id === content.content.id,
    );

    if (!componentExists) {
      const chartComponent: MessageDisplayComponentPublic = {
        id: content.content.id,
        type: 'chart',
        chart_type: content.content.chart_type,
        title: null,
        description: null,
        data: content.content.data,
        config: {},
        position: message.display_components.length,
        created_at: new Date().toISOString(),
      };

      message.display_components.push(chartComponent);
      hasDisplayComponents = true;
      updateStreamingMessages(message);
    }
  }

  function handleError(content: any) {
    toast({
      variant: 'destructive',
      title: 'Error',
      description: content.content,
    });
  }

  function handleInterrupt(content: any, message: APIMessage) {
    const interruptContent =
      typeof content.content === 'string'
        ? content.content
        : content.content?.value || 'Would you like to proceed?';

    const formattedMessage = {
      value: interruptContent,
      resumable: content.content?.resumable ?? true,
      ns: content.content?.ns,
      when: content.content?.when || new Date().toISOString(),
    };

    // Create the confirmation object with handlers
    const confirmationObj = {
      message: formattedMessage,
      onConfirm: createInterruptHandler(
        true,
        message,
        {
          conversationId,
          setInterruptConfirmation,
          setIsStreaming,
          toast,
        },
        props,
      ),
      onCancel: createInterruptHandler(
        false,
        message,
        {
          conversationId,
          setInterruptConfirmation,
          setIsStreaming,
          toast,
        },
        props,
      ),
    };

    // Set the confirmation state to trigger UI update
    setInterruptConfirmation(confirmationObj);
  }

  function createNewMessage(): APIMessage {
    return {
      id: generateId(),
      conversation_id: conversationId,
      content: '',
      role: 'assistant',
      created_at: Math.floor(Date.now() / 1000),
      agent_thoughts: [],
      display_components: [],
    };
  }

  function handleRecommendationsTable(tableContent: any) {
    if (!onRecommendations) return;

    const recommendations = tableContent.data.rows.map((row: any[]) => {
      // Parse potential savings with fallback to 0 if invalid
      let potentialSavings = 0;
      try {
        if (row[3] && typeof row[3] === 'string') {
          const rawValue = row[3].replace('$', '').replace(',', '');
          potentialSavings = parseFloat(rawValue);
          // If NaN, set to 0
          if (isNaN(potentialSavings)) potentialSavings = 0;
        } else if (typeof row[3] === 'number') {
          potentialSavings = row[3];
        }
      } catch (e) {
        console.warn('Error parsing potential savings:', e);
        potentialSavings = 0;
      }

      return {
        id: uuidv4(),
        type: row[0],
        title: row[1],
        description: row[2],
        potential_savings: potentialSavings,
        effort: row[4]?.toLowerCase() || 'medium',
        risk: row[5]?.toLowerCase() || 'low',
        technical_guidances: row[6] || [],
        document_url: row[7] || '',
        status: row[8]?.toLowerCase() || 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        resource_id: resourceId || '',
      };
    });

    onRecommendations(recommendations);
  }
}
