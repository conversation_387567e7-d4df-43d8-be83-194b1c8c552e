import {
  MessageDisplayComponentPublic,
  MessageStreamInput,
  RecommendationPublic,
} from '@/client/types.gen';
import { Message as ChatMessage } from '@/components/chat/types';
import { APIMessage } from '@/types/agent';

export interface ChatStreamParams {
  conversationId: string;
  message: MessageStreamInput;
}

export interface MessageStreamOptions {
  onRecommendations?: (recommendations: RecommendationPublic[]) => void;
  resourceId?: string;
}

export type {
  ChatMessage,
  MessageDisplayComponentPublic,
  RecommendationPublic,
  APIMessage,
};
