import { SchemaConversationRenameRequest } from '@/openapi-ts/gens';
import { api, fetchData } from '@/openapi-ts/openapi-fetch';

import { ConversationQueryParams } from '../models/conversation.type';

export const conversationApi = {
  // Basic CRUD operations
  list: (query: ConversationQueryParams) =>
    fetchData(
      api.GET('/api/v1/autonomous-agents/conversations', {
        params: {
          query,
        },
      }),
    ),

  detail: (conversationId: string) => ({
    rename: (body: SchemaConversationRenameRequest) =>
      fetchData(
        api.PUT(
          '/api/v1/autonomous-agents/conversations/{conversation_id}/name',
          {
            params: { path: { conversation_id: conversationId } },
            body,
          },
        ),
      ),

    delete: () =>
      fetchData(
        api.DELETE(
          '/api/v1/autonomous-agents/conversations/{conversation_id}',
          {
            params: { path: { conversation_id: conversationId } },
          },
        ),
      ),
  }),

  // Agent-specific operations
  getWithAgent: (agentId: string, resourceId?: string) =>
    fetchData(
      api.GET('/api/v1/autonomous-agents/conversations', {
        params: {
          query: {
            agent_id: agentId,
            ...(resourceId && { resource_id: resourceId }),
          },
        },
      }),
    ),
};
