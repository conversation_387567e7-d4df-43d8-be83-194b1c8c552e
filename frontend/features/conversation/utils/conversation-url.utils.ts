import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';

import pathsConfig from '@/config/paths.config';

export const conversationUrlUtils = {
  /**
   * Build URL for agent conversation with optional initial message
   */
  buildAgentConversationUrl: (
    agentId: string,
    conversationId: string,
    initialMessage?: string,
  ): string => {
    const baseUrl = `${pathsConfig.app.agents}/${agentId}`;
    const params = new URLSearchParams();

    params.append('conversation', conversationId);

    if (initialMessage?.trim()) {
      params.append('initialMessage', initialMessage.trim());
    }

    return `${baseUrl}?${params.toString()}`;
  },

  /**
   * Navigate to a specific conversation
   */
  navigateToConversation: (
    router: AppRouterInstance,
    agentId: string,
    conversationId: string,
    initialMessage?: string,
  ): void => {
    const url = conversationUrlUtils.buildAgentConversationUrl(
      agentId,
      conversationId,
      initialMessage,
    );
    router.push(url);
  },

  /**
   * Extract conversation parameters from URL search params
   */
  parseConversationParams: (searchParams: URLSearchParams) => {
    return {
      conversationId: searchParams.get('conversation'),
      initialMessage: searchParams.get('initialMessage'),
    };
  },

  /**
   * Remove a URL parameter from the current path
   */
  removeUrlParam: (
    pathname: string,
    searchParams: URLSearchParams,
    paramToRemove: string,
  ): string => {
    const params = new URLSearchParams(searchParams);
    params.delete(paramToRemove);
    const queryString = params.toString();
    return queryString ? `${pathname}?${queryString}` : pathname;
  },

  /**
   * Update URL with conversation ID for new conversations
   */
  updateUrlWithConversationId: (
    pathname: string,
    searchParams: URLSearchParams,
    conversationId: string,
  ): string => {
    const params = new URLSearchParams(searchParams);
    params.set('conversation', conversationId);
    const queryString = params.toString();
    return queryString ? `${pathname}?${queryString}` : pathname;
  },
};
