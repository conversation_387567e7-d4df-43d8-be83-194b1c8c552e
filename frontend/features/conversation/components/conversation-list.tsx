'use client';

import { useDeferredValue, useEffect, useState } from 'react';

import Link from 'next/link';

import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import { PageSkeleton } from '@/components/ui/common/page';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Heading } from '@/components/ui/heading';
import { InputIconPrefix } from '@/components/ui/input-icon-prefix';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import pathsConfig from '@/config/paths.config';
import { ConfirmDeleteConversation } from '@/features/conversation/components/confirm-delete-conversation';
import { RenameConversationDialog } from '@/features/conversation/components/rename-conversation-dialog';
import { useUserContext } from '@/features/user/provider/user-provider';
import { formatDateOnly, formatSmartDate, formatTime } from '@/lib/date-utils';
import { groupBy, map } from 'lodash';
import { Loader2Icon, MoreHorizontalIcon, SearchIcon } from 'lucide-react';
import { useInView } from 'react-intersection-observer';

import { conversationQuery } from '../hooks/conversation.query';

export function ConversationList() {
  const { agentId } = useUserContext();
  const [valueSearch, setValueSearch] = useState('');
  const debouncedValueSearch = useDeferredValue(valueSearch);

  const { data, isFetchingNextPage, fetchNextPage, hasNextPage, isLoading } =
    conversationQuery.query.useInfiniteList({
      agent_id: agentId,
      search: debouncedValueSearch,
    });

  const { ref: loadMoreRef, inView } = useInView({
    threshold: 0.1,
    rootMargin: '100px',
  });

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage, isFetchingNextPage, fetchNextPage]);

  return (
    <>
      <If condition={data}>
        {(data) => {
          const histories = data.pages.flatMap((page) => page.data);

          const historyGroupedByDate = groupBy(histories, (history) =>
            formatDateOnly(history.created_at),
          );
          return (
            <>
              <div className="space-y-1">
                <Heading level={5}>Recent Conversations</Heading>
                <InputIconPrefix
                  Icon={SearchIcon}
                  placeholder="Search conversations..."
                  value={debouncedValueSearch}
                  onChange={(e) => setValueSearch(e.target.value)}
                />
              </div>
              <Separator />
              <ScrollArea className="h-[400px]">
                {map(historyGroupedByDate, (histories, date) => {
                  return (
                    <div key={date}>
                      <p className="text-muted-foreground text-xs font-bold">
                        {formatSmartDate(date)}
                      </p>

                      <div className="py-1">
                        {histories.map((history) => (
                          <Link
                            key={history.id}
                            href={`${pathsConfig.app.agentDetail(agentId)}?&conversation=${history.id}`}
                            className="hover:bg-secondary flex justify-between space-y-1 rounded-md px-2 py-1 transition-colors"
                          >
                            <div>
                              <p className="max-w-40 truncate text-sm sm:max-w-64">
                                {history.name}
                              </p>
                              <p className="text-muted-foreground text-xs">
                                {formatTime(history.created_at)}
                              </p>
                            </div>

                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontalIcon className="size-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent
                                onClick={(e) => e.stopPropagation()}
                              >
                                <RenameConversationDialog
                                  conversationId={history.id}
                                  defaultName={history.name}
                                >
                                  <DropdownMenuItem
                                    onSelect={(e) => e.preventDefault()}
                                  >
                                    Rename
                                  </DropdownMenuItem>
                                </RenameConversationDialog>
                                <ConfirmDeleteConversation
                                  conversationId={history.id}
                                >
                                  <DropdownMenuItem
                                    onSelect={(e) => e.preventDefault()}
                                    className="text-destructive/80 hover:!text-destructive flex h-full w-full items-center gap-2"
                                  >
                                    Delete
                                  </DropdownMenuItem>
                                </ConfirmDeleteConversation>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </Link>
                        ))}
                      </div>
                    </div>
                  );
                })}

                <If condition={hasNextPage}>
                  <div ref={loadMoreRef} className="flex justify-center py-4">
                    <If
                      condition={isFetchingNextPage}
                      fallback={
                        <div className="text-muted-foreground text-sm">
                          Scroll to load more...
                        </div>
                      }
                    >
                      <Loader2Icon className="text-primary size-4 animate-spin" />
                    </If>
                  </div>
                </If>
              </ScrollArea>
            </>
          );
        }}
      </If>
      <If condition={isLoading}>
        <PageSkeleton />
      </If>
    </>
  );
}
