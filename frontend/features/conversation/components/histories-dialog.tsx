import { PropsWithChildren } from 'react';

import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';

import { ConversationList } from './conversation-list';

export function HistoriesDialog({ children }: PropsWithChildren) {
  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent>
        <ConversationList />
      </DialogContent>
    </Dialog>
  );
}
