'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  SidebarGroupLabel,
  SidebarMenuButton,
  useSidebar,
} from '@/components/ui/sidebar';
import { cn } from '@/lib/utils';
import { HistoryIcon } from 'lucide-react';

import { HistoriesPopup } from './histories-popup';
import { RecentConversationList } from './recent-conversation-list';

export function SidebarConversation() {
  const { open } = useSidebar();

  return (
    <>
      <SidebarGroupLabel className={cn({ hidden: !open })}>
        Recent Conversations
      </SidebarGroupLabel>
      <RecentConversationList className="group-data-[minimized=true]:hidden" />
      <HistoriesPopup>
        <SidebarMenuButton tooltip="View All History" asChild>
          <Button
            variant="ghost"
            size="sm"
            className="relative w-full justify-start gap-2 px-2"
          >
            <HistoryIcon className="size-4" />
            <span>View All History</span>
          </Button>
        </SidebarMenuButton>
      </HistoriesPopup>
    </>
  );
}
