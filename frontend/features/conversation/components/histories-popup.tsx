'use client';

import { PropsWithChildren } from 'react';

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

import { ConversationList } from './conversation-list';

export function HistoriesPopup({ children }: PropsWithChildren) {
  return (
    <Popover>
      <PopoverTrigger asChild>{children}</PopoverTrigger>

      <PopoverContent
        align="start"
        side="right"
        sideOffset={8}
        className="space-y-2 sm:w-96"
      >
        <ConversationList />
      </PopoverContent>
    </Popover>
  );
}
