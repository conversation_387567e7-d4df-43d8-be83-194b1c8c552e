'use client';

import Link from 'next/link';

import { If } from '@/components/ui/common/if';
import { Skeleton } from '@/components/ui/skeleton';
import pathsConfig from '@/config/paths.config';
import { useUserContext } from '@/features/user/provider/user-provider';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';
import { range } from 'lodash';

import { conversationQuery } from '../hooks/conversation.query';

type Props = {
  className?: string;
};

export function RecentConversationList({ className }: Props) {
  const { agentId } = useUserContext();

  const { data, isLoading } = conversationQuery.query.useInfiniteList({
    agent_id: agentId,
  });

  return (
    <div className={cn('space-y-1', className)}>
      <If condition={data}>
        {(data) =>
          data.pages
            .flatMap((page) => page.data)
            .slice(0, 3)
            .map((conversation) => (
              <Link
                key={conversation.id}
                href={`${pathsConfig.app.agentDetail(agentId)}?&conversation=${conversation.id}`}
                className="hover:bg-sidebar-accent flex flex-col justify-between space-y-1 rounded-md px-2 py-1 transition-colors"
              >
                <p className="max-w-40 truncate text-sm sm:max-w-64">
                  {conversation.name}
                </p>
                <p className="text-muted-foreground text-xs">
                  {formatDistanceToNow(conversation.created_at)}
                </p>
              </Link>
            ))
        }
      </If>
      <If condition={isLoading}>
        {range(3).map((i) => (
          <Skeleton key={i} className="h-4 w-full" />
        ))}
      </If>
    </div>
  );
}
