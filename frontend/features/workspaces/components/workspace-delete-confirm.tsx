'use client';

import { PropsWithChildren } from 'react';

import { DeleteConfirmAlert } from '@/components/ui/common/delete-confirm-alert';

import { workspaceQuery } from '../hooks/workspace.query';

type Props = PropsWithChildren<{
  workspaceId: string;
}>;

export function WorkspaceDeleteConfirm({ workspaceId, children }: Props) {
  const { mutate, isPending } = workspaceQuery.mutation.useDelete(workspaceId);

  const onConfirm = (toggle: () => void) => {
    mutate(undefined, {
      onSuccess: toggle,
    });
  };

  return (
    <DeleteConfirmAlert
      title="Delete Workspace"
      description="Are you sure you want to delete this workspace?"
      onConfirm={onConfirm}
      loading={isPending}
    >
      {children}
    </DeleteConfirmAlert>
  );
}
