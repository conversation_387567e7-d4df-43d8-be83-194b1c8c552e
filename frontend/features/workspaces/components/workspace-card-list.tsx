'use client';

import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useUserContext } from '@/features/user/provider/user-provider';
import { CreateWorkspaceDialog } from '@/features/workspaces/components/create-workspace-dialog';
import { filter, includes } from 'lodash';
import { PlusIcon, Search } from 'lucide-react';

import { WorkspaceCard } from './workspace-card';

export function WorkspaceCardList() {
  const { workspaces, currentWorkspace } = useUserContext();
  const [searchQuery, setSearchQuery] = useState('');

  const filteredWorkspaces = filter(workspaces, (workspace) =>
    includes(workspace.name.toLowerCase(), searchQuery.toLowerCase()),
  );

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <div className="relative grow">
          <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
          <Input
            placeholder="Search workspaces..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <CreateWorkspaceDialog>
          <Button className="gap-2">
            <PlusIcon className="size-4" />
            Create Workspace
          </Button>
        </CreateWorkspaceDialog>
      </div>

      <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 2xl:grid-cols-3">
        {filteredWorkspaces.map((workspace) => (
          <WorkspaceCard
            key={workspace.id}
            workspace={workspace}
            isCurrentWorkspace={workspace.id === currentWorkspace?.id}
          />
        ))}
      </div>
    </div>
  );
}
