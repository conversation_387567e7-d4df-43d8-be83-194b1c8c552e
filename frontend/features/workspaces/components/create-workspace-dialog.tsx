'use client';

import { PropsWithChildren } from 'react';

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { CloudProvider } from '@/openapi-ts/gens';
import { SubmitHandler } from 'react-hook-form';
import { useToggle } from 'usehooks-ts';

import { workspaceQuery } from '../hooks/workspace.query';
import { WorkspaceSchema } from '../schema/workspace.schema';
import { WorkspaceForm } from './workspace-form';

export function CreateWorkspaceDialog({ children }: PropsWithChildren) {
  const [isOpen, toggle] = useToggle(false);
  const { mutate, isPending } = workspaceQuery.mutation.useCreate();

  const onSubmit: SubmitHandler<WorkspaceSchema> = (data) => {
    mutate(data, {
      onSuccess: toggle,
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Create Workspace</DialogTitle>
        </DialogHeader>
        <WorkspaceForm
          onSubmit={onSubmit}
          isPending={isPending}
          defaultValues={{
            provider: CloudProvider.AWS,
          }}
        />
      </DialogContent>
    </Dialog>
  );
}
