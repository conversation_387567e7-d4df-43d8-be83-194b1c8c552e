'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import { UpdateWrapper } from '@/components/ui/common/update-wrapper';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { SubmitHandler, useForm } from 'react-hook-form';

import { WORKSPACE_PROVIDER_CONFIG } from '../config/workspace-provider.config';
import { WorkspaceSchema, workspaceSchema } from '../schema/workspace.schema';

type Props = {
  onSubmit: SubmitHandler<WorkspaceSchema>;
  defaultValues?: Partial<WorkspaceSchema>;
  isPending?: boolean;
};

export function WorkspaceForm({ onSubmit, defaultValues, isPending }: Props) {
  const form = useForm<WorkspaceSchema>({
    resolver: zodResolver(workspaceSchema),
    defaultValues,
  });

  const { control, handleSubmit } = form;

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={control}
          name="provider"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Provider</FormLabel>
              <FormDescription>
                Select your preferred cloud infrastructure provider. This cannot
                be changed after workspace creation.
              </FormDescription>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  value={field.value}
                  className="space-y-1"
                >
                  {WORKSPACE_PROVIDER_CONFIG.LIST.map((provider) => (
                    <FormItem
                      key={provider.value}
                      className="flex flex-row items-center space-y-0 space-x-2"
                    >
                      <FormControl>
                        <RadioGroupItem
                          value={provider.value}
                          disabled={provider.isComingSoon}
                        />
                      </FormControl>
                      <FormLabel
                        className={cn(
                          provider.isComingSoon &&
                            'cursor-not-allowed opacity-60',
                        )}
                      >
                        <div className="flex items-center gap-2">
                          {
                            WORKSPACE_PROVIDER_CONFIG.CONFIG[provider.value]
                              .icon
                          }
                          {provider.fullLabel}
                          <If condition={provider.isComingSoon}>
                            <Badge variant="secondary">Coming Soon</Badge>
                          </If>
                        </div>
                      </FormLabel>
                    </FormItem>
                  ))}
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Name</FormLabel>
              <FormControl>
                <Input {...field} placeholder="Enter workspace name" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  placeholder="Enter workspace description"
                  className="min-h-36"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end">
          <Button type="submit" disabled={isPending}>
            <UpdateWrapper isPending={isPending}>Save</UpdateWrapper>
          </Button>
        </div>
      </form>
    </Form>
  );
}
