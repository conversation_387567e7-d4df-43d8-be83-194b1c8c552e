'use client';

import { PropsWithChildren } from 'react';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { SchemaWorkspacePublic } from '@/openapi-ts/gens';
import { differentObject } from '@/utils/object';
import { flow, partialRight, pick } from 'lodash';
import { SubmitHandler } from 'react-hook-form';
import { useToggle } from 'usehooks-ts';

import { workspaceQuery } from '../hooks/workspace.query';
import { WorkspaceSchema } from '../schema/workspace.schema';
import { WorkspaceForm } from './workspace-form';

type Props = PropsWithChildren<{
  workspace: SchemaWorkspacePublic;
}>;

export function EditWorkspaceForm({ workspace, children }: Props) {
  const [isOpen, toggle] = useToggle(false);
  const { mutate, isPending } = workspaceQuery.mutation.useUpdate(workspace.id);

  const defaultValues: WorkspaceSchema = pick(workspace, [
    'name',
    'description',
    'provider',
  ]);

  const onSubmit: SubmitHandler<WorkspaceSchema> = flow(
    differentObject(defaultValues),
    partialRight(mutate, {
      onSuccess: toggle,
    }),
  );

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Edit Workspace</DialogTitle>
        </DialogHeader>
        <WorkspaceForm
          onSubmit={onSubmit}
          isPending={isPending}
          defaultValues={defaultValues}
        />
      </DialogContent>
    </Dialog>
  );
}
