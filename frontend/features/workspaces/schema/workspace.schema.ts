import { CloudProvider } from '@/openapi-ts/gens';
import { z } from 'zod';

export const workspaceSchema = z.object({
  name: z
    .string()
    .min(1, { message: 'Workspace name is required.' })
    .max(255, { message: 'Workspace name must be at most 255 characters.' }),
  description: z
    .string()
    .max(1000, { message: 'Description must not exceed 1000 characters.' })
    .optional(),
  provider: z.nativeEnum(CloudProvider, {
    required_error: 'Please select a cloud provider',
  }),
});

export type WorkspaceSchema = z.infer<typeof workspaceSchema>;
