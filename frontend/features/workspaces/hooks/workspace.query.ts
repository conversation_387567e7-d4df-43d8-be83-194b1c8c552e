import { useRouter } from 'next/navigation';

import { clearAllCache } from '@/utils/server/server-action';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'sonner';

import { workspaceApi } from '../services/workspace.api';

const useCreate = () => {
  const router = useRouter();

  return useMutation({
    mutationFn: workspaceApi.create,
    onSuccess: () => {
      toast.success('Workspace created successfully');
      clearAllCache();
      router.refresh();
    },
  });
};

const useUpdate = (id: string) => {
  const router = useRouter();

  return useMutation({
    mutationFn: workspaceApi.detail(id).update,
    onSuccess: () => {
      toast.success('Workspace updated successfully');
      clearAllCache();
      router.refresh();
    },
  });
};

const useDelete = (id: string) => {
  const router = useRouter();

  return useMutation({
    mutationFn: workspaceApi.detail(id).delete,
    onSuccess: () => {
      toast.success('Workspace deleted successfully');
      clearAllCache();
      router.refresh();
    },
  });
};

export const workspaceQuery = {
  mutation: {
    useCreate,
    useUpdate,
    useDelete,
  },
};
