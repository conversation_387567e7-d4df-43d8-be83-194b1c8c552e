import { Button } from '@/components/ui/button';
import { WithPaginationDefaults } from '@/utils/with-pagination-defaults';
import { PlusIcon } from 'lucide-react';

import { UserQueryParams } from '../models/user.type';
import { CreateUserDialog } from './create-user-dialog';
import { UserTable } from './user-table/user-table';

type Props = {
  searchParams: WithPaginationDefaults<UserQueryParams>;
};

export function UserListPage({ searchParams }: Props) {
  return (
    <div className="space-y-4">
      <div className="flex justify-end">
        <CreateUserDialog>
          <Button className="gap-2">
            <PlusIcon className="size-4" />
            Create New User
          </Button>
        </CreateUserDialog>
      </div>
      <UserTable searchParams={searchParams} />
    </div>
  );
}
