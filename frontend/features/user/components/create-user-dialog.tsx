'use client';

import { PropsWithChildren } from 'react';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { SubmitHandler } from 'react-hook-form';
import { useToggle } from 'usehooks-ts';

import { userQuery } from '../hooks/user.query';
import { UserSchema } from '../schema/user.schema';
import { UserForm } from './user-form';

export function CreateUserDialog({ children }: PropsWithChildren) {
  const [isOpen, toggle] = useToggle(false);
  const { mutate, isPending } = userQuery.mutation.useCreate();

  const onSubmit: SubmitHandler<Omit<UserSchema, 'confirm_password'>> = (
    data,
  ) => {
    mutate(data, {
      onSuccess: toggle,
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Create User</DialogTitle>
        </DialogHeader>
        <UserForm
          onSubmit={onSubmit}
          isPending={isPending}
          defaultValues={{
            is_active: false,
            is_email_verified: false,
            is_superuser: false,
          }}
        />
      </DialogContent>
    </Dialog>
  );
}
