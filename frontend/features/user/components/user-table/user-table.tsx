'use client';

import { PageSkeleton } from '@/components/ui/common/page';
import { ErrorAlert } from '@/components/ui/error-alert';
import { NewDataTable } from '@/components/ui/table/new-data-table';
import { WithPaginationDefaults } from '@/utils/with-pagination-defaults';

import { userQuery } from '../../hooks/user.query';
import { UserQueryParams } from '../../models/user.type';
import { userColumns } from './user.column';

type Props = {
  searchParams: WithPaginationDefaults<UserQueryParams>;
};

export function UserTable({ searchParams }: Props) {
  const { data, isLoading, isRefetching, isError } =
    userQuery.query.useList(searchParams);

  if (data) {
    return (
      <NewDataTable
        data={data.data ?? []}
        columns={userColumns}
        pageSize={10}
        pageIndex={Number(searchParams.page ?? 1) - 1}
        pageCount={Math.ceil(data.count / 10)}
        isRefetching={isRefetching}
        itemCount={data.count}
      />
    );
  }

  if (isLoading) return <PageSkeleton />;

  if (isError) {
    return (
      <ErrorAlert
        title="Failed to Load Users"
        description="Unable to fetch the user list. Please check your connection and try again."
      />
    );
  }
}
