import { SchemaUserPublic } from '@/openapi-ts/gens';
import { ColumnDef } from '@tanstack/react-table';

export const userColumns: ColumnDef<SchemaUserPublic>[] = [
  {
    accessorKey: 'full_name',
    header: 'Full Name',
  },
  {
    accessorKey: 'email',
    header: 'Email',
  },
  {
    accessorKey: 'is_superuser',
    header: 'Role',
    cell: ({ row }) => (row.original.is_superuser ? 'Superuser' : 'User'),
  },
  {
    accessorKey: 'is_active',
    header: 'Status',
    cell: ({ row }) => (row.original.is_active ? 'Active' : 'Inactive'),
  },
];
