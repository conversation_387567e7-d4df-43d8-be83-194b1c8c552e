'use client';

import {
  PropsWithChildren,
  createContext,
  useContext,
  useEffect,
  useMemo,
} from 'react';

import { SchemaUserDetail, SchemaWorkspacePublic } from '@/openapi-ts/gens';
import { UserInfo } from '@/types/common.enum';
import clientCookie from 'js-cookie';
import { find } from 'lodash';

type UserContextValue = {
  user: SchemaUserDetail;
  workspaces: SchemaWorkspacePublic[];
  currentWorkspace: SchemaWorkspacePublic | undefined;
  agentId: string;
};

const UserContext = createContext<UserContextValue | null>(null);

export const UserProvider = ({
  children,
  user,
  workspaces,
  agentId,
}: PropsWithChildren<Omit<UserContextValue, 'currentWorkspace'>>) => {
  const currentWorkspaceId = clientCookie.get(UserInfo.WorkspacesID);

  const currentWorkspace = useMemo(() => {
    return find(workspaces, {
      id: currentWorkspaceId,
    });
  }, [currentWorkspaceId, workspaces]);

  useEffect(() => {
    if (!currentWorkspace && workspaces.length > 0) {
      const defaultWorkspace = find(workspaces, {
        is_default: true,
      });
      
      if (defaultWorkspace) {
        clientCookie.set(UserInfo.WorkspacesID, defaultWorkspace.id);
      }
    }
  }, [currentWorkspace, workspaces]);

  return (
    <UserContext.Provider
      value={{ user, workspaces, currentWorkspace, agentId }}
    >
      {children}
    </UserContext.Provider>
  );
};

export const useUserContext = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUserContext must be used within an UserProvider');
  }
  return context;
};
