import { FC, createElement } from 'react';

import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { If } from '@/components/ui/common/if';
import { cn } from '@/lib/utils';
import AwsLogo from '@/public/aws-logo.svg';
import Azure<PERSON>ogo from '@/public/azure-logo.svg';
import Gcp<PERSON>ogo from '@/public/gcp-logo.svg';
import SandboxLogo from '@/public/sandbox-logo.svg';
import { createUtilityConfig } from '@/utils/option-config';
import { CheckIcon } from 'lucide-react';

export enum CloudProvider {
  AWS = 'aws',
  GCP = 'gcp',
  AZURE = 'azure',
  SANDBOX = 'sandbox',
}

export const CLOUD_PROVIDER_CONFIG = createUtilityConfig({
  [CloudProvider.AWS]: {
    label: 'AWS',
    fullName: 'Amazon Web Services',
    icon: AwsLogo,
    isPopular: true,
  },
  [CloudProvider.GCP]: {
    label: 'GCP',
    fullName: 'Google Cloud Platform',
    icon: GcpLogo,
  },
  [CloudProvider.AZURE]: {
    label: 'Azure',
    fullName: 'Microsoft Azure',
    icon: AzureLogo,
  },
  [CloudProvider.SANDBOX]: {
    label: 'Sandbox',
    fullName: 'Sandbox Mode',
    icon: SandboxLogo,
    noCredentialsRequired: true,
    isComingSoon: true,
  },
} satisfies Record<
  CloudProvider,
  {
    label: string;
    icon: FC<{ className?: string }>;
    fullName: string;
    isPopular?: boolean;
    noCredentialsRequired?: boolean;
    isComingSoon?: boolean;
  }
>);

export const CloudProviderCard = ({
  provider,
  selected,
  isComingSoon,
}: {
  provider: CloudProvider;
  selected?: boolean;
  isComingSoon?: boolean;
}) => {
  const config = CLOUD_PROVIDER_CONFIG.CONFIG[provider];

  return (
    <Card
      className={cn(
        'relative flex gap-2 p-3 transition-colors',
        'hover:bg-primary/5 hover:border-primary/50',
        selected && 'border-primary ring-primary pointer-events-none ring-1',
        isComingSoon && 'pointer-events-none opacity-50 select-none',
      )}
    >
      {createElement(config.icon, {
        className: 'size-14 shrink-0',
      })}
      <div className="space-y-1">
        <p className="text-lg font-semibold">{config.label}</p>
        <p className="text-muted-foreground">{config.fullName}</p>
      </div>
      <If condition={config.noCredentialsRequired}>
        <Badge
          variant="info"
          className="absolute top-0 right-0 -translate-y-1/2"
        >
          No Credentials Required
        </Badge>
      </If>
      <If condition={config.isPopular}>
        <Badge className="absolute top-0 right-0 -translate-y-1/2">
          Popular
        </Badge>
      </If>
      <If condition={selected}>
        <CheckIcon className="text-primary absolute top-1/2 right-2 size-6 -translate-y-1/2" />
      </If>
      <If condition={isComingSoon}>
        <Badge variant="ghost-info" className="absolute right-2 bottom-2">
          Coming Soon
        </Badge>
      </If>
    </Card>
  );
};
