'use client';

import { PropsWithChildren, createContext, useContext } from 'react';

import { SchemaConstantOption } from '@/openapi-ts/gens';

type OnboardingContextValue = {
  awsRegions: SchemaConstantOption[];
};

const OnboardingContext = createContext<OnboardingContextValue | null>(null);

export const OnboardingProvider = ({
  children,
  awsRegions,
}: PropsWithChildren<OnboardingContextValue>) => {
  return (
    <OnboardingContext.Provider value={{ awsRegions }}>
      {children}
    </OnboardingContext.Provider>
  );
};

export const useOnboardingContext = () => {
  const context = useContext(OnboardingContext);
  if (!context) {
    throw new Error(
      'useOnboardingContext must be used within an OnboardingProvider',
    );
  }
  return context;
};
