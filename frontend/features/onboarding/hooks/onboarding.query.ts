import { useRouter } from 'next/navigation';

import { clearAllCache } from '@/utils/server/server-action';
import { useMutation, useQuery } from '@tanstack/react-query';
import { toast } from 'sonner';

import { onboardingApi } from '../services/onboarding.api';

const useCreateWorkspace = () => {
  return useMutation({
    mutationFn: onboardingApi.createWorkspace,
    onSuccess: () => {
      toast.success('Workspace created successfully');
    },
  });
};

const useConnectAws = () => {
  return useMutation({
    mutationFn: onboardingApi.connect.aws,
    onSuccess: () => {
      toast.success('AWS connected successfully');
    },
  });
};

const useConnectGcp = () => {
  return useMutation({
    mutationFn: onboardingApi.connect.gcp,
    onSuccess: () => {
      toast.success('GCP connected successfully');
    },
  });
};

const useConnectAzure = () => {
  return useMutation({
    mutationFn: onboardingApi.connect.azure,
    onSuccess: () => {
      toast.success('Azure connected successfully');
    },
  });
};

const useGetTaskTemplates = () => {
  return useQuery({
    queryKey: ['task-templates'],
    queryFn: onboardingApi.getTaskTemplates,
  });
};

const useCompleteTaskTemplate = () => {
  const router = useRouter();

  return useMutation({
    mutationFn: onboardingApi.completeTaskTemplate,
    onSuccess: async () => {
      await clearAllCache();
      router.refresh();
      toast.success('Task template completed successfully');
    },
  });
};

export const onboardingQuery = {
  query: {
    useGetTaskTemplates,
  },
  mutation: {
    useCreateWorkspace,
    useConnectAws,
    useConnectGcp,
    useConnectAzure,
    useCompleteTaskTemplate,
  },
};
