'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { If } from '@/components/ui/common/if';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { zodResolver } from '@hookform/resolvers/zod';
import { AnimatePresence, motion } from 'framer-motion';
import { ArrowRightIcon, Loader2Icon } from 'lucide-react';
import { useForm, useWatch } from 'react-hook-form';

import {
  CLOUD_PROVIDER_CONFIG,
  CloudProvider,
  CloudProviderCard,
} from '../config/cloud-provider.config';
import {
  OnboardingCloudSchema,
  onboardingCloudSchema,
} from '../schema/onboarding-cloud.schema';
import { AwsConfigConnection } from './config-connection/aws-config-connection';
import { GcpConfigConnection } from './config-connection/gcp-config-connection';
import { MicrosoftAzureConfigConnection } from './config-connection/microsoft-azure-config-connection';

export function OnboardingStepTwo() {
  const form = useForm<OnboardingCloudSchema>({
    resolver: zodResolver(onboardingCloudSchema),
    defaultValues: {
      cloudProvider: CloudProvider.AWS,
    },
  });
  const { control } = form;

  const selectedProvider = useWatch({ control, name: 'cloudProvider' });

  return (
    <div className="space-y-4">
      <Form {...form}>
        <form>
          <Card>
            <CardHeader>
              <CardTitle>Choose Your Cloud Provider</CardTitle>
              <CardDescription>
                Select your primary cloud provider or try our sandbox mode
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FormField
                control={control}
                name="cloudProvider"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="grid grid-cols-1 gap-4 md:grid-cols-2"
                      >
                        {CLOUD_PROVIDER_CONFIG.LIST.map((provider) => (
                          <FormItem
                            key={provider.value}
                            className="flex flex-row items-center space-y-0 space-x-2"
                          >
                            <FormLabel className="grow">
                              <FormControl className="sr-only">
                                <RadioGroupItem
                                  value={provider.value}
                                  disabled={provider.isComingSoon}
                                />
                              </FormControl>
                              <CloudProviderCard
                                provider={provider.value}
                                selected={field.value === provider.value}
                                isComingSoon={provider.isComingSoon}
                              />
                            </FormLabel>
                          </FormItem>
                        ))}
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>
        </form>
      </Form>

      <AnimatePresence mode="wait">
        <motion.div
          key={selectedProvider}
          initial={{ opacity: 0, x: 30 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -30 }}
          transition={{
            duration: 0.2,
            ease: [0.4, 0.0, 0.2, 1],
          }}
        >
          <If condition={selectedProvider === CloudProvider.AWS}>
            <AwsConfigConnection ContinueButton={ContinueButton} />
          </If>
          <If condition={selectedProvider === CloudProvider.GCP}>
            <GcpConfigConnection ContinueButton={ContinueButton} />
          </If>
          <If condition={selectedProvider === CloudProvider.AZURE}>
            <MicrosoftAzureConfigConnection ContinueButton={ContinueButton} />
          </If>
        </motion.div>
      </AnimatePresence>
    </div>
  );
}

const ContinueButton = ({ isPending }: { isPending: boolean }) => (
  <div className="flex justify-end">
    <Button type="submit" className="gap-2" disabled={isPending}>
      Continue
      <If
        condition={isPending}
        fallback={<ArrowRightIcon className="size-4" />}
      >
        <Loader2Icon className="size-4 animate-spin" />
      </If>
    </Button>
  </div>
);
