import { ReactNode } from 'react';

import { useStepContext } from '@/components/providers/step-provider';
import { Card, CardContent } from '@/components/ui/card';
import { Autocomplete } from '@/components/ui/common/autocomplete';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { zodResolver } from '@hookform/resolvers/zod';
import { SubmitHandler, useForm } from 'react-hook-form';

import {
  CLOUD_PROVIDER_CONFIG,
  CloudProvider,
} from '../../config/cloud-provider.config';
import { onboardingQuery } from '../../hooks/onboarding.query';
import { useOnboardingContext } from '../../provider/onboarding-provider';
import {
  AwsConnectionSchema,
  awsConnectionSchema,
} from '../../schema/aws-connection.schema';
import { OnboardingConfigureConnectionCardHeader } from '../onboarding-configure-connection-card-header';

type Props = {
  ContinueButton: (props: { isPending: boolean }) => ReactNode;
};

export const AwsConfigConnection = ({ ContinueButton }: Props) => {
  const { awsRegions } = useOnboardingContext();
  const form = useForm<AwsConnectionSchema>({
    resolver: zodResolver(awsConnectionSchema),
  });

  const { control, handleSubmit } = form;

  const { mutate, isPending } = onboardingQuery.mutation.useConnectAws();
  const { goToNextStep } = useStepContext();

  const onSubmit: SubmitHandler<AwsConnectionSchema> = (data) => {
    mutate(data, {
      onSuccess: goToNextStep,
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <Card>
          <OnboardingConfigureConnectionCardHeader
            providerFullName={
              CLOUD_PROVIDER_CONFIG.CONFIG[CloudProvider.AWS].fullName
            }
          />
          <CardContent className="space-y-4">
            <FormField
              control={control}
              name="aws_access_key_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Access Key ID</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="AKIA..." />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="aws_secret_access_key"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Secret Access Key</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Enter your secret access key"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="aws_default_region"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Default Region</FormLabel>
                  <FormControl>
                    <Autocomplete
                      {...field}
                      name="region"
                      clearable={false}
                      options={awsRegions}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {ContinueButton({ isPending })}
      </form>
    </Form>
  );
};
