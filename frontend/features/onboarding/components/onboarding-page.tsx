'use client';

import { useEffect } from 'react';

import {
  useStepContext,
  withStepProvider,
} from '@/components/providers/step-provider';
import { If } from '@/components/ui/common/if';
import { Heading } from '@/components/ui/heading';
import { Progress } from '@/components/ui/progress';
import { useCallbackRef } from '@/hooks/use-callback-ref';
import { cn } from '@/lib/utils';
import { CheckCircle } from 'lucide-react';

import { OnboardingStepOne } from './onboarding-step-one';
import { OnboardingStepThree } from './onboarding-step-three';
import { OnboardingStepTwo } from './onboarding-step-two';

type Props = {
  defaultStep: number;
};

function OnboardingPageComponent({ defaultStep }: Props) {
  const { currentStep, setStep } = useStepContext();

  const setStepCallback = useCallbackRef(setStep);

  useEffect(() => {
    setStepCallback(defaultStep + 1);
  }, [defaultStep, setStepCallback]);

  const currentStepInfo = steps[currentStep - 1];

  // Calculate progress percentage
  const progressPercentage = (currentStep / steps.length) * 100;

  return (
    <div className="flex h-dvh items-center justify-center overflow-auto">
      <div className="flex h-full w-full max-w-5xl flex-col space-y-8 p-4">
        {/* Header */}
        <div className="text-center">
          <Heading level={1}>Welcome to CloudThinker</Heading>
          <p className="text-muted-foreground">
            Step {currentStep} of {steps.length}
          </p>
        </div>

        {/* Progress Bar */}
        <Progress value={progressPercentage} className="h-2 shrink-0" />

        {/* Step Indicator */}
        <div className="flex w-full justify-center space-x-4">
          {steps.map((step, index) => {
            const stepNumber = index + 1;
            const isActive = stepNumber === currentStep;
            const isCompleted = stepNumber < currentStep;

            return (
              <div key={index} className="align-self flex h-full flex-1">
                <div className="flex h-full flex-col items-center space-y-1 text-center">
                  <div
                    className={cn(
                      'flex size-10 items-center justify-center rounded-full border-2 transition-all duration-200',
                      'border-muted-foreground/30 bg-background text-muted-foreground',
                      isActive &&
                        'border-primary bg-primary text-primary-foreground',
                      isCompleted &&
                        'border-success bg-success text-success-foreground',
                    )}
                  >
                    <If
                      condition={isCompleted}
                      fallback={
                        <span className="text-sm font-medium">
                          {stepNumber}
                        </span>
                      }
                    >
                      <CheckCircle className="size-5" />
                    </If>
                  </div>

                  {/* Step Label */}
                  <p
                    className={cn(
                      'font-semibold',
                      isActive && 'text-primary',
                      isCompleted && 'text-success',
                    )}
                  >
                    {step.title}
                  </p>

                  <p className={cn('text-muted-foreground text-sm')}>
                    {step.description}
                  </p>
                </div>
              </div>
            );
          })}
        </div>

        <div className="text-center">
          <Heading level={2}>
            {currentStepInfo.pageTitle ?? currentStepInfo.title}
          </Heading>
          <p className="text-muted-foreground">
            {currentStepInfo.pageDescription ?? currentStepInfo.description}
          </p>
        </div>

        <div className="grow">
          {/* Step Content */}
          <div className="mb-8">{currentStepInfo.content}</div>
        </div>
      </div>
    </div>
  );
}

export const OnboardingPage = withStepProvider(OnboardingPageComponent, 3);

const steps = [
  {
    title: 'Create Your Workspace',
    description: 'Set up your AI-powered operations command center',
    content: <OnboardingStepOne />,
  },
  {
    title: 'Connect Cloud',
    description: 'Link your infrastructure or try sandbox',
    content: <OnboardingStepTwo />,
  },
  {
    title: 'First Operation',
    description: 'Experience AI-powered analysis',
    pageTitle: "Experience CloudThinker's Agentic Operations",
    pageDescription:
      'Watch CostOps, FinOps, and Security agents work autonomously',
    content: <OnboardingStepThree />,
  },
];
