import { PropsWithChildren } from 'react';

import { CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import { SecureInfoAlert } from './config-connection/secure-info-alert';

type Props = PropsWithChildren<{
  providerFullName: string;
}>;

export const OnboardingConfigureConnectionCardHeader = ({
  providerFullName,
}: Props) => {
  return (
    <CardHeader>
      <CardTitle>Configure Connection</CardTitle>
      <CardDescription>
        Enter your {providerFullName} credentials
      </CardDescription>
      <SecureInfoAlert />
    </CardHeader>
  );
};
