'use client';

import { PageSkeleton } from '@/components/ui/common/page';
import { builtinToolsQuery } from '../hooks/builtin-tools.query';
import { ToolCardContainer } from './tool-card-container';
import { Badge } from '@/components/ui/badge';

export function ToolList() {
  const { data: builtinTools, isLoading: isBuiltinToolsLoading } = builtinToolsQuery.query.useList();

  if (builtinTools) {
    return (
      <div className="flex flex-col h-full overflow-y-auto">
        <div className="mt-2 mb-3 space-y-3">
          <div className="flex flex-wrap items-center gap-3">
            <Badge variant="ghost-primary" className="px-3 py-1">
              {builtinTools.length} Tools
            </Badge>
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
          {builtinTools.map((tool) => (
            <ToolCardContainer
              key={tool.id}
              tool={tool}
              attachedAgents={[]}
            />
          ))}
        </div>
      </div>
    );
  }

  if (isBuiltinToolsLoading) {
    return <PageSkeleton />;
  }
}
