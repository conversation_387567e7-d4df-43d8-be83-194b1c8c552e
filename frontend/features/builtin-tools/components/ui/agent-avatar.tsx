'use client';

import React from 'react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface AgentAvatarProps {
  agent: {
    agentId: string;
    agentName: string;
  };
}

export function AgentAvatar({ agent }: AgentAvatarProps) {
  const agentNameInitial = agent.agentName
    .charAt(0)
    .toUpperCase();
  const agentFirstName = agent.agentName
    .split(' ')[0]
    .toLowerCase();
  const avatarPath = `/avatars/${agentFirstName}.webp`;

  return (
    <TooltipProvider delayDuration={100}>
      <Tooltip>
        <TooltipTrigger asChild>
          <Avatar className="border-background hover:border-primary/50 h-8 w-8 cursor-pointer border-2 transition-all hover:scale-105">
            <AvatarImage
              src={avatarPath}
              alt={agent.agentName}
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
              }}
            />
            <AvatarFallback className="bg-muted text-muted-foreground text-xs font-medium">
              {agentNameInitial}
            </AvatarFallback>
          </Avatar>
        </TooltipTrigger>
        <TooltipContent
          side="top"
          align="center"
          className="border-border bg-popover rounded-lg border px-3 py-2 shadow-lg"
          sideOffset={8}
        >
          <div className="flex items-center gap-2">
            <div className="h-2 w-2 animate-pulse rounded-full bg-green-500"></div>
            <p className="text-popover-foreground text-sm font-medium">
              {agent.agentName}
            </p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
