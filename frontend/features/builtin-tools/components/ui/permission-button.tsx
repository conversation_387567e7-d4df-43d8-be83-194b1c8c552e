'use client';

import React from 'react';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Loader2, Lock } from 'lucide-react';

interface PermissionButtonProps {
  requiresPermission: boolean;
  isPending: boolean;
  onTogglePermission: () => void;
  className?: string;
  disabled?: boolean;
}

export function PermissionButton({
  requiresPermission,
  isPending,
  onTogglePermission,
  className,
  disabled = false,
}: PermissionButtonProps) {
  return (
    <Button
      size="sm"
      variant={requiresPermission ? 'default' : 'outline'}
      onClick={onTogglePermission}
      disabled={isPending || disabled}
      className={cn(
        'h-7 px-2 text-xs transition-all',
        requiresPermission && 'bg-warning hover:bg-warning',
        className
      )}
    >
      {isPending ? (
        <Loader2 className="mr-1 h-3 w-3 animate-spin" />
      ) : (
        <Lock className="mr-1 h-3 w-3" />
      )}
      <span>
        {requiresPermission
          ? 'Remove Approval'
          : 'Require Approval'}
      </span>
    </Button>
  );
}
