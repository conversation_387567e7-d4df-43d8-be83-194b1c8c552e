'use client';

import React from 'react';

import { SchemaWorkspaceBuiltInToolResponse } from '@/openapi-ts/gens';

import { builtinToolsQuery } from '../hooks/builtin-tools.query';
import { ToolCard, ToolCardUIProps } from './tool-card';

export interface ToolCardContainerProps {
  tool: SchemaWorkspaceBuiltInToolResponse;
  attachedAgents?: Array<{ agentId: string; agentName: string }>;
}

export function ToolCardContainer({
  tool,
  attachedAgents = [],
}: ToolCardContainerProps) {
  const {
    isPermissionPending,
    requiresPermission,
    displayName,
    description,
    handleTogglePermission,
  } = builtinToolsQuery.query.useToolCard(tool);

  const uiProps: ToolCardUIProps = {
    displayName,
    description,
    requiresPermission,
    attachedAgents,
    isPermissionPending,
    onTogglePermission: handleTogglePermission,
  };

  return <ToolCard {...uiProps} />;
}
