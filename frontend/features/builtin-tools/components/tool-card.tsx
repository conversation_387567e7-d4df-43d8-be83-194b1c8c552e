'use client';

import React from 'react';

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { cn } from '@/lib/utils';

import { AgentAvatar } from './ui/agent-avatar';
import { PermissionButton } from './ui/permission-button';

export interface ToolCardUIProps {
  displayName: string;
  description: string;
  requiresPermission: boolean;
  attachedAgents?: Array<{ agentId: string; agentName: string }>;
  isPermissionPending: boolean;
  onTogglePermission: () => void;
  onViewDetails?: () => void;
}

export function ToolCard({
  displayName,
  description,
  requiresPermission,
  attachedAgents = [],
  isPermissionPending,
  onTogglePermission,
}: ToolCardUIProps) {
  const cardBorderStyle = cn(
    'group hover:shadow-md overflow-hidden transition-all duration-200 border border-border/50',
    'bg-card/50 hover:bg-card relative',
    requiresPermission
      ? 'hover:border-amber-200 dark:hover:border-amber-800'
      : 'hover:border-primary/30 dark:hover:border-primary/30',
  );

  return (
    <Card className={cardBorderStyle}>
      <CardHeader className="p-0">
        <div className="flex items-center justify-between px-4 py-3">
          <div>
            <h3 className="font-medium text-sm leading-tight text-foreground group-hover:text-primary transition-colors">
              {displayName}
            </h3>
          </div>
          <div className="flex items-center gap-2">
            {requiresPermission && (
              <div className="flex-shrink-0">
                <div className="w-2 h-2 rounded-full bg-amber-400 dark:bg-amber-500" title="Requires permission" />
              </div>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-3 px-4 pb-3 pt-0">
        <div className="text-xs text-muted-foreground line-clamp-3 leading-relaxed">
          {description}
        </div>

        {attachedAgents.length > 0 && (
          <div className="flex items-center gap-1.5">
            {attachedAgents?.map((agent) => (
              <AgentAvatar key={agent.agentId} agent={agent} />
            ))}
          </div>
        )}

        <div className="flex items-center justify-end pt-2">
          <PermissionButton
            requiresPermission={requiresPermission}
            isPending={isPermissionPending}
            onTogglePermission={onTogglePermission}
          />
        </div>
      </CardContent>
    </Card>
  );
}
