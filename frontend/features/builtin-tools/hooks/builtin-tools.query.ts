import { createQuery<PERSON>eys } from '@lukemorales/query-key-factory';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import { builtinToolsApi } from '../services/builtin-tools.api';
import { SchemaWorkspaceBuiltInToolResponse } from '@/openapi-ts/gens';
import { useState } from 'react';

const builtinToolsQueryKeys = createQueryKeys('builtin-tools', {
  list: {
    queryKey: null,
    queryFn: builtinToolsApi.list,
  },
});

const useList = () => {
  return useQuery(builtinToolsQueryKeys.list);
}

const useUpdatePermission = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      toolId,
      requiredPermission,
    }: {
      toolId: string;
      requiredPermission: boolean;
    }) => {
      return await builtinToolsApi.detail(toolId).updatePermission({ requiredPermission });
    },
    onSuccess: (_, { requiredPermission }) => {
      queryClient.invalidateQueries({ queryKey: builtinToolsQueryKeys.list.queryKey });
      toast.success(
        requiredPermission
          ? 'Permission requirement enabled'
          : 'Permission requirement disabled',
      );
    },
    onError: () => {
      toast.error('Failed to update permission requirement');
    },
  });
}

const useToolCard = (tool: SchemaWorkspaceBuiltInToolResponse) => {
  const { mutate: togglePermission, isPending: isPermissionPending } = builtinToolsQuery.mutation.useUpdatePermission();
  const [requiresPermission, setRequiresPermission] = useState(tool.required_permission);

  const displayName = tool.builtin_tool.display_name;
  const description = tool.builtin_tool.description;

  const handleTogglePermission = () => {
    if (!tool.id) return;

    const newRequiresPermission = !requiresPermission;
    setRequiresPermission(newRequiresPermission);

    togglePermission({
      toolId: tool.id,
      requiredPermission: newRequiresPermission,
    }, {
      onError: () => {
        setRequiresPermission(!newRequiresPermission); // Revert on error
      }
    });
  };

  return {
    // State
    isPermissionPending,
    requiresPermission,

    // Display data
    displayName,
    description,

    // Actions
    handleTogglePermission,
  };
}

export const builtinToolsQuery = {
  query: {
    useList,
    useToolCard,
  },
  mutation: {
    useUpdatePermission,
  },
};
