'use client';

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SchemaCloudSyncConfigPublic, SchemaConnectionPublic } from '@/openapi-ts/gens';
import { 
  Cloud, 
  MoreVertical, 
  Edit, 
  Trash2, 
  Play, 
  Clock,
  Database,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface CloudSyncConfigCardProps {
  readonly config: SchemaCloudSyncConfigPublic;
  readonly connection?: SchemaConnectionPublic | null;
  readonly isUpdating?: boolean;
  readonly isDeleting?: boolean;
  readonly isSyncing?: boolean;
  readonly className?: string;

  // Action handlers
  readonly onEdit?: (config: SchemaCloudSyncConfigPublic) => void;
  readonly onDelete?: (config: SchemaCloudSyncConfigPublic) => void;
  readonly onToggleEnabled?: (config: SchemaCloudSyncConfigPublic, enabled: boolean) => void;
  readonly onTriggerSync?: (config: SchemaCloudSyncConfigPublic) => void;
}

export function CloudSyncConfigCard({
  config,
  connection,
  isUpdating = false,
  isDeleting = false,
  isSyncing = false,
  className,
  onEdit,
  onDelete,
  onToggleEnabled,
  onTriggerSync,
}: CloudSyncConfigCardProps) {
  const isLoading = isUpdating || isDeleting || isSyncing;

  return (
    <Card className={cn("relative flex flex-col h-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-muted">
              <Cloud className="h-5 w-5" />
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <Badge variant="outline" className="text-xs">
                  {connection?.prefix?.toUpperCase() || 'Unknown'}
                </Badge>
                <Badge 
                  variant={config.is_enabled ? "default" : "secondary"}
                  className="text-xs"
                >
                  {config.is_enabled ? (
                    <>
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Enabled
                    </>
                  ) : (
                    <>
                      <XCircle className="h-3 w-3 mr-1" />
                      Disabled
                    </>
                  )}
                </Badge>
              </div>
              <h3 className="font-medium text-sm truncate">
                {connection?.name || 'Unknown Connection'}
              </h3>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit?.(config)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              {config.is_enabled && (
                <DropdownMenuItem onClick={() => onTriggerSync?.(config)}>
                  <Play className="h-4 w-4 mr-2" />
                  Trigger Sync
                </DropdownMenuItem>
              )}
              <DropdownMenuItem 
                onClick={() => onDelete?.(config)}
                className="text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="flex flex-col flex-grow pt-0">
        {/* Configuration Details */}
        <div className="space-y-3 mb-4">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground flex items-center gap-1">
              <Clock className="h-3 w-3" />
              Refresh Interval
            </span>
            <span className="font-medium">{config.refresh_interval}m</span>
          </div>
          
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground flex items-center gap-1">
              <Database className="h-3 w-3" />
              Resource Types
            </span>
            <span className="font-medium">{config.selected_resources?.length || 0}</span>
          </div>

          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Include Stopped</span>
            <Badge variant={config.include_stopped_resources ? "default" : "secondary"} className="text-xs">
              {config.include_stopped_resources ? 'Yes' : 'No'}
            </Badge>
          </div>
        </div>

        {/* Resource Types List */}
        {config.selected_resources && config.selected_resources.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
              Selected Resources
            </h4>
            <div className="flex flex-wrap gap-1">
              {config.selected_resources.slice(0, 6).map((resource) => (
                <Badge key={resource} variant="outline" className="text-xs">
                  {resource}
                </Badge>
              ))}
              {config.selected_resources.length > 6 && (
                <Badge variant="outline" className="text-xs">
                  +{config.selected_resources.length - 6} more
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Bottom Action Section */}
        <div className="flex items-center justify-between pt-4 mt-auto border-t">
          <span className="text-xs text-muted-foreground">
            Status
          </span>
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">
              {config.is_enabled ? 'Enabled' : 'Disabled'}
            </span>
            <Switch
              size="sm"
              checked={config.is_enabled}
              onCheckedChange={(checked) => onToggleEnabled?.(config, checked)}
              disabled={isLoading}
            />
          </div>
        </div>
      </CardContent>

      {/* Loading overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-background/50 flex items-center justify-center rounded-lg">
          <div className="flex items-center gap-2 text-sm">
            <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full" />
            {isSyncing ? 'Syncing...' : isDeleting ? 'Deleting...' : 'Updating...'}
          </div>
        </div>
      )}
    </Card>
  );
}