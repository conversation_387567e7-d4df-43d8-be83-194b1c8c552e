import { useEffect, useRef } from 'react';

import { useRouter } from 'next/navigation';

// Utility function to build URL with parameters
const buildUrlWithParams = (
  pathname: string,
  params: Record<string, string>,
) => {
  const urlParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value) {
      urlParams.append(key, value);
    }
  });
  const queryString = urlParams.toString();
  return queryString ? `${pathname}?${queryString}` : pathname;
};

interface UrlManagerProps {
  selectedConversation: string | null;
  conversationId?: string;
  mounted: boolean;
  setSelectedConversation: (id: string | null) => void;
  setInterruptConfirmation: (confirmation: any) => void;
}

export const useUrlManager = ({
  selectedConversation,
  conversationId,
  mounted,
  setSelectedConversation,
  setInterruptConfirmation,
}: UrlManagerProps) => {
  const router = useRouter();

  // Use refs to store the latest callback functions
  const setSelectedConversationRef = useRef(setSelectedConversation);
  const setInterruptConfirmationRef = useRef(setInterruptConfirmation);

  // Update refs when the callbacks change
  useEffect(() => {
    setSelectedConversationRef.current = setSelectedConversation;
    setInterruptConfirmationRef.current = setInterruptConfirmation;
  }, [setSelectedConversation, setInterruptConfirmation]);

  // Handle conversationId changes
  useEffect(() => {
    if (conversationId) {
      setSelectedConversationRef.current(conversationId);
      setInterruptConfirmationRef.current(null);
    }
  }, [conversationId]);

  // Handle URL updates
  useEffect(() => {
    if (selectedConversation && mounted) {
      const newUrl = buildUrlWithParams(window.location.pathname, {
        conversation: selectedConversation,
        // Don't preserve initialMessage when updating conversation URL
        // It should only be present initially and removed after message is sent
      });
      router.replace(newUrl, { scroll: false });
    }
  }, [selectedConversation, mounted, router]);
};
