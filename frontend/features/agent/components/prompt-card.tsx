import { FC, createElement } from 'react';

import { cn } from '@/lib/utils';

export type Prompt = {
  provider: 'aws' | 'azure' | 'gcp';
  icon: FC<{ className?: string }>;
  title: string;
  question: string;
  className?: Partial<{
    gradient: string;
    icon: string;
  }>;
};

type PromptCardProps = {
  prompt: Prompt;
  onPromptClick: (prompt: Prompt) => void;
  className?: string;
};

export const PromptCard = ({
  prompt,
  onPromptClick,
  className,
}: PromptCardProps) => {
  return (
    <button
      onClick={() => onPromptClick(prompt)}
      className={cn(
        'group relative overflow-hidden rounded-lg border p-4 text-left transition-all duration-300',
        'hover:bg-primary/5 hover:-translate-y-1 hover:shadow-xl',
        'from-card/80 to-card/40 bg-gradient-to-br backdrop-blur-sm',
        'active:translate-y-0 active:scale-[0.98]',
        'focus-visible:ring-primary/50 focus-visible:ring-2 focus-visible:outline-none',
        className,
      )}
      style={{
        animationDelay: '100ms',
      }}
    >
      {/* Animated gradient background */}
      <div
        className={cn(
          'absolute inset-0 opacity-0 transition-all duration-500',
          'group-hover:opacity-100',
          'bg-gradient-to-br',
          prompt.className?.gradient,
        )}
      />

      {/* Content */}
      <div className="relative z-10 flex h-full flex-col justify-between gap-3">
        <div className="flex items-center gap-2">
          <div className="bg-background/80 group-hover:bg-background/90 flex aspect-square size-9 shrink-0 items-center justify-center rounded-lg backdrop-blur-sm transition-colors">
            {createElement(prompt.icon, {
              className: cn('size-7', prompt.className?.icon),
            })}
          </div>
          <h3 className="truncate font-semibold transition-colors">
            {prompt.title}
          </h3>
        </div>
        <p className="text-muted-foreground group-hover:text-foreground/80 grow text-sm leading-relaxed transition-colors">
          {prompt.question}
        </p>
      </div>
    </button>
  );
};
