'use client';

import { AgentCard } from './agent-card';
import { LoadingSkeleton } from './loading-skeleton';
import { cn } from '@/lib/utils';
import { AgentType } from '@/openapi-ts/gens';
import { filter, range } from 'lodash';

import { agentQuery } from '../hooks/agent.query';

type Props = {
  className?: string;
};

export const AgentCardList = ({ className }: Props) => {
  const { data, isLoading } = agentQuery.query.useList();

  if (data) {
    const conversationalAgents = filter(data.data, {
      type: AgentType.conversation_agent,
    });

    return (
      <div className={cn('flex flex-wrap gap-4', className)}>
        {conversationalAgents.map((item) => (
          <AgentCard className="max-w-md" key={item.id} item={item} />
        ))}
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {range(6).map((i) => (
          <LoadingSkeleton key={i} />
        ))}
      </div>
    );
  }

  // Error
  return <div>Error</div>;
};
