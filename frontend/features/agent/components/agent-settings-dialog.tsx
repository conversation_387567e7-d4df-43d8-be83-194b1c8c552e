'use client';

import { useState } from 'react';

import { Checkbox } from '@/components/ui/checkbox';
import { If } from '@/components/ui/common/if';
import { Label } from '@/components/ui/label';
import { SidebarDialog, SidebarTab } from '@/components/ui/sidebar-dialog';
import { Textarea } from '@/components/ui/textarea';
import { agentQuery } from '@/features/agent/hooks/agent.query';
import { useAgentContext } from '@/features/agent/provider/agent-provider';
import { cn } from '@/lib/utils';
import {
  SchemaAgentBuiltInToolPublic,
  SchemaAgentPublic,
} from '@/openapi-ts/gens';
import { Link, MessageSquare, Shield } from 'lucide-react';
import { toast } from 'sonner';

import { AgentConnectionsTab } from './agent-connections-tab';

interface AgentSettingsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  agent: SchemaAgentPublic;
}

const TABS_CONFIG: SidebarTab[] = [
  {
    id: 'tools',
    name: 'Builtin Tools',
    icon: Shield,
    description: "Manage agent's builtin tools.",
  },
  {
    id: 'connections',
    name: 'Connections',
    icon: Link,
    description: "Manage agent's available connections.",
  },
  {
    id: 'instructions',
    name: 'Instructions',
    icon: MessageSquare,
    description: "Define the agent's core behavior and guidelines.",
  },
];

export function AgentSettingsDialog({
  isOpen,
  onClose,
  agent,
}: AgentSettingsDialogProps) {
  const [instructions, setInstructions] = useState<string>(
    agent.instructions || '',
  );
  const [activeTab, setActiveTab] = useState<string>(TABS_CONFIG[0].id);

  const { agentsBuiltinTools } = useAgentContext();

  const originalAgentBuiltinTools: SchemaAgentBuiltInToolPublic[] =
    agentsBuiltinTools?.agents_builtin_tools?.find(
      (agentTool) => agentTool.agent_id === agent.id,
    )?.tools || [];

  const [modifiedTools, setModifiedTools] = useState<
    SchemaAgentBuiltInToolPublic[]
  >(originalAgentBuiltinTools);

  const currentAgentBuiltinTools =
    modifiedTools.length > 0 ? modifiedTools : originalAgentBuiltinTools;

  const { mutate: updateInstructions, isPending: isUpdatingInstructions } =
    agentQuery.mutation.useUpdateInstructions(agent.id);
  const {
    mutate: updateAgentBuiltinTools,
    isPending: isUpdatingAgentBuiltinTools,
  } = agentQuery.mutation.useUpdateAgentBuiltinTools(agent.id);
  const { isPending: isCreatingConnection } =
    agentQuery.mutation.useCreateConnection(agent.id);
  const { isPending: isDeletingConnection } =
    agentQuery.mutation.useDeleteConnection(agent.id);

  const handleSave = async () => {
    try {
      if (activeTab === 'instructions') {
        await updateInstructions(instructions);
        toast.success('Agent instructions updated successfully');
      } else if (activeTab === 'tools') {
        await updateAgentBuiltinTools(
          currentAgentBuiltinTools
            .filter((tool) => {
              const originalTool = originalAgentBuiltinTools.find(
                (t) => t.id === tool.id,
              );
              return originalTool?.is_active !== tool.is_active;
            })
            .map((tool) => ({
              workspace_builtin_tool_id: tool.id,
              is_active: tool.is_active,
            })),
        );
        toast.success('Agent tools updated successfully');
      } else if (activeTab === 'connections') {
        // Connections are handled by the AgentConnectionsTab component directly
        toast.success('Connections updated successfully');
      }
    } catch {
      toast.error('Failed to update agent settings');
    }
  };

  const handleToolToggle = (toolName: string, checked: boolean) => {
    const updatedTools = currentAgentBuiltinTools.map((tool) =>
      tool.name === toolName ? { ...tool, is_active: checked } : tool,
    );

    setModifiedTools(updatedTools);
  };

  const renderToolsContent = () => (
    <div className="flex flex-1 flex-col overflow-hidden">
      <div className="flex-shrink-0 space-y-2">
        <p className="text-muted-foreground text-sm">
          Select the built-in tools this agent can use
        </p>
      </div>

      <div className="custom-scrollbar flex-1 overflow-y-auto pt-4">
        <If
          condition={currentAgentBuiltinTools.length > 0}
          fallback={
            <div className="flex flex-1 items-center justify-center">
              <div className="text-center">
                <Shield className="text-muted-foreground/50 mx-auto h-12 w-12" />
                <p className="text-muted-foreground mt-2 text-sm">
                  No built-in tools available
                </p>
              </div>
            </div>
          }
        >
          <div className="grid auto-rows-max grid-cols-1 gap-3 sm:grid-cols-2">
            {currentAgentBuiltinTools.map((tool) => (
              <div
                key={tool.name}
                className={cn(
                  'group flex items-center gap-3 rounded-lg border p-3',
                  'hover:bg-muted/50 transition-colors',
                  'focus-within:ring-primary/20 focus-within:ring-2',
                )}
              >
                <Checkbox
                  id={`tool-${tool.name}`}
                  checked={tool.is_active}
                  onCheckedChange={(checked) =>
                    handleToolToggle(tool.name, checked as boolean)
                  }
                  className="shrink-0"
                />
                <Label
                  htmlFor={`tool-${tool.name}`}
                  className="flex-1 cursor-pointer text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  title={tool.display_name || tool.name}
                >
                  {tool.display_name || tool.name}
                </Label>
              </div>
            ))}
          </div>
        </If>
      </div>
    </div>
  );

  const renderInstructionsContent = () => (
    <div className="flex flex-1 flex-col">
      <div className="flex-shrink-0 space-y-2">
        <p className="text-muted-foreground text-sm">
          Configure the instructions that guide this agent&apos;s behavior
        </p>
      </div>

      <div className="flex-1 pt-4">
        <Textarea
          value={instructions}
          onChange={(e) => setInstructions(e.target.value)}
          placeholder="Enter instructions for the agent..."
          className={cn(
            'w-full resize-none overflow-y-auto',
            'max-h-[600px] min-h-[300px]',
            'focus:ring-primary/20 focus:ring-2',
          )}
        />
      </div>
    </div>
  );

  return (
    <SidebarDialog
      isOpen={isOpen}
      onClose={onClose}
      title={`Agent Settings - ${agent.title}`}
      description="Configure the tools and instructions for this agent."
      tabs={TABS_CONFIG}
      activeTab={activeTab}
      onTabChange={setActiveTab}
      isSaving={
        isUpdatingInstructions ||
        isUpdatingAgentBuiltinTools ||
        isCreatingConnection ||
        isDeletingConnection
      }
      onSave={activeTab === 'connections' ? undefined : handleSave}
    >
      <If condition={activeTab === 'tools'}>{renderToolsContent()}</If>
      <If condition={activeTab === 'connections'}>
        <AgentConnectionsTab agent={agent} />
      </If>
      <If condition={activeTab === 'instructions'}>
        {renderInstructionsContent()}
      </If>
    </SidebarDialog>
  );
}
