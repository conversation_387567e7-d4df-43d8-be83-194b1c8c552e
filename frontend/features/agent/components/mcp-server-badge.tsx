import { memo } from 'react';

import { Badge } from '@/components/ui/badge';
import { DisplayConnectionInfo } from '@/features/connection/hooks/connection.query';
import { Server } from 'lucide-react';

interface MCPServerBadgeProps {
  server: DisplayConnectionInfo;
}

export const MCPServerBadge = memo(({ server }: MCPServerBadgeProps) => {
  return (
    <Badge
      variant={
        server.is_active
          ? server.status === 'connected'
            ? 'success'
            : 'destructive'
          : 'secondary'
      }
      className="inline-flex h-fit w-fit items-center gap-1 px-1 whitespace-nowrap"
    >
      <Server className="h-3 w-3.5" />
      <span>{server.name}</span>
    </Badge>
  );
});
