import Image from 'next/image';

import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from '@/components/ui/card';
import { useAgentContext } from '@/features/agent/provider/agent-provider';
import { cn } from '@/lib/utils';
import {
  AgentType,
  ConnectionType,
  SchemaAgentPublic,
} from '@/openapi-ts/gens';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { If } from '@/components/ui/common/if';

import { AgentActions } from './agent-actions';

interface AgentCardProps {
  item: SchemaAgentPublic;
  className?: string;
}

export const AgentCard = ({ item, className }: AgentCardProps) => {
  const { agentsBuiltinTools, agentsConnections } = useAgentContext();

  const agentTools =
    agentsBuiltinTools?.agents_builtin_tools.find(
      (agent) => agent.agent_id === item.id,
    )?.tools || [];
  const agentConnections =
    agentsConnections?.agents_connections.find(
      (agent) => agent.agent_id === item.id,
    )?.connections || [];

  // Filter out CLOUD connections
  const filteredConnections = agentConnections.filter(
    (conn) => conn.type !== ConnectionType.cloud,
  );
  const hasConnections = filteredConnections.length > 0;

  const agentName = item.alias;
  const avatarPath = `/avatars/${agentName}.webp`;
  const isGroupChat = item.type === AgentType.autonomous_agent;

  return (
    <Card
      className={cn('flex flex-col transition-all duration-200', className)}
    >
      <CardHeader className="flex flex-row flex-wrap items-center justify-between gap-2">
        <div className="flex items-center gap-3">
          <div className="shrink-0">
            <If
              condition={!isGroupChat}
              fallback={
                <Image
                  src={avatarPath}
                  alt={item.title}
                  className="h-6 w-auto"
                  width={40}
                  height={0}
                />
              }
            >
              <Avatar className="size-8">
                <AvatarImage src={avatarPath} />
                <AvatarFallback>{item.alias}</AvatarFallback>
              </Avatar>
            </If>
          </div>
          <div className="min-w-0 flex-1">
            <h3 className="truncate text-lg font-semibold tracking-tight">
              {item.title}
            </h3>
          </div>
        </div>

        <If condition={!isGroupChat}>
          <AgentActions agent={item} />
        </If>
      </CardHeader>

      <CardContent className="flex grow flex-col justify-between">
        <p className="text-muted-foreground line-clamp-3 text-sm">
          {item.instructions}
        </p>

        <div className="space-y-4 pt-5">
          {/* Built-in Tools */}
          <If condition={agentTools.length > 0}>
            <div className="w-full">
              <div className="mb-2">
                <span className="text-muted-foreground text-xs font-medium">
                  Built-in Tools
                </span>
              </div>
              <div className="flex flex-wrap gap-2">
                {agentTools
                  .filter((tool) => tool.is_active)
                  .map((tool) => (
                    <Badge
                      key={tool.name}
                      variant="ghost-primary"
                      className="inline-flex h-fit w-fit max-w-[120px] items-center"
                    >
                      <span className="block max-w-full truncate">
                        {tool.display_name || tool.name}
                      </span>
                    </Badge>
                  ))}
              </div>
            </div>
          </If>

          {/* Connections */}
          <If condition={hasConnections}>
            <div className="w-full">
              <div className="mb-2">
                <span className="text-muted-foreground text-xs font-medium">
                  Connections
                </span>
              </div>
              <div className="flex flex-wrap gap-2">
                {filteredConnections.map((connection) => (
                  <Badge
                    key={connection.id}
                    variant={
                      connection.type === ConnectionType.builtin
                        ? 'ghost-info'
                        : 'ghost-success'
                    }
                    className="inline-flex h-fit w-fit max-w-[120px] items-center"
                  >
                    <span className="block max-w-full truncate">
                      {connection.name}
                    </span>
                  </Badge>
                ))}
              </div>
            </div>
          </If>
        </div>
      </CardContent>
    </Card>
  );
};
