import { PromptCard } from '@/features/agent/components/prompt-card';
import { DEFAULT_PROMPT_LIST } from '@/features/agent/data/prompt.constants';
import { RESOURCE_TYPE_CONFIG } from '@/features/resource/config/resource-type.config';
import { AWSResourceType } from '@/features/resource/config/resource-type.config';

interface AgentExampleQuestionsProps {
  onQuestionClick: (question: string) => void;
  resourceType?: AWSResourceType;
}

export function AgentExampleQuestions({
  onQuestionClick,
  resourceType,
}: AgentExampleQuestionsProps) {
  // Get questions based on resource type or use defaults
  const prompts = resourceType
    ? RESOURCE_TYPE_CONFIG.CONFIG[resourceType]?.prompts
    : DEFAULT_PROMPT_LIST;

  const title = resourceType
    ? `${RESOURCE_TYPE_CONFIG.getLabel(resourceType)} Optimization Questions`
    : 'Explore Cloud Cost Optimization';

  return (
    <div className="@container size-full py-6">
      <div className="flex flex-col items-center px-4 md:px-8 lg:px-12">
        <h3 className="text-foreground/90 mb-3 text-center text-lg font-medium">
          {title}
        </h3>
        <div className="animate-fade-in grid grow grid-cols-1 gap-3 overflow-y-auto @2xl:grid-cols-2 @2xl:gap-4">
          {prompts.map((prompt) => (
            <PromptCard
              key={prompt.title}
              prompt={prompt}
              onPromptClick={(prompt) => onQuestionClick(prompt.question)}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
