'use client';

import { useState } from 'react';

import { AgentPublic } from '@/client';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Settings } from 'lucide-react';
import { agentQuery } from '@/features/agent/hooks/agent.query';
import { toast } from 'sonner';

import { AgentSettingsDialog } from './agent-settings-dialog';

interface AgentActionsProps {
  agent: AgentPublic;
}

export function AgentActions({ agent }: AgentActionsProps) {
  const toggleActiveMutation = agentQuery.mutation.useUpdateStatus(agent.id);
  const [isSettingsDialogOpen, setIsSettingsDialogOpen] = useState(false);

  const handleToggleActive = (isActive: boolean) => {
    toggleActiveMutation.mutate(isActive, {
      onSuccess: () => {
        const status = isActive ? 'activated' : 'deactivated';
        toast.success(`Agent ${status} successfully`);
      },
      onError: () => {
        const action = isActive ? 'activate' : 'deactivate';
        toast.error(`Failed to ${action} agent`);
      },
    });
  };

  const handleOpenSettings = () => {
    setIsSettingsDialogOpen(true);
  };

  const handleCloseSettingsDialog = () => {
    setIsSettingsDialogOpen(false);
  };

  return (
    <>
      <AgentSettingsDialog
        isOpen={isSettingsDialogOpen}
        onClose={handleCloseSettingsDialog}
        agent={agent}
      />

      <div className="flex shrink-0 items-center gap-2">
        <Switch
          checked={agent.is_active ?? false}
          onCheckedChange={handleToggleActive}
          disabled={toggleActiveMutation.isPending}
          aria-label={agent.is_active ? 'Deactivate agent' : 'Activate agent'}
        />
        <Button
          variant="ghost"
          size="icon"
          onClick={handleOpenSettings}
          disabled={isSettingsDialogOpen || toggleActiveMutation.isPending}
          aria-label={`Open settings for ${agent.title}`}
        >
          <Settings className="size-4" />
        </Button>
      </div>
    </>
  );
}

AgentActions.displayName = 'AgentActions';
