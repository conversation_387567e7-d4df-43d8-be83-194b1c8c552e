'use client';

import { createContext, useContext } from 'react';

import { agentQuery } from '@/features/agent/hooks/agent.query';

type AgentContextValue = {
  agentsBuiltinTools: ReturnType<
    typeof agentQuery.query.useAgentsBuiltinTools
  >['data'];
  agentsConnections: ReturnType<typeof agentQuery.query.useConnections>['data'];
};

const AgentContext = createContext<AgentContextValue | null>(null);

export const AgentProvider = ({ children }: { children: React.ReactNode }) => {
  const { data: agentsBuiltinTools } = agentQuery.query.useAgentsBuiltinTools();
  const { data: agentsConnections } = agentQuery.query.useConnections();

  return (
    <AgentContext.Provider value={{ agentsBuiltinTools, agentsConnections }}>
      {children}
    </AgentContext.Provider>
  );
};

export const useAgentContext = () => {
  const context = useContext(AgentContext);
  if (!context) {
    throw new Error('useAgentContext must be used within an AgentProvider');
  }
  return context;
};
