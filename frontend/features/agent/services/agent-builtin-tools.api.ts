import { api, fetchData } from '@/openapi-ts/openapi-fetch';
import { SchemaAgentBuiltInToolUpdate } from '@/openapi-ts/gens';

export const agentBuiltinToolsApi = {
  list: () =>
    fetchData(
      api.GET('/api/v1/agents-builtin-tools/'),
    ),

  update: (agent_id: string, updates: SchemaAgentBuiltInToolUpdate[]) =>
    fetchData(
      api.PATCH('/api/v1/agents-builtin-tools/{agent_id}', {
        params: {
          path: {
            agent_id,
          },
        },
        body: {
          agent_builtin_tools: updates,
        },
      }),
    ),
};
