import { AgentType } from '@/openapi-ts/gens';
import { api, fetchData } from '@/openapi-ts/openapi-fetch';
import { find } from 'lodash';

export const agentApi = {
  list: () => fetchData(api.GET('/api/v1/agents/')),

  updateStatus: (id: string, isActive: boolean) =>
    fetchData(
      api.PATCH('/api/v1/agents/{agent_id}/status', {
        params: {
          path: {
            agent_id: id,
          },
        },
        body: {
          agent_status: isActive,
        },
      }),
    ),

  updateInstructions: (id: string, instructions: string) =>
    fetchData(
      api.PATCH('/api/v1/agents/{agent_id}/instructions', {
        params: {
          path: { agent_id: id },
        },
        body: {
          instructions,
        },
      }),
    ),
};

export const getAutonomousAgentId = async () => {
  const agentId = find((await agentApi.list()).data, {
    type: AgentType.autonomous_agent,
  })?.id;

  if (!agentId) {
    throw new Error('No agent found');
  }

  return agentId;
};
