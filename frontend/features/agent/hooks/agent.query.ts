import { <PERSON><PERSON><PERSON><PERSON> } from '@/components/utils/cache-key';
import { SchemaAgentBuiltInToolUpdate } from '@/openapi-ts/gens';
import { createQueryKeys } from '@lukemorales/query-key-factory';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import { agentBuiltinToolsApi } from '../services/agent-builtin-tools.api';
import { agentConnectionsApi } from '../services/agent-connections';
import { agentApi } from '../services/agent.api';

const agentQueryKeys = createQueryKeys('agents', {
  list: {
    queryKey: null,
    queryFn: agentApi.list,
  },
  builtinTools: {
    queryKey: null,
    queryFn: agentBuiltinToolsApi.list,
  },
  connections: {
    queryKey: null,
    queryFn: agentConnectionsApi.list,
  },
});

const useList = (options?: {
  limit?: number;
  skip?: number;
  enabled?: boolean;
  refetchOnWindowFocus?: boolean;
  refetchInterval?: number | false;
}) => {
  // Cache time constants (in milliseconds)
  const STALE_TIME = 5 * 60 * 1000; // 5 minutes
  const CACHE_TIME = 10 * 60 * 1000; // 10 minutes

  // Create a query key that includes the limit and skip parameters
  // This ensures we have a consistent cache key for the same parameters
  const queryKey =
    options?.limit || options?.skip
      ? [CacheKey.AGENTS, { limit: options.limit, skip: options.skip }]
      : [CacheKey.AGENTS];

  return useQuery({
    queryKey,
    queryFn: () => agentApi.list(),
    staleTime: STALE_TIME, // Data will be considered fresh for 5 minutes
    gcTime: CACHE_TIME, // Cache will be garbage collected after 10 minutes
    // Always enable the query unless explicitly disabled
    // This ensures the data is fetched and cached on the landing page
    enabled: options?.enabled !== false,
    refetchOnWindowFocus: options?.refetchOnWindowFocus ?? false, // Don't refetch on window focus by default
    refetchInterval: options?.refetchInterval ?? false, // Don't auto-refetch by default
  });
};

const useAgentsBuiltinTools = () => {
  return useQuery(agentQueryKeys.builtinTools);
};

const useConnections = () => {
  return useQuery(agentQueryKeys.connections);
};

const useUpdateStatus = (id: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (isActive: boolean) => agentApi.updateStatus(id, isActive),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: agentQueryKeys.list.queryKey });
      // Also invalidate the agents cache so agent-mention.tsx gets updated
      queryClient.invalidateQueries({ queryKey: [CacheKey.AGENTS] });
    },
    onError: () => {
      toast.error('Failed to update agent status');
    },
  });
};

const useUpdateInstructions = (id: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (instructions: string) =>
      agentApi.updateInstructions(id, instructions),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: agentQueryKeys.list.queryKey });
      // Also invalidate the agents cache so agent-mention.tsx gets updated
      queryClient.invalidateQueries({ queryKey: [CacheKey.AGENTS] });
    },
    onError: () => {
      toast.error('Failed to update agent instructions');
    },
  });
};

const useUpdateAgentBuiltinTools = (id: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (updates: SchemaAgentBuiltInToolUpdate[]) =>
      agentBuiltinToolsApi.update(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: agentQueryKeys.builtinTools.queryKey,
      });
      // Also invalidate the agents cache so agent-mention.tsx gets updated
      queryClient.invalidateQueries({ queryKey: [CacheKey.AGENTS] });
    },
    onError: () => {
      toast.error('Failed to update agent builtin tools');
    },
  });
};

const useCreateConnection = (id: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (connection_id: string) =>
      agentConnectionsApi.create(id, connection_id),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: agentQueryKeys.connections.queryKey,
      });
      toast.success('Connection added successfully');
    },
    onError: () => {
      toast.error('Failed to add connection');
    },
  });
};

const useDeleteConnection = (id: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (connection_id: string) =>
      agentConnectionsApi.delete(id, connection_id),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: agentQueryKeys.connections.queryKey,
      });
      toast.success('Connection removed successfully');
    },
    onError: () => {
      toast.error('Failed to remove connection');
    },
  });
};

export const agentQuery = {
  query: {
    useList,
    useAgentsBuiltinTools,
    useConnections,
  },
  mutation: {
    useUpdateStatus,
    useUpdateInstructions,
    useUpdateAgentBuiltinTools,
    useCreateConnection,
    useDeleteConnection,
  },
};
