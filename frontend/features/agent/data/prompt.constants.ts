import {
  BarChartIcon,
  CloudIcon,
  DollarSignIcon,
  ShieldIcon,
} from 'lucide-react';

import { Prompt } from '../components/prompt-card';

export const DEFAULT_PROMPT_LIST: Prompt[] = [
  {
    provider: 'aws',
    icon: DollarSignIcon,
    title: 'Cost Optimization',
    question:
      'Analyze EC2 instance utilization metrics to identify opportunities for cost optimization and resource right-sizing.',
    className: {
      icon: 'text-emerald-400 drop-shadow-[0_0_8px_rgba(34,197,94,0.3)]',
      gradient: 'from-emerald-400/30 to-teal-600/30',
    },
  },
  {
    provider: 'aws',
    icon: ShieldIcon,
    title: 'Security Audit',
    question:
      'Perform a comprehensive security audit of S3 bucket configurations, permissions, and access patterns.',
    className: {
      icon: 'text-blue-400 drop-shadow-[0_0_8px_rgba(59,130,246,0.3)]',
      gradient: 'from-blue-400/30 to-indigo-600/30',
    },
  },
  {
    provider: 'aws',
    icon: Bar<PERSON>hartI<PERSON>,
    title: 'Performance Monitoring',
    question:
      'Monitor and analyze the performance metrics of your web application load balancer for any anomalies or issues.',
    className: {
      icon: 'text-violet-400 drop-shadow-[0_0_8px_rgba(167,139,250,0.3)]',
      gradient: 'from-violet-400/30 to-purple-600/30',
    },
  },
  {
    provider: 'aws',
    icon: CloudIcon,
    title: 'Complex Cost Optimization Report',
    question:
      'Conduct comprehensive cost optimization analysis with detailed billing data extraction, assessment roadmap creation, team delegation, and consolidated reporting with prioritized recommendations.',
    className: {
      icon: 'text-amber-400 drop-shadow-[0_0_8px_rgba(251,191,36,0.3)]',
      gradient: 'from-amber-400/30 to-orange-600/30',
    },
  },
];
