import {
  SchemaConnectionCreate,
  SchemaConnectionUpdate,
} from '@/openapi-ts/gens';
import { api, fetchData } from '@/openapi-ts/openapi-fetch';

import { ConnectionQueryParams } from '../models/connection.type';

export const connectionApi = {
  list: (query?: ConnectionQueryParams) =>
    fetchData(api.GET('/api/v1/connection/', { params: { query } })),

  builtinList: () => fetchData(api.GET('/api/v1/connection/builtin')),

  installBuiltin: (builtinId: string, configOverride?: Record<string, any>) =>
    fetchData(
      api.POST('/api/v1/connection/builtin/{builtin_id}/install', {
        params: { path: { builtin_id: builtinId } },
        body: configOverride
          ? { config_override: configOverride as Record<string, never> }
          : undefined,
      }),
    ),

  create: (body: SchemaConnectionCreate) =>
    fetchData(api.POST('/api/v1/connection/', { body })),

  detail: (connectionId: string) => ({
    get: () =>
      fetchData(
        api.GET('/api/v1/connection/{conn_id}', {
          params: { path: { conn_id: connectionId } },
        }),
      ),

    update: (body: SchemaConnectionUpdate) =>
      fetchData(
        api.PUT('/api/v1/connection/{conn_id}', {
          params: { path: { conn_id: connectionId } },
          body,
        }),
      ),

    delete: () =>
      fetchData(
        api.DELETE('/api/v1/connection/{conn_id}', {
          params: { path: { conn_id: connectionId } },
        }),
      ),
  }),
};
