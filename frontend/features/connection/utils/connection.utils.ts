import { ConnectionType } from '@/openapi-ts/gens';

/**
 * Extract display name from tool name by removing prefixes
 */
export function getToolDisplayName(tool: string): string {
  return tool.split('__').pop() || tool;
}

/**
 * Check if connection type is MCP
 */
export function isMcpConnection(type: ConnectionType): boolean {
  return type === ConnectionType.mcp;
}

/**
 * Check if connection type is builtin
 */
export function isBuiltinConnection(type: ConnectionType): boolean {
  return type === ConnectionType.builtin || type === ConnectionType.cloud || type === ConnectionType.cli;
}

/**
 * Get CSS classes for permission-based styling
 */
export function getPermissionStyles(requiresPermission: boolean, isEnabled?: boolean) {
  if (requiresPermission) {
    return {
      container: "bg-orange-50/50 border-orange-200 hover:bg-orange-100/50 dark:bg-orange-950/20 dark:border-orange-700 dark:hover:bg-orange-900/30",
      text: "text-orange-700 dark:text-orange-300"
    };
  }

  if (isEnabled) {
    return {
      container: "bg-primary/10 border-primary/20 hover:bg-primary/15",
      text: "text-primary"
    };
  }

  return {
    container: "bg-muted/50 border-border hover:bg-muted",
    text: "text-muted-foreground"
  };
}
