import { ConnectionTransport, ConnectionType, ConnectionStatus } from '@/openapi-ts/gens';
import { z } from 'zod';

export const connectionSchema = z.object({
  name: z
    .string()
    .min(1, { message: 'Connection name is required.' })
    .max(255, { message: 'Connection name must be at most 255 characters.' }),
  prefix: z
    .string()
    .min(1, { message: 'Prefix is required.' }),
  type: z.nativeEnum(ConnectionType, {
    required_error: 'Please select a connection type',
  }),
  transport_type: z.nativeEnum(ConnectionTransport, {
    required_error: 'Please select a transport type',
  }),
  config: z
    .record(z.string())
    .optional()
    .default({}),
  is_active: z
    .boolean()
    .optional()
    .default(true),
  tool_list: z.array(z.string()).optional().default([]),
  tool_permissions: z.array(z.string()).optional().default([]),
  tool_enabled: z.array(z.string()).optional().default([]),
  tool_schemas: z.record(z.string()).optional().default({}),
  status: z.nativeEnum(ConnectionStatus, {
    required_error: 'Please select a connection status',
  }),
  status_message: z.string().optional().default(''),
  status_updated_at: z.string().optional().default(''),
});


export type ConnectionSchema = z.infer<typeof connectionSchema>;
