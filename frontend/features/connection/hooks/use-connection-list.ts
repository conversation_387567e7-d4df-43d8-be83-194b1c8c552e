import { useMemo } from 'react';
import { SchemaConnectionPublic, ConnectionType, SchemaConnectionUpdate } from '@/openapi-ts/gens';
import { connectionQuery } from './connection.query';
import { isBuiltinConnection, isMcpConnection } from '../utils/connection.utils';

interface UseConnectionListProps {
  type: ConnectionType;
}

export function useConnectionList({ type }: UseConnectionListProps) {
  // Fetch connections based on type
  const { data: builtinResponse, isLoading: isLoadingBuiltin, error: builtinError } = connectionQuery.query.useBuiltinList();
  const { data: userConnectionsResponse, isLoading: isLoadingUser, error: userError } = connectionQuery.query.useList();

  // Handle errors and ensure we have valid arrays
  const builtinConnections = Array.isArray(builtinResponse?.data) ? builtinResponse.data : [];
  const userConnections = Array.isArray(userConnectionsResponse?.data) ? userConnectionsResponse.data : [];

  // Mutations
  const { mutate: installBuiltin, isPending: isInstalling } = connectionQuery.mutation.useInstallBuiltin();
  const { mutate: updateConnection } = connectionQuery.mutation.useUpdate();
  const { mutate: deleteConnection, isPending: isDeleting } = connectionQuery.mutation.useDelete();

  // Filter connections based on type
  const connections = useMemo(() => {
    if (builtinError || userError) {
      console.error('Connection API errors:', { builtinError, userError });
      return [];
    }

    if (isBuiltinConnection(type)) {
      return builtinConnections;
    } else {
      return userConnections.filter((conn) => isMcpConnection(conn.type));
    }
  }, [type, builtinConnections, userConnections, builtinError, userError]);

  const installedMcpConnections = useMemo(() => {
    return userConnections.filter((conn) => isMcpConnection(conn.type));
  }, [userConnections]);

  // For builtin connections, get installed connections directly
  const installedConnections = useMemo(() => {
    if (!isBuiltinConnection(type)) return [];

    return userConnections.filter((conn) =>
      builtinConnections.some((builtin) => builtin.name === conn.name)
    );
  }, [type, builtinConnections, userConnections]);

  // For builtin connections, track which ones are installed (for backward compatibility)
  const installedBuiltinIds = useMemo(() => {
    if (!isBuiltinConnection(type)) return new Set<string>();

    const installed = new Set<string>();
    installedConnections.forEach((installedConn) => {
      const matchingBuiltin = builtinConnections.find(
        (builtin) => builtin.name === installedConn.name
      );
      if (matchingBuiltin) {
        installed.add(matchingBuiltin.id);
      }
    });
    return installed;
  }, [type, builtinConnections, installedConnections]);

  // Event handlers
  const handleInstall = (connectionId: string, configOverride?: Record<string, any>) => {
    console.log('Install connection:', { connectionId, configOverride });
    installBuiltin({ builtinId: connectionId, configOverride });
  };

  const handleEdit = (connection: SchemaConnectionPublic) => {
    // Edit logic is now handled in ConnectionList component
    console.log('Edit connection:', { connectionId: connection.id });
  };

  const handleDelete = (connection: SchemaConnectionPublic) => {
    if (isBuiltinConnection(connection.type) && isBuiltinConnection(type)) {
      // For builtin connections in the builtin view, we need to find the installed connection and delete it
      const installedConnection = userConnections.find(
        (conn) => conn.name === connection.name && isBuiltinConnection(conn.type)
      );

      if (installedConnection) {
        deleteConnection(installedConnection.id);
      }
    } else {
      // For MCP connections or direct connection deletion
      deleteConnection(connection.id);
    }
  };

  const handleToolToggle = (connection: SchemaConnectionPublic, toolName: string, enabled: boolean) => {
    const currentEnabledTools = connection.tool_enabled || [];
    const newEnabledTools = enabled
      ? [...currentEnabledTools, toolName]
      : currentEnabledTools.filter((t) => t !== toolName);

    const updateData: SchemaConnectionUpdate = {
      tool_enabled: newEnabledTools,
    };

    updateConnection({ id: connection.id, data: updateData });
  };

  const handleConnectionToggle = (connection: SchemaConnectionPublic, active: boolean) => {
    const updateData: SchemaConnectionUpdate = { is_active: active };
    updateConnection({ id: connection.id, data: updateData });
  };

  const handlePermissionsChange = (connection: SchemaConnectionPublic, toolPermissions: string[]) => {
    const updateData: SchemaConnectionUpdate = { tool_permissions: toolPermissions };
    updateConnection({ id: connection.id, data: updateData });
  };

  const isLoading = isBuiltinConnection(type) ? (isLoadingBuiltin || isLoadingUser) : isLoadingUser;
  const error = isBuiltinConnection(type) ? (builtinError || userError) : userError;

  return {
    connections,
    installedMcpConnections,
    installedConnections,
    installedBuiltinIds,
    isInstalling,
    isDeleting,
    isLoading,
    error,
    handleInstall,
    handleEdit,
    handleDelete,
    handleToolToggle,
    handleConnectionToggle,
    handlePermissionsChange,
  };
}
