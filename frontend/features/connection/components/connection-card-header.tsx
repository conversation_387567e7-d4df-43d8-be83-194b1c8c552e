'use client';

import { CardTitle } from '@/components/ui/card';
import { Server, Database } from 'lucide-react';
import { SchemaConnectionPublic } from '@/openapi-ts/gens';
import Image from 'next/image';

// Icon mapping for builtin connections based on their names
function getBuiltinConnectionIcon(connectionName: string) {
  switch (connectionName) {
    case 'PostgreSQL':
      return { type: 'icon', component: Database };
    case 'Amazon Web Services':
      return { type: 'image', src: '/aws-logo.svg', alt: 'AWS Logo' };
    case 'Google Cloud Platform':
      return { type: 'image', src: '/gcp-logo.svg', alt: 'Google Cloud Logo' };
    case "Microsoft Azure":
      return { type: 'image', src: '/azure-logo.svg', alt: 'Microsoft Azure Logo' };
    case 'Grafana':
      return { type: 'image', src: '/grafana-logo.svg', alt: 'Grafana Logo' };
    case 'Kubernetes':
      return { type: 'image', src: '/k8s-log.svg', alt: 'Kubernetes Logo' };
    default:
      return { type: 'icon', component: Server };
  }
}

export interface ConnectionCardHeaderProps {
  readonly connection: SchemaConnectionPublic;
}

export function ConnectionCardHeader({
  connection,
}: ConnectionCardHeaderProps) {
  const iconConfig = getBuiltinConnectionIcon(connection.name);

  return (
    <div className="flex items-center justify-between px-4 py-3">
      <div className="flex items-center gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-muted">
          {iconConfig.type === 'image' && iconConfig.src && iconConfig.alt ? (
            <Image
              src={iconConfig.src}
              alt={iconConfig.alt}
              width={20}
              height={20}
              className="h-5 w-5"
            />
          ) : (
            iconConfig.type === 'icon' && iconConfig.component && (
              <iconConfig.component className="h-5 w-5 text-muted-foreground" />
            )
          )}
        </div>
        <CardTitle className="text-sm font-medium text-foreground group-hover:text-primary transition-colors">
          {connection.name}
        </CardTitle>
      </div>
    </div>
  );
}
