'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, ChevronDown, ChevronUp, <PERSON>ader2, Eye, AlertCircle } from 'lucide-react';
import { SchemaConnectionPublic } from '@/openapi-ts/gens';
import { useMemo, useCallback, useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { getToolDisplayName, isMcpConnection } from '../utils/connection.utils';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';

export interface ConnectionToolsSectionProps {
  readonly connection: SchemaConnectionPublic;
  readonly displayConnection: SchemaConnectionPublic;
  readonly isInstalled: boolean;
  readonly onToolToggle?: (connection: SchemaConnectionPublic, toolName: string, enabled: boolean) => void;
  readonly onPermissionsChange?: (connection: SchemaConnectionPublic, toolPermissions: string[]) => void;
}

export function ConnectionToolsSection({
  connection,
  displayConnection,
  isInstalled,
  onToolToggle,
  onPermissionsChange,
}: ConnectionToolsSectionProps) {
  const [showPermissionsDialog, setShowPermissionsDialog] = useState(false);
  const [tempPermissions, setTempPermissions] = useState<string[]>([]);
  const [showAllTools, setShowAllTools] = useState(false);
  const [togglingTools, setTogglingTools] = useState<Set<string>>(new Set());


  // Initialize temp permissions when dialog opens
  const handleOpenPermissionsDialog = useCallback(() => {
    const currentPermissions = displayConnection.tool_permissions || [];
    setTempPermissions([...currentPermissions]);
    setShowPermissionsDialog(true);
  }, [displayConnection.tool_permissions]);

  // Handle permission toggle
  const handlePermissionToggle = useCallback((toolName: string, requiresPermission: boolean) => {
    setTempPermissions(prev => {
      if (requiresPermission) {
        return prev.includes(toolName) ? prev : [...prev, toolName];
      } else {
        return prev.filter(t => t !== toolName);
      }
    });
  }, []);

  // Handle tool toggle with loading state
  const handleToolToggle = useCallback(async (connection: SchemaConnectionPublic, toolName: string, enabled: boolean) => {
    if (!onToolToggle) return;

    setTogglingTools(prev => new Set(prev).add(toolName));

    try {
      await onToolToggle(connection, toolName, enabled);
    } finally {
      setTogglingTools(prev => {
        const next = new Set(prev);
        next.delete(toolName);
        return next;
      });
    }
  }, [onToolToggle]);

  // Save permissions
  const handleSavePermissions = useCallback(() => {
    onPermissionsChange?.(displayConnection, tempPermissions);
    setShowPermissionsDialog(false);
  }, [displayConnection, tempPermissions, onPermissionsChange]);

  const variant = useMemo(() => {
    // Always use 'installed' variant for consistent UI layout
    return 'installed';
  }, [connection.type]);

  // Get tools to display
  const tools = useMemo(() => {
    const sourceConnection = isInstalled ? displayConnection : connection;
    return sourceConnection.tool_list || [];
  }, [connection, displayConnection, isInstalled]);

  const effectivelyInstalled = isInstalled || isMcpConnection(connection.type);
  const isActive = displayConnection.is_active !== false;

  // Determine tools to show based on showAllTools state
  const toolsToShow = useMemo(() => {
    return showAllTools ? tools : tools.slice(0, 6);
  }, [tools, showAllTools]);

  // Only show expand/collapse button if there are more than 6 tools
  const shouldShowExpandButton = tools.length > 6;

  if (!tools.length) return null;

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <div
          className={cn(
            'text-muted-foreground flex items-center gap-1 text-xs font-medium',
            !isActive && 'text-destructive',
          )}
        >
          <Wrench className="h-3 w-3" />
          Tools ({tools.length})
          {!isActive && (
            <AlertCircle className="text-destructive h-3 w-3" />
          )}
          {!isInstalled && !isMcpConnection(connection.type) && (
            <span className="text-xs text-muted-foreground font-normal">
              (Install to enable)
            </span>
          )}
        </div>
        <div className="flex items-center gap-1">
          {(isInstalled || isMcpConnection(connection.type)) && onPermissionsChange && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleOpenPermissionsDialog}
                    className="text-muted-foreground hover:text-foreground h-6 px-2 text-xs"
                  >
                    <Settings className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top" className="text-xs">
                  Manage Permissions
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          {shouldShowExpandButton && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAllTools(!showAllTools)}
              className="text-muted-foreground hover:text-foreground h-6 px-2 text-xs"
            >
              {showAllTools ? (
                <>
                  <ChevronUp className="mr-1 h-3 w-3" />
                  Show Less
                </>
              ) : (
                <>
                  <ChevronDown className="mr-1 h-3 w-3" />
                  Show All
                </>
              )}
            </Button>
          )}
        </div>
      </div>
      <div className={cn(
        "flex flex-wrap gap-1.5",
        showAllTools && shouldShowExpandButton && "max-h-32 overflow-y-auto scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent"
      )}>
        {toolsToShow.map((tool, index) => {
          const isEnabled = effectivelyInstalled
            ? (displayConnection.tool_enabled?.includes(tool) || false)
            : false;
          const isToggling = togglingTools.has(tool);
          const requiresPermission = displayConnection.tool_permissions?.includes(tool) ?? false;
          const cleanToolName = getToolDisplayName(tool);

          return (
            <TooltipProvider key={index}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={isEnabled ? 'secondary' : 'outline'}
                    size="sm"
                    onClick={() => handleToolToggle(displayConnection, tool, !isEnabled)}
                    disabled={isToggling || !isActive || !effectivelyInstalled}
                    className={cn(
                      'h-auto min-h-[24px] px-2 py-1 text-xs transition-all duration-200 ease-in-out',
                      'relative hover:scale-[1.02] active:scale-[0.98]',
                      // Enabled state with permission variants
                      isEnabled &&
                        !requiresPermission && [
                          'border-primary/30 bg-primary/10 text-primary-foreground',
                          'hover:border-primary/40 hover:bg-primary/15',
                          'shadow-xs hover:shadow-md',
                        ],
                      // Enabled state with permissions - amber/yellow styling
                      isEnabled &&
                        requiresPermission && [
                          'border-amber-300 bg-amber-50 dark:border-amber-700 dark:bg-amber-950/30',
                          'text-amber-800 dark:text-amber-200',
                          'hover:bg-amber-100 dark:hover:bg-amber-950/50',
                          'hover:border-amber-400 dark:hover:border-amber-600',
                          'shadow-xs shadow-amber-200/50 dark:shadow-amber-900/20',
                          'hover:shadow-md hover:shadow-amber-200/60 dark:hover:shadow-amber-900/30',
                        ],
                      // Disabled state - muted, professional styling
                      !isEnabled && [
                        'bg-muted/30 text-muted-foreground opacity-60',
                        'border-muted-foreground/20 hover:opacity-80',
                        'hover:border-muted-foreground/30 hover:bg-muted/40',
                      ],
                      isToggling &&
                        'pointer-events-none animate-pulse',
                    )}
                  >
                    <div className="flex items-center gap-1.5">
                      <div
                        className={cn(
                          'flex items-center justify-center',
                          isToggling && 'animate-spin',
                        )}
                      >
                        {isToggling ? (
                          <Loader2 className="h-3 w-3" />
                        ) : (
                          <div
                            className={cn(
                              'h-2 w-2 rounded-full transition-all duration-200',
                              isEnabled
                                ? requiresPermission
                                  ? 'bg-amber-500 shadow-xs shadow-amber-500/30'
                                  : 'bg-green-500 shadow-xs shadow-green-500/30'
                                : 'border-muted-foreground/30 bg-muted-foreground/40 border',
                            )}
                          />
                        )}
                      </div>
                      <span
                        className={cn(
                          'max-w-[80px] truncate font-medium transition-all duration-200',
                          isEnabled
                            ? requiresPermission
                              ? 'text-amber-800 dark:text-amber-200'
                              : 'text-foreground'
                            : 'text-muted-foreground',
                        )}
                      >
                        {cleanToolName}
                      </span>
                    </div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent
                  side="top"
                  align="center"
                  className="border-border bg-popover max-w-[300px] rounded-lg border px-3 py-2 shadow-lg"
                  sideOffset={8}
                >
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div
                        className={cn(
                          'h-2.5 w-2.5 rounded-full',
                          isEnabled
                            ? requiresPermission
                              ? 'bg-amber-500'
                              : 'bg-green-500'
                            : 'bg-muted-foreground/40',
                        )}
                      />
                      <p className="text-popover-foreground text-sm font-medium">
                        {cleanToolName}
                      </p>
                      {requiresPermission && (
                        <div className="inline-flex items-center gap-1 rounded bg-amber-100 px-1.5 py-0.5 text-xs font-medium text-amber-700 dark:bg-amber-900/30 dark:text-amber-300">
                          <Eye className="h-3 w-3" />
                          Protected
                        </div>
                      )}
                    </div>
                    <div className="space-y-1">
                      <p className="text-muted-foreground text-xs">
                        <span className="font-medium">Status:</span>{' '}
                        {isEnabled ? 'Active' : 'Inactive'}
                      </p>
                      {requiresPermission && isEnabled && (
                        <p className="text-xs text-amber-600 dark:text-amber-400">
                          <span className="font-medium">
                            Permission:
                          </span>{' '}
                          User approval required before execution
                        </p>
                      )}
                      <p className="text-muted-foreground text-xs">
                        <span className="font-medium">Action:</span>{' '}
                        Click to{' '}
                        {isEnabled ? 'deactivate' : 'activate'} this
                        tool
                      </p>
                      {!isActive && (
                        <p className="text-xs text-amber-600 dark:text-amber-400">
                          Connection must be active to toggle tools
                        </p>
                      )}
                      {!effectivelyInstalled && (
                        <p className="text-xs text-amber-600 dark:text-amber-400">
                          Connection must be installed to enable tools
                        </p>
                      )}
                    </div>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        })}
      </div>

      {/* Tool Permissions Dialog */}
      <Dialog open={showPermissionsDialog} onOpenChange={setShowPermissionsDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Tool Permissions - {displayConnection.name}</DialogTitle>
            <DialogDescription>
              Choose which tools require approval before execution.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {(displayConnection.tool_list || []).map((toolName) => {
              const requiresPermission = tempPermissions.includes(toolName);
              const displayName = getToolDisplayName(toolName);

              return (
                <div
                  key={toolName}
                  className={cn(
                    "flex items-center justify-between p-3 rounded-lg border transition-colors",
                    requiresPermission
                      ? "border-orange-500 bg-orange-50/50 dark:bg-orange-950/20"
                      : "border-gray-200 dark:border-gray-700"
                  )}
                >
                  <div className="flex items-center gap-3 flex-1">
                    <div className={cn(
                      "w-2 h-2 rounded-full",
                      requiresPermission ? "bg-orange-500" : "bg-gray-400"
                    )} />
                    <div className="flex flex-col">
                      <span className="font-medium text-sm">{displayName}</span>
                      <span className="text-xs text-muted-foreground">
                        {requiresPermission ? "Requires approval" : "Inactive"}
                      </span>
                    </div>
                    {requiresPermission && (
                      <Badge variant="outline" className="ml-2 text-xs border-orange-500 text-orange-600">
                        Protected
                      </Badge>
                    )}
                  </div>
                  <Switch
                    checked={requiresPermission}
                    onCheckedChange={(checked) => handlePermissionToggle(toolName, checked)}
                    className="ml-4"
                  />
                </div>
              );
            })}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPermissionsDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleSavePermissions}>
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
