'use client';

import { ReactNode, useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Di<PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  ConnectionStatus,
  ConnectionTransport,
  ConnectionType,
  SchemaConnectionCreate,
} from '@/openapi-ts/gens';
import { SchemaConnectionPublic } from '@/openapi-ts/gens';
import { Globe, Loader2, Plus, X, Zap } from 'lucide-react';

import { connectionQuery } from '../hooks/connection.query';

interface FormData {
  name: string;
  prefix: string;
  transport_type: ConnectionTransport;
  url: string;
  headers: Record<string, string>;
  timeout: number;
  sse_read_timeout: number;
  is_active: boolean;
}

interface HeaderEntry {
  key: string;
  value: string;
}

const DEFAULT_HEADERS: HeaderEntry[] = [
  { key: 'Authorization', value: 'Bearer <your-token-here>' },
];

interface CreateConnectionDialogProps {
  children?: ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  editConnection?: SchemaConnectionPublic | null;
}

export const CreateConnectionDialog = ({
  children: _children,
  open: controlledOpen,
  onOpenChange,
  editConnection,
}: CreateConnectionDialogProps) => {
  const [internalOpen, setInternalOpen] = useState(false);
  const open = controlledOpen ?? internalOpen;
  const setOpen = onOpenChange || setInternalOpen;
  const { mutate: createConnection, isPending: isCreating } =
    connectionQuery.mutation.useCreate();
  const { mutate: updateConnection, isPending: isUpdating } =
    connectionQuery.mutation.useUpdate();

  const isPending = isCreating || isUpdating;
  const isEditing = !!editConnection;

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [headerEntries, setHeaderEntries] =
    useState<HeaderEntry[]>(DEFAULT_HEADERS);
  const [formData, setFormData] = useState<FormData>({
    name: '',
    prefix: '',
    transport_type: ConnectionTransport.sse,
    url: '',
    headers: {},
    timeout: 5.0,
    sse_read_timeout: 30.0,
    is_active: true,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [prefixManuallyEdited, setPrefixManuallyEdited] = useState(false);

  useEffect(() => {
    if (open) {
      if (isEditing && editConnection) {
        // Populate form with existing connection data
        const config = (editConnection.config as any) || {};
        const existingHeaders = config.headers || {};
        const headerEntriesFromConnection =
          Object.keys(existingHeaders).length > 0
            ? Object.entries(existingHeaders).map(([key, value]) => ({
                key,
                value: String(value),
              }))
            : DEFAULT_HEADERS;

        setHeaderEntries(headerEntriesFromConnection);
        setFormData({
          name: editConnection.name || '',
          prefix: editConnection.prefix || '',
          transport_type:
            editConnection.transport_type || ConnectionTransport.sse,
          url: config.url || '',
          headers: existingHeaders,
          timeout: config.timeout || 5.0,
          sse_read_timeout: config.sse_read_timeout || 30.0,
          is_active: editConnection.is_active ?? true,
        });
        setPrefixManuallyEdited(true); // In edit mode, don't auto-generate prefix
      } else {
        // Reset form for new connection
        setHeaderEntries(DEFAULT_HEADERS);
        setFormData({
          name: '',
          prefix: '',
          transport_type: ConnectionTransport.sse,
          url: '',
          headers: {},
          timeout: 5.0,
          sse_read_timeout: 30.0,
          is_active: true,
        });
        setPrefixManuallyEdited(false);
      }
    }
  }, [open, isEditing, editConnection]);

  const nameToPrefix = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  };

  const handleNameChange = (newName: string) => {
    setFormData((prev) => {
      const updates: Partial<FormData> = { name: newName };
      if (!prefixManuallyEdited && newName.trim()) {
        updates.prefix = nameToPrefix(newName);
      }
      return { ...prev, ...updates };
    });
  };

  const handlePrefixChange = (newPrefix: string) => {
    setFormData((prev) => ({ ...prev, prefix: newPrefix }));
    if (!prefixManuallyEdited) {
      setPrefixManuallyEdited(true);
    }
  };

  const handleUrlChange = (newUrl: string) => {
    setFormData((prev) => ({
      ...prev,
      url: newUrl,
    }));
  };

  const handleTypeChange = (newType: ConnectionTransport) => {
    setFormData((prev) => ({
      ...prev,
      transport_type: newType,
    }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.prefix.trim()) {
      newErrors.prefix = 'Prefix is required';
    }

    if (!formData.url.trim()) {
      newErrors.url = 'URL is required';
    } else {
      const requiredSuffix =
        formData.transport_type === ConnectionTransport.sse ? '/sse' : '/mcp';
      if (!formData.url.endsWith(requiredSuffix)) {
        newErrors.url = `URL must end with "${requiredSuffix}" for ${formData.transport_type === ConnectionTransport.sse ? 'Server-Sent Events' : 'Streamable HTTP'} connection type`;
      }
    }

    if (formData.timeout < 1) {
      newErrors.timeout = 'Timeout must be at least 1 second';
    }

    if (formData.sse_read_timeout < 1) {
      newErrors.sse_read_timeout = 'SSE read timeout must be at least 1 second';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }
    setIsSubmitting(true);
    try {
      const headers = headerEntries.reduce(
        (acc, { key, value }) => {
          if (key.trim() && value.trim()) {
            acc[key.trim()] = value.trim();
          }
          return acc;
        },
        {} as Record<string, string>,
      );

      const config = {
        url: formData.url,
        headers: headers,
        timeout: formData.timeout,
        sse_read_timeout: formData.sse_read_timeout,
      };

      if (isEditing && editConnection) {
        // Update existing connection
        const updateData = {
          name: formData.name,
          prefix: formData.prefix,
          transport_type: formData.transport_type,
          config: config,
          is_active: formData.is_active,
        };

        updateConnection(
          { id: editConnection.id, data: updateData as any },
          {
            onSuccess: () => {
              setOpen(false);
            },
          },
        );
      } else {
        // Create new connection
        const connectionData: SchemaConnectionCreate = {
          name: formData.name,
          prefix: formData.prefix,
          type: ConnectionType.mcp,
          transport_type: formData.transport_type,
          config: config as any,
          is_active: formData.is_active,
          tool_list: [],
          tool_permissions: [],
          tool_enabled: [],
          tool_schemas: [],
          status: ConnectionStatus.error,
          status_message: '',
          status_updated_at: new Date().toISOString(),
        };

        createConnection(connectionData, {
          onSuccess: () => {
            setOpen(false);
          },
        });
      }
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const addHeader = () => {
    setHeaderEntries([...headerEntries, { key: '', value: '' }]);
  };

  const removeHeader = (index: number) => {
    setHeaderEntries(headerEntries.filter((_, i) => i !== index));
  };

  const updateHeader = (
    index: number,
    field: 'key' | 'value',
    value: string,
  ) => {
    const newEntries = [...headerEntries];
    newEntries[index] = { ...newEntries[index], [field]: value };
    setHeaderEntries(newEntries);
  };

  return (
    <Dialog open={open} onOpenChange={(open) => !open && setOpen(false)}>
      <DialogContent className="sm:max-w-[580px]">
        <DialogHeader className="pb-2">
          <DialogTitle className="text-lg font-semibold">
            {isEditing ? 'Edit Server' : 'Add New Server'}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Basic Configuration */}
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1.5">
                <Label htmlFor="name" className="text-sm font-medium">
                  Server Name
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleNameChange(e.target.value)}
                  placeholder="My Server"
                  className={`h-9 ${errors.name ? 'border-red-500 dark:border-red-400' : ''}`}
                />
                {errors.name && (
                  <p className="text-xs text-red-500 dark:text-red-400">
                    {errors.name}
                  </p>
                )}
              </div>

              <div className="space-y-1.5">
                <Label htmlFor="prefix" className="text-sm font-medium">
                  Prefix
                </Label>
                <Input
                  id="prefix"
                  value={formData.prefix}
                  onChange={(e) => handlePrefixChange(e.target.value)}
                  placeholder="my-server"
                  className={`h-9 ${errors.prefix ? 'border-red-500 dark:border-red-400' : ''}`}
                />
                {errors.prefix && (
                  <p className="text-xs text-red-500 dark:text-red-400">
                    {errors.prefix}
                  </p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium">Connection Type</Label>
              <RadioGroup
                value={formData.transport_type}
                onValueChange={handleTypeChange}
                className="grid grid-cols-2 gap-3"
              >
                <div
                  className="flex cursor-pointer items-center space-x-2 rounded-lg border p-2.5 transition-colors hover:bg-gray-50 dark:hover:bg-gray-800"
                  onClick={() => handleTypeChange(ConnectionTransport.sse)}
                >
                  <RadioGroupItem value={ConnectionTransport.sse} id="sse" />
                  <div className="flex items-center space-x-2">
                    <Zap className="h-4 w-4 text-blue-500 dark:text-blue-400" />
                    <Label htmlFor="sse" className="cursor-pointer text-sm">
                      Server-Sent Events
                    </Label>
                  </div>
                </div>
                <div
                  className="flex cursor-pointer items-center space-x-2 rounded-lg border p-2.5 transition-colors hover:bg-gray-50 dark:hover:bg-gray-800"
                  onClick={() =>
                    handleTypeChange(ConnectionTransport.streamable_http)
                  }
                >
                  <RadioGroupItem
                    value={ConnectionTransport.streamable_http}
                    id="streamable_http"
                  />
                  <div className="flex items-center space-x-2">
                    <Globe className="h-4 w-4 text-green-500 dark:text-green-400" />
                    <Label
                      htmlFor="streamable_http"
                      className="cursor-pointer text-sm"
                    >
                      Streamable HTTP
                    </Label>
                  </div>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-1.5">
              <Label htmlFor="url" className="text-sm font-medium">
                Server URL
              </Label>
              <Input
                id="url"
                value={formData.url}
                onChange={(e) => handleUrlChange(e.target.value)}
                placeholder={`https://api.example.com${formData.transport_type === ConnectionTransport.sse ? '/sse' : '/mcp'}`}
                className={`h-9 ${errors.url ? 'border-red-500 dark:border-red-400' : ''}`}
              />
              {errors.url && (
                <p className="text-xs text-red-500 dark:text-red-400">
                  {errors.url}
                </p>
              )}
              <p className="text-muted-foreground text-xs">
                URL must end with "
                {formData.transport_type === ConnectionTransport.sse
                  ? '/sse'
                  : '/mcp'}
                " for{' '}
                {formData.transport_type === ConnectionTransport.sse
                  ? 'Server-Sent Events'
                  : 'Streamable HTTP'}{' '}
                connections
              </p>
            </div>
          </div>

          {/* Headers Section */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Headers</Label>
            <div className="overflow-hidden rounded-lg border">
              <div className="max-h-[80px] overflow-y-auto bg-gray-50/50 dark:bg-gray-900/50">
                {headerEntries.map((entry, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-2 border-b border-gray-200 bg-white p-2 last:border-b-0 dark:border-gray-700 dark:bg-gray-950"
                  >
                    <Input
                      placeholder="Key"
                      value={entry.key}
                      onChange={(e) =>
                        updateHeader(index, 'key', e.target.value)
                      }
                      disabled={entry.key === 'Authorization'}
                      className="h-8 flex-1"
                    />
                    <Input
                      placeholder="Value"
                      value={entry.value}
                      onChange={(e) =>
                        updateHeader(index, 'value', e.target.value)
                      }
                      className="h-8 flex-1"
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeHeader(index)}
                      disabled={entry.key === 'Authorization'}
                      className="h-8 w-8 p-1"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
              <div className="bg-gray-50 p-1.5 dark:bg-gray-900">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={addHeader}
                  className="h-7 w-full text-xs"
                >
                  <Plus className="mr-1 h-3 w-3" />
                  Add Header
                </Button>
              </div>
            </div>
          </div>

          {/* Settings */}
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1.5">
                <Label htmlFor="timeout" className="text-sm font-medium">
                  Timeout (seconds)
                </Label>
                <Input
                  id="timeout"
                  type="text"
                  inputMode="decimal"
                  placeholder="5.0"
                  value={formData.timeout}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (value === '' || /^\d*\.?\d*$/.test(value)) {
                      const numValue = value === '' ? 0 : parseFloat(value);
                      setFormData({
                        ...formData,
                        timeout: isNaN(numValue) ? 0 : numValue,
                      });
                    }
                  }}
                  className={`h-9 ${errors.timeout ? 'border-red-500 dark:border-red-400' : ''}`}
                />
                {errors.timeout && (
                  <p className="text-xs text-red-500 dark:text-red-400">
                    {errors.timeout}
                  </p>
                )}
              </div>

              <div className="space-y-1.5">
                <Label
                  htmlFor="sse_read_timeout"
                  className="text-sm font-medium"
                >
                  SSE Timeout (seconds)
                </Label>
                <Input
                  id="sse_read_timeout"
                  type="text"
                  inputMode="decimal"
                  placeholder="30.0"
                  value={formData.sse_read_timeout}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (value === '' || /^\d*\.?\d*$/.test(value)) {
                      const numValue = value === '' ? 0 : parseFloat(value);
                      setFormData({
                        ...formData,
                        sse_read_timeout: isNaN(numValue) ? 0 : numValue,
                      });
                    }
                  }}
                  className={`h-9 ${errors.sse_read_timeout ? 'border-red-500 dark:border-red-400' : ''}`}
                />
                {errors.sse_read_timeout && (
                  <p className="text-xs text-red-500 dark:text-red-400">
                    {errors.sse_read_timeout}
                  </p>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-2 rounded-lg border border-blue-200 bg-blue-50 p-2.5 dark:border-blue-800 dark:bg-blue-950/50">
              <Checkbox
                id="is_active"
                checked={formData.is_active}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, is_active: checked === true })
                }
              />
              <Label htmlFor="is_active" className="cursor-pointer text-sm">
                Enable server connections
              </Label>
            </div>
          </div>
        </div>

        <DialogFooter className="pt-4">
          <Button
            variant="outline"
            onClick={() => setOpen(false)}
            disabled={isSubmitting || isPending}
            className="h-9"
          >
            {isSubmitting || isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Wait...
              </>
            ) : (
              'Cancel'
            )}
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting || isPending}
            className="h-9 min-w-[120px]"
          >
            {isSubmitting || isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isEditing ? 'Saving...' : 'Adding...'}
              </>
            ) : isEditing ? (
              'Save Changes'
            ) : (
              'Add Server'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
