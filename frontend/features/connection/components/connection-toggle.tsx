'use client';

import { Switch } from '@/components/ui/switch';
import { SchemaConnectionPublic } from '@/openapi-ts/gens';
import { useCallback } from 'react';

export interface ConnectionToggleProps {
  readonly connection: SchemaConnectionPublic;
  readonly isInstalled: boolean;
  readonly onConnectionToggle?: (connection: SchemaConnectionPublic, active: boolean) => void;
  readonly showConnectionToggle?: boolean;
}

export function ConnectionToggle({
  connection,
  isInstalled,
  onConnectionToggle,
  showConnectionToggle = true,
}: ConnectionToggleProps) {
  const handleConnectionToggle = useCallback(
    (checked: boolean) => {
      onConnectionToggle?.(connection, checked);
    },
    [connection, onConnectionToggle]
  );

  // Only show toggle for installed connections
  if (!isInstalled || !showConnectionToggle || !onConnectionToggle) {
    return null;
  }

  return (
    <Switch
      checked={connection.is_active}
      onCheckedChange={handleConnectionToggle}
      aria-label={`Toggle ${connection.name} connection active state`}
    />
  );
}
