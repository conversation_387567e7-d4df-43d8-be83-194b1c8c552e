'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { <PERSON>lug, Loader2, Trash2, Unplug, AlertTriangle, Edit } from 'lucide-react';
import { SchemaConnectionPublic } from '@/openapi-ts/gens';
import { ReactNode, useCallback, useState } from 'react';
import { isMcpConnection, isBuiltinConnection } from '../utils/connection.utils';

export interface ConnectionActionButtonsProps {
  readonly connection: SchemaConnectionPublic;
  readonly isInstalled: boolean;
  readonly isInstalling?: boolean;
  readonly isDeleting?: boolean;
  readonly onInstall?: (connection: SchemaConnectionPublic) => void;
  readonly onEdit?: (connection: SchemaConnectionPublic) => void;
  readonly onDelete?: (connection: SchemaConnectionPublic) => void;
  readonly actionButtons?: ReactNode;
}

export function ConnectionActionButtons({
  connection,
  isInstalled,
  isInstalling = false,
  isDeleting = false,
  onInstall,
  onEdit,
  onDelete,
  actionButtons,
}: ConnectionActionButtonsProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const handleInstall = useCallback(() => {
    onInstall?.(connection);
  }, [connection, onInstall]);

  const handleDeleteClick = useCallback(() => {
    setShowDeleteDialog(true);
  }, []);

  const handleDeleteConfirm = useCallback(() => {
    onDelete?.(connection);
    setShowDeleteDialog(false);
  }, [connection, onDelete]);

  const handleDeleteCancel = useCallback(() => {
    setShowDeleteDialog(false);
  }, []);

  const handleEdit = useCallback(() => {
    onEdit?.(connection);
  }, [connection, onEdit]);

  // If custom action buttons are provided, use them
  if (actionButtons) {
    return <>{actionButtons}</>;
  }

  // For uninstalled connections - show Connect button
  if (!isInstalled && onInstall) {
    return (
      <Button
        onClick={handleInstall}
        className="flex items-center gap-2"
        size="sm"
        disabled={isInstalling}
        aria-label={`Connect ${connection.name} connection`}
      >
        {isInstalling ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Plug className="h-4 w-4" />
        )}
        {isInstalling ? 'Connecting...' : 'Connect'}
      </Button>
    );
  }

  // For installed connections or MCP connections (MCP can be deleted regardless of installation status)
  if (isInstalled || isMcpConnection(connection.type)) {
    const isMcp = isMcpConnection(connection.type);
    const isBuiltin = isBuiltinConnection(connection.type);

    if (isMcp || (isBuiltin && isInstalled)) {
      const buttonConfig = isMcp
        ? {
            icon: Trash2,
            text: 'Delete',
            loadingText: 'Deleting...',
            dialogTitle: 'Delete Connection',
            dialogDescription: 'This action cannot be undone. This will permanently delete the connection and remove all associated data.',
            confirmText: 'Delete Connection',
            type: 'delete' as const
          }
        : {
            icon: Unplug,
            text: 'Disconnect',
            loadingText: 'Disconnecting...',
            dialogTitle: 'Disconnect Connection',
            dialogDescription: 'This will remove the connection from your workspace. You can reconnect it later if needed.',
            confirmText: 'Disconnect Connection',
            type: 'disconnect' as const
          };

      const Icon = buttonConfig.icon;

      return (
        <div className="flex items-center gap-2">
          {/* Edit button - show for MCP connections or installed builtin connections */}
          {onEdit && (isMcp || (isBuiltin && isInstalled)) && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleEdit}
              className="flex items-center gap-2"
              aria-label={`Edit ${connection.name} connection`}
            >
              <Edit className="h-4 w-4" />
              Edit
            </Button>
          )}

          {/* Delete/Uninstall button - show if onDelete is provided */}
          {onDelete && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDeleteClick}
                className="flex items-center gap-2 text-destructive hover:bg-destructive hover:text-destructive-foreground"
                disabled={isDeleting}
                aria-label={`${buttonConfig.text} ${connection.name} connection`}
              >
                {isDeleting ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Icon className="h-4 w-4" />
                )}
                {isDeleting ? buttonConfig.loadingText : buttonConfig.text}
              </Button>

              <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-5 w-5 text-destructive" />
                      <AlertDialogTitle>{buttonConfig.dialogTitle}</AlertDialogTitle>
                    </div>
                    <AlertDialogDescription className="text-muted-foreground">
                      Are you sure you want to {buttonConfig.type} "{connection.name}"?
                      <br />
                      <br />
                      {buttonConfig.dialogDescription}
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel
                      onClick={handleDeleteCancel}
                      disabled={isDeleting}
                    >
                      Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleDeleteConfirm}
                      disabled={isDeleting}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      {isDeleting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          {buttonConfig.loadingText}
                        </>
                      ) : (
                        buttonConfig.confirmText
                      )}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </>
          )}
        </div>
      );
    }
  }

  return null;
}
