'use client';

import { SchemaConnectionPublic, ConnectionType } from '@/openapi-ts/gens';
import { ConnectionCard } from './connection-card';
import { isBuiltinConnection, isMcpConnection } from '../utils/connection.utils';

interface ConnectionGridProps {
  readonly connections: SchemaConnectionPublic[];
  readonly type: ConnectionType;
  readonly installedBuiltinIds: Set<string>;
  readonly installedConnections: SchemaConnectionPublic[];
  readonly isInstalling: boolean;
  readonly isDeleting: boolean;
  readonly onInstall?: (connection: SchemaConnectionPublic) => void;
  readonly onEdit?: (connection: SchemaConnectionPublic) => void;
  readonly onDelete?: (connection: SchemaConnectionPublic) => void;
  readonly onToolToggle: (connection: SchemaConnectionPublic, toolName: string, enabled: boolean) => void;
  readonly onConnectionToggle: (connection: SchemaConnectionPublic, active: boolean) => void;
  readonly onPermissionsChange?: (connection: SchemaConnectionPublic, toolPermissions: string[]) => void;
}

export function ConnectionGrid({
  connections,
  type,
  installedBuiltinIds,
  installedConnections,
  isInstalling,
  isDeleting,
  onInstall,
  onEdit,
  onDelete,
  onToolToggle,
  onConnectionToggle,
  onPermissionsChange,
}: ConnectionGridProps) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 auto-rows-min">
      {connections.map((connection) => {
        const isInstalled = isBuiltinConnection(type) ? installedBuiltinIds.has(connection.id) : false;
        const installedConnection = isBuiltinConnection(type) && isInstalled
          ? installedConnections.find((installed) => installed.name === connection.name) || null
          : null;

        return (
          <ConnectionCard
            key={connection.id}
            connection={connection}
            isInstalled={isInstalled}
            installedConnection={installedConnection}
            isInstalling={isInstalling}
            isDeleting={isDeleting}
            onInstall={isBuiltinConnection(type) ? () => onInstall?.(connection) : undefined}
            onEdit={onEdit}
            onDelete={onDelete} // Use onDelete for both MCP and builtin connections
            onToolToggle={onToolToggle}
            onConnectionToggle={onConnectionToggle}
            onPermissionsChange={onPermissionsChange}
            showTools={true}
            showConnectionToggle={true}
          />
        );
      })}
    </div>
  );
}
