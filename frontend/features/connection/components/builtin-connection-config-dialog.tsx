'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Form } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { SchemaConnectionPublic } from '@/openapi-ts/gens';
import { useCallback, useEffect, useState } from 'react';
import { Loader2, Plus, X, Eye, EyeOff } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import { connectionQuery } from '../hooks/connection.query';
import { toast } from 'sonner';

// Types and Schema
const envVariableSchema = z.object({
  key: z.string().min(1, 'Environment variable name is required'),
  value: z.string().min(1, 'Environment variable value is required'),
});

const builtinConfigSchema = z.object({
  env_variables: z.array(envVariableSchema),
});

type BuiltinConfigFormData = z.infer<typeof builtinConfigSchema>;

interface EnvVariable {
  key: string;
  value: string;
}

interface BuiltinConnectionConfigDialogProps {
  readonly open?: boolean;
  readonly onOpenChange?: (open: boolean) => void;
  readonly connection?: SchemaConnectionPublic | null;
  readonly mode: 'install' | 'edit';
  readonly onInstall?: (connectionId: string, configOverride: Record<string, any>) => void;
}

// Environment Variables Manager Component (reused from edit dialog)
interface EnvVariablesManagerProps {
  envVariables: EnvVariable[];
  onEnvVariablesChange: (envVariables: EnvVariable[]) => void;
  mode?: 'install' | 'edit';
}

function EnvVariablesManager({ envVariables, onEnvVariablesChange, mode = 'install' }: EnvVariablesManagerProps) {
  const [visibilityState, setVisibilityState] = useState<Record<number, boolean>>({});

  const updateEnvVariable = useCallback((
    index: number,
    field: 'key' | 'value',
    value: string,
  ) => {
    const newEnvVariables = [...envVariables];
    newEnvVariables[index] = { ...newEnvVariables[index], [field]: value };
    onEnvVariablesChange(newEnvVariables);
  }, [envVariables, onEnvVariablesChange]);

  const handleValuePaste = useCallback((
    index: number,
    e: React.ClipboardEvent<HTMLInputElement>
  ) => {
    const pastedText = e.clipboardData.getData('text');
    if (pastedText.includes('\n')) {
      e.preventDefault();
      updateEnvVariable(index, 'value', pastedText);
    }
  }, [updateEnvVariable]);


  const toggleVisibility = useCallback((index: number) => {
    setVisibilityState(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  }, []);

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium">Environment Variables</h4>
      </div>

      <div className="space-y-2">
        {envVariables.length === 0 ? (
          <div className="rounded-lg border border-dashed p-4 text-center text-sm text-muted-foreground">
            No environment variables configured. Click "Add Variable" to get started.
          </div>
        ) : (
          <div className="space-y-2">
            {envVariables.map((envVar, index) => (
              <div
                key={index}
                className="flex items-center gap-2 rounded-lg border p-3"
              >
                <div className="flex-1">
                  <Input
                    placeholder="VARIABLE_NAME"
                    value={envVar.key}
                    onChange={(e) => updateEnvVariable(index, 'key', e.target.value)}
                    className="h-8 font-mono text-xs text-muted-foreground"
                    readOnly
                    disabled
                  />
                </div>
                <div className="flex-1 relative">
                  {envVar.value.includes('\n') || envVar.value.length > 100 ? (
                    <div className="relative">
                      <textarea
                        placeholder="variable_value (supports multiline YAML/JSON)"
                        value={envVar.value}
                        onChange={(e) => updateEnvVariable(index, 'value', e.target.value)}
                        className="min-h-[80px] w-full resize-none rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        style={{
                          filter: visibilityState[index] ? 'none' : 'blur(4px)',
                          fontFamily: 'monospace',
                          resize: 'none'
                        }}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleVisibility(index)}
                        className="absolute right-1 top-2 h-6 w-6 p-1 text-muted-foreground hover:text-foreground"
                        aria-label={visibilityState[index] ? "Hide value" : "Show value"}
                      >
                        {visibilityState[index] ? (
                          <EyeOff className="h-3 w-3" />
                        ) : (
                          <Eye className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                  ) : (
                    <>
                      <Input
                        placeholder="variable_value (paste multiline content to auto-expand)"
                        value={envVar.value}
                        onChange={(e) => updateEnvVariable(index, 'value', e.target.value)}
                        onPaste={(e) => handleValuePaste(index, e)}
                        className="h-8 pr-10 font-mono"
                        type={visibilityState[index] ? "text" : "password"}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleVisibility(index)}
                        className="absolute right-1 top-1/2 h-6 w-6 -translate-y-1/2 p-1 text-muted-foreground hover:text-foreground"
                        aria-label={visibilityState[index] ? "Hide value" : "Show value"}
                      >
                        {visibilityState[index] ? (
                          <EyeOff className="h-3 w-3" />
                        ) : (
                          <Eye className="h-3 w-3" />
                        )}
                      </Button>
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

// Main Component
export function BuiltinConnectionConfigDialog({
  open,
  onOpenChange,
  connection,
  mode,
  onInstall,
}: BuiltinConnectionConfigDialogProps) {
  const { mutate: updateConnection, isPending: isUpdating } = connectionQuery.mutation.useUpdate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<BuiltinConfigFormData>({
    resolver: zodResolver(builtinConfigSchema),
    defaultValues: {
      env_variables: [],
    },
  });

  const { watch, setValue, reset } = form;
  const watchedEnvVariables = watch('env_variables');

  // Initialize form with existing environment variables or start with one empty field
  useEffect(() => {
    if (open && connection) {
      const config = connection.config as any || {};
      const existingEnvVars = config.env || {};

      const envVariables = Object.keys(existingEnvVars).length > 0
        ? Object.entries(existingEnvVars).map(([key, value]) => ({
            key,
            value: String(value)
          }))
        : [{ key: '', value: '' }]; // Start with one empty field for both install and edit

      setValue('env_variables', envVariables);
    } else if (open) {
      // Reset form for new session
      setValue('env_variables', [{ key: '', value: '' }]);
    }
  }, [open, connection, setValue, mode]);

  const handleEnvVariablesChange = useCallback((envVariables: EnvVariable[]) => {
    setValue('env_variables', envVariables);
  }, [setValue]);

  const onSubmit = useCallback(async (data: BuiltinConfigFormData) => {
    if (!connection) return;

    try {
      setIsSubmitting(true);

      // Convert environment variables array to object, preserving structure
      const envObject = data.env_variables.reduce((acc, { key, value }) => {
        if (key.trim() && value.trim()) {
          // Preserve the original structure - don't trim multiline values
          acc[key.trim()] = value;
        }
        return acc;
      }, {} as Record<string, string>);

      if (mode === 'install') {
        // Handle installation with config override
        const configOverride = { env: envObject };
        onInstall?.(connection.id, configOverride);
        onOpenChange?.(false);
        reset();
      } else {
        // Handle edit mode - update existing connection
        const currentConfig = connection.config as any || {};
        const updateData = {
          config: {
            ...currentConfig,
            env: envObject,
          },
          type: connection.type,
          is_installed: true,
        };

        updateConnection({ id: connection.id, data: updateData }, {
          onSuccess: () => {
            onOpenChange?.(false);
            reset();
          },
        });
      }
    } catch (error) {
      toast.error(`Error ${mode === 'install' ? 'installing' : 'updating'} builtin connection: ${error}`);
    } finally {
      setIsSubmitting(false);
    }
  }, [connection, mode, onInstall, updateConnection, onOpenChange, reset]);

  const handleCancel = useCallback(() => {
    onOpenChange?.(false);
    reset();
  }, [onOpenChange, reset]);

  const isPending = isUpdating || isSubmitting;

  if (!connection) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader className="pb-2">
          <DialogTitle className="text-lg font-semibold">
            {mode === 'install' ? `Install ${connection.name}` : `Configure ${connection.name}`}
          </DialogTitle>
          <p className="text-sm text-muted-foreground">
            {mode === 'install'
              ? 'Set environment variables to install this builtin connection'
              : 'Set environment variables for this builtin connection'
            }
          </p>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <EnvVariablesManager
              envVariables={watchedEnvVariables}
              onEnvVariablesChange={handleEnvVariablesChange}
              mode={mode}
            />

            <DialogFooter className="pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isPending}
                className="h-9"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isPending}
                className="h-9 min-w-[120px]"
              >
                {isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {mode === 'install' ? 'Installing...' : 'Saving...'}
                  </>
                ) : (
                  mode === 'install' ? 'Install Connection' : 'Save Configuration'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
