'use client';

import { ConnectionType, SchemaConnectionPublic } from '@/openapi-ts/gens';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Loader2, Plus } from 'lucide-react';
import { useState } from 'react';
import { isMcpConnection } from '../utils/connection.utils';

import { ConnectionEmptyState } from './connection-empty-state';
import { ConnectionGrid } from './connection-grid';
import { CreateConnectionDialog } from './create-connection-dialog';
import { BuiltinConnectionConfigDialog } from './builtin-connection-config-dialog';
import { useConnectionList } from '../hooks/use-connection-list';
import { isBuiltinConnection } from '../utils/connection.utils';

interface ConnectionListProps {
  readonly className?: string;
  readonly type: ConnectionType;
}

export function ConnectionList({ className, type }: ConnectionListProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingConnection, setEditingConnection] = useState<SchemaConnectionPublic | null>(null);
  const [isBuiltinConfigDialogOpen, setIsBuiltinConfigDialogOpen] = useState(false);
  const [builtinConfigConnection, setBuiltinConfigConnection] = useState<SchemaConnectionPublic | null>(null);
  const [builtinConfigMode, setBuiltinConfigMode] = useState<'install' | 'edit'>('edit');

  const {
    connections,
    installedConnections,
    installedBuiltinIds,
    isInstalling,
    isDeleting,
    isLoading,
    error,
    handleInstall,
    handleDelete,
    handleToolToggle,
    handleConnectionToggle,
    handlePermissionsChange,
  } = useConnectionList({ type });

  const handleCreateClick = () => setIsCreateDialogOpen(true);

  const handleEditClick = (connection: SchemaConnectionPublic) => {
    if (isMcpConnection(connection.type)) {
      // Handle MCP connection edit - use the create dialog in edit mode
      setEditingConnection(connection);
      setIsCreateDialogOpen(true);
    } else if (isBuiltinConnection(connection.type)) {
      // Handle builtin connection edit - find the installed connection (user's instance)
      const installedConnection = installedConnections.find(
        (installed) => installed.name === connection.name
      );
      if (installedConnection) {
        setBuiltinConfigConnection(installedConnection);
        setBuiltinConfigMode('edit');
        setIsBuiltinConfigDialogOpen(true);
      }
    }
  };

  const handleInstallClick = (connection: SchemaConnectionPublic) => {
    if (isBuiltinConnection(connection.type)) {
      // Open config dialog for installation
      setBuiltinConfigConnection(connection);
      setBuiltinConfigMode('install');
      setIsBuiltinConfigDialogOpen(true);
    }
  };

  const handleMcpDialogClose = () => {
    setIsCreateDialogOpen(false);
    setEditingConnection(null);
  };

  const handleBuiltinConfigDialogClose = () => {
    setIsBuiltinConfigDialogOpen(false);
    setBuiltinConfigConnection(null);
  };

  if (connections){
    return (
      <>
        <div className="mt-2 mb-3 space-y-3">
          <div className="flex flex-wrap items-center justify-between gap-3">
            <Badge variant="ghost-primary" className="px-3 py-1">
              {connections.length} Connections
            </Badge>
            {isMcpConnection(type) && (
              <Button
                onClick={handleCreateClick}
                className="flex items-center gap-2"
                size="sm"
              >
                <Plus className="h-4 w-4" />
                New Connection
              </Button>
            )}
          </div>
        </div>
        <div className={`space-y-4 ${className}`}>
          {connections.length > 0 ? (
            <ConnectionGrid
              connections={connections}
              type={type}
              installedBuiltinIds={installedBuiltinIds}
              installedConnections={installedConnections}
              isInstalling={isInstalling}
              isDeleting={isDeleting}
              onInstall={handleInstallClick}
              onEdit={handleEditClick}
              onDelete={handleDelete}
              onToolToggle={handleToolToggle}
              onConnectionToggle={handleConnectionToggle}
              onPermissionsChange={handlePermissionsChange}
            />
          ) : (
            <ConnectionEmptyState
              type={type}
              onCreateClick={handleCreateClick}
            />
          )}

          {/* Create Connection Dialog - Only for MCP */}
          {isMcpConnection(type) && (
            <CreateConnectionDialog
              open={isCreateDialogOpen}
              onOpenChange={handleMcpDialogClose}
              editConnection={editingConnection}
            />
          )}

          {/* Builtin Connection Config Dialog - Only for Builtin (handles both install and edit) */}
          {isBuiltinConnection(type) && (
            <BuiltinConnectionConfigDialog
              open={isBuiltinConfigDialogOpen}
              onOpenChange={handleBuiltinConfigDialogClose}
              connection={builtinConfigConnection}
              mode={builtinConfigMode}
              onInstall={handleInstall}
            />
          )}
        </div>
      </>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center space-y-2">
          <h4 className="text-lg font-medium text-destructive">Error loading connections</h4>
          <p className="text-sm text-muted-foreground">
            {error instanceof Error ? error.message : 'An unexpected error occurred'}
          </p>
        </div>
      </div>
    );
  }
}
