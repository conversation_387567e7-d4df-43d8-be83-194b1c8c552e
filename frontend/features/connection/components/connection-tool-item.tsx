'use client';

import { Switch } from '@/components/ui/switch';
import { SchemaConnectionPublic } from '@/openapi-ts/gens';
import { useCallback } from 'react';
import { cn } from '@/lib/utils';
import { getPermissionStyles } from '../utils/connection.utils';

export interface ConnectionToolItemProps {
  readonly tool: string;
  readonly isEnabled: boolean;
  readonly displayName: string;
  readonly connection: SchemaConnectionPublic;
  readonly isInstalled: boolean;
  readonly onToggle?: (connection: SchemaConnectionPublic, tool: string, enabled: boolean) => void;
  readonly variant: 'installed' | 'mcp';
}

export function ConnectionToolItem({
  tool,
  isEnabled,
  displayName,
  connection,
  isInstalled,
  onToggle,
  variant
}: ConnectionToolItemProps) {
  const handleToggle = useCallback(() => {
    if (isInstalled && onToggle) {
      onToggle(connection, tool, !isEnabled);
    }
  }, [connection, tool, isEnabled, onToggle, isInstalled]);

  // Check if this tool requires permissions
  const requiresPermission = connection.tool_permissions?.includes(tool) || false;
  const permissionStyles = getPermissionStyles(requiresPermission, isEnabled);

  // For installed builtin connections - clickable buttons
  if (variant === 'installed' && isInstalled) {
    return (
      <button
        key={tool}
        type="button"
        className={cn(
          "flex items-center justify-between p-2 rounded-md border transition-all duration-200 relative",
          "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-1 focus:ring-offset-background",
          permissionStyles.container
        )}
        onClick={handleToggle}
        aria-pressed={isEnabled}
        aria-label={`Toggle ${displayName} tool`}
      >
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <span
            className={cn(
              "text-xs font-medium truncate",
              permissionStyles.text
            )}
          >
            {displayName}
          </span>
        </div>
      </button>
    );
  }

  // For uninstalled builtin connections - disabled display
  if (variant === 'installed' && !isInstalled) {
    return (
      <div
        key={tool}
        className="flex items-center justify-between p-2 rounded-md border bg-muted/30 border-muted cursor-not-allowed opacity-60"
      >
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <span className="text-xs font-medium truncate text-muted-foreground">
            {displayName}
          </span>
        </div>
      </div>
    );
  }

  // For MCP connections - with switch
  const mcpStyles = requiresPermission && isInstalled
    ? getPermissionStyles(requiresPermission).container
    : isInstalled
    ? "bg-muted/30 hover:bg-muted/50"
    : "bg-muted/20 border-muted cursor-not-allowed opacity-60";

  const mcpTextStyles = requiresPermission && isInstalled
    ? getPermissionStyles(requiresPermission).text
    : isInstalled
    ? "text-foreground"
    : "text-muted-foreground";

  return (
    <div
      key={tool}
      className={cn(
        "flex items-center justify-between p-2 rounded-md border transition-colors duration-200",
        mcpStyles
      )}
    >
      <div className="flex items-center gap-2 flex-1 min-w-0">
        <span className={cn(
          "text-sm font-medium truncate",
          mcpTextStyles
        )}>
          {displayName}
        </span>
      </div>
      {isInstalled && onToggle && (
        <Switch
          checked={isEnabled}
          onCheckedChange={(checked) => onToggle(connection, tool, checked)}
          className="flex-shrink-0 ml-2"
          aria-label={`Toggle ${displayName} tool`}
        />
      )}
    </div>
  );
}
