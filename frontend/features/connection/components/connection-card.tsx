'use client';

import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { ConnectionType, SchemaConnectionPublic } from '@/openapi-ts/gens';
import { ReactNode } from 'react';
import { cn } from '@/lib/utils';
import { Plug } from 'lucide-react';

// Import new sub-components
import { ConnectionCardHeader } from './connection-card-header';
import { ConnectionActionButtons } from './connection-action-buttons';
import { ConnectionToolsSection } from './connection-tools-section';
import { ConnectionToggle } from './connection-toggle';
import { isBuiltinConnection } from '../utils/connection.utils';


export interface ConnectionCardProps {
  readonly connection: SchemaConnectionPublic;
  readonly isInstalled?: boolean;
  readonly installedConnection?: SchemaConnectionPublic | null;
  readonly isInstalling?: boolean;
  readonly isDeleting?: boolean;
  readonly className?: string;

  // Action handlers
  readonly onInstall?: (connection: SchemaConnectionPublic) => void;
  readonly onEdit?: (connection: SchemaConnectionPublic) => void;
  readonly onDelete?: (connection: SchemaConnectionPublic) => void;
  readonly onToolToggle?: (connection: SchemaConnectionPublic, toolName: string, enabled: boolean) => void;
  readonly onConnectionToggle?: (connection: SchemaConnectionPublic, active: boolean) => void;
  readonly onPermissionsChange?: (connection: SchemaConnectionPublic, toolPermissions: string[]) => void;

  // Custom action buttons for different connection types
  readonly actionButtons?: ReactNode;
  readonly showTools?: boolean;
  readonly showConnectionToggle?: boolean;
}


export function ConnectionCard({
  connection,
  isInstalled = false,
  installedConnection = null,
  isInstalling = false,
  isDeleting = false,
  className,
  onInstall,
  onEdit,
  onDelete,
  onToolToggle,
  onConnectionToggle,
  onPermissionsChange,
  actionButtons,
  showTools = true,
  showConnectionToggle = true,
}: ConnectionCardProps) {
  const displayConnection = installedConnection || connection;
  const isBuiltin = isBuiltinConnection(connection.type);

  // For builtin connections: only show tools if installed
  // For other connections: show tools if they exist
  const shouldShowTools = showTools && (
    isBuiltin
      ? isInstalled && ((displayConnection.tool_list?.length ?? 0) > 0)
      : ((connection.tool_list?.length ?? 0) > 0 || (displayConnection.tool_list?.length ?? 0) > 0)
  );

  // Show "Ready to connect" for builtin connections that are not installed
  const shouldShowReadyToConnect = isBuiltin && !isInstalled;

  return (
    <Card className={cn("relative flex flex-col h-full", className)}>
      <CardHeader className="p-0 flex-shrink-0">
        <ConnectionCardHeader connection={connection} />
      </CardHeader>

      <CardContent className="flex flex-col flex-grow px-4 pb-3 pt-0">
        {/* Tools Section or Ready to Connect */}
        <div className="flex-grow">
          {shouldShowReadyToConnect ? (
            <div className="flex flex-col items-center justify-center py-6 text-center">
              <div className="flex items-center justify-center w-12 h-12 rounded-full bg-muted/50 mb-3">
                <Plug className="h-6 w-6 text-muted-foreground" />
              </div>
              <p className="text-sm text-muted-foreground font-medium">
                Ready to connect
              </p>
            </div>
          ) : shouldShowTools ? (
            <ConnectionToolsSection
              connection={connection}
              displayConnection={displayConnection}
              isInstalled={isInstalled}
              onToolToggle={onToolToggle}
              onPermissionsChange={onPermissionsChange}
            />
          ) : null}
        </div>

        {/* Bottom Action Section - Install/Uninstall and Toggle */}
        <div className="flex items-center justify-end pt-3 mt-auto">
          <div className="flex items-center gap-3">
            <ConnectionActionButtons
              connection={connection}
              isInstalled={isInstalled}
              isInstalling={isInstalling}
              isDeleting={isDeleting}
              onInstall={onInstall}
              onEdit={onEdit}
              onDelete={onDelete}
              actionButtons={actionButtons}
            />
            {/* <ConnectionToggle
              connection={displayConnection}
              isInstalled={isInstalled}
              onConnectionToggle={onConnectionToggle}
              showConnectionToggle={showConnectionToggle}
            /> */}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
