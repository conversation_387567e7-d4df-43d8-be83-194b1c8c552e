import { ConnectionTransport } from '@/openapi-ts/gens';
import { createUtilityConfig } from '@/utils/option-config';

export const CONNECTION_TRANSPORT_CONFIG = createUtilityConfig({
  [ConnectionTransport.streamable_http]: {
    label: 'Streamable HTTP',
    description: 'HTTP-based streaming connection',
    protocol: 'HTTP',
    supportsStreaming: true,
  },
  [ConnectionTransport.sse]: {
    label: 'Server-Sent Events',
    description: 'Server-sent events connection',
    protocol: 'SSE',
    supportsStreaming: true,
  },
} satisfies Record<ConnectionTransport, { 
  label: string; 
  description: string;
  protocol: string;
  supportsStreaming: boolean;
}>);