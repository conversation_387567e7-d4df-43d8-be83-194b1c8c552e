import React from 'react';

import { CodeBlock as UICodeBlock } from '@/components/ui/code-block';

import { MarkdownCodeBlockProps } from '../types';
import {
  childrenToString,
  extractLanguage,
  isMermaidContent,
  shouldRenderInline,
} from '../utils';
import { MarkdownMermaid } from './markdown-mermaid';

/**
 * Component for rendering code blocks in markdown
 * Handles both inline and block code, with Mermaid support
 */
export const MarkdownCodeBlock: React.FC<MarkdownCodeBlockProps> = ({
  className,
  children,
  inline,
  enableMermaid = true,
  isStreaming = false,
}) => {
  const language = extractLanguage(className);
  const codeString = childrenToString(children);
  const isInline = shouldRenderInline(children, className, inline);

  // Handle Mermaid diagrams
  if (
    enableMermaid &&
    (language === 'mermaid' || isMermaidContent(codeString))
  ) {
    return <MarkdownMermaid content={codeString} isStreaming={isStreaming} />;
  }

  // Handle inline code
  if (isInline) {
    return (
      <code className="bg-muted dark:bg-muted/90 text-foreground/90 dark:text-foreground/95 rounded-lg px-1.5 py-0.5 font-mono text-sm break-words">
        {codeString}
      </code>
    );
  }

  // Handle block code using the existing UI component
  return (
    <UICodeBlock
      code={codeString}
      language={language}
      showCopyButton={true}
      className="prose rounded-none border-none"
    />
  );
};
