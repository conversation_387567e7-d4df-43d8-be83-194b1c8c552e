import React, { useEffect, useRef, useState } from 'react';

import { But<PERSON> } from '@/components/ui/button';
import { CopyToClipboard } from '@/components/ui/copy-to-clipboard';
import { Mermaid } from '@/components/ui/mermaid';
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Download, PenTool, RotateCcw, ZoomIn, ZoomOut } from 'lucide-react';

import { MarkdownMermaidProps } from '../types';

/**
 * Component for rendering Mermaid diagrams in markdown
 */
export const MarkdownMermaid: React.FC<MarkdownMermaidProps> = ({
  content,
  isStreaming = false,
  className = '',
}) => {
  const [isMermaidComplete, setIsMermaidComplete] = useState(false);
  const [scale, setScale] = useState(1);
  const prevContentRef = useRef<string>('');

  useEffect(() => {
    if (content.includes('```') && !isMermaidComplete) {
      setIsMermaidComplete(true);
    }
    prevContentRef.current = content;
  }, [content, isMermaidComplete]);

  const handleDownload = async () => {
    try {
      // Get the SVG element
      const svgElement = document.querySelector('.mermaid-diagram svg');
      if (!svgElement) return;

      // Create a blob from the SVG
      const svgData = new XMLSerializer().serializeToString(svgElement);
      const blob = new Blob([svgData], { type: 'image/svg+xml' });

      // Create download link
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'mermaid-diagram.svg';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Failed to download diagram:', err);
    }
  };

  const handleZoomIn = () => {
    setScale((prev) => Math.min(prev + 0.25, 2.5));
  };

  const handleZoomOut = () => {
    setScale((prev) => Math.max(prev - 0.25, 0.5));
  };

  const handleZoomReset = () => {
    setScale(1);
  };

  // Show loading state only when streaming and diagram is not complete
  if (isStreaming && !isMermaidComplete) {
    return (
      <div
        className={`border-border bg-background relative min-h-[100px] w-full max-w-full overflow-hidden rounded-lg border p-6 ${className}`}
      >
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="flex items-center gap-1.5">
            <div className="bg-primary/50 h-1.5 w-1.5 animate-pulse rounded-full"></div>
            <div className="bg-primary/50 h-1.5 w-1.5 animate-pulse rounded-full [animation-delay:0.2s]"></div>
            <div className="bg-primary/50 h-1.5 w-1.5 animate-pulse rounded-full [animation-delay:0.4s]"></div>
          </div>
        </div>
        {/* Keep the partial content but hide it */}
        <div className="h-0 overflow-hidden opacity-0">
          <Mermaid content={prevContentRef.current || content} />
        </div>
      </div>
    );
  }

  const mermaidContent = (
    <div
      style={{
        transform: `scale(${scale})`,
        transformOrigin: 'top left',
        transition: 'transform 0.2s ease',
      }}
    >
      <Mermaid content={content} />
    </div>
  );

  return (
    <div
      className={`border-border bg-background relative min-h-[100px] w-full max-w-full overflow-hidden rounded-lg border ${className}`}
    >
      {/* Mermaid Header */}
      <div className="border-border bg-muted/50 flex min-w-0 items-center justify-between border-b px-4 py-2">
        <div className="flex min-w-0 flex-1 items-center gap-2">
          <PenTool className="h-4 w-4" />
          <span className="text-muted-foreground truncate text-xs font-medium uppercase">
            mermaid
          </span>
        </div>
        <div className="flex shrink-0 items-center gap-1">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-7 w-7 p-0"
                  onClick={handleZoomIn}
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top">Zoom In</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-7 w-7 p-0"
                  onClick={handleZoomOut}
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top">Zoom Out</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-7 w-7 p-0"
                  onClick={handleZoomReset}
                >
                  <RotateCcw className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top">Reset Zoom</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-7 w-7 p-0"
                  onClick={handleDownload}
                >
                  <Download className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top">Download SVG</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <CopyToClipboard text={content} />
        </div>
      </div>

      {/* Mermaid Content */}
      <div className="max-w-full overflow-auto p-6">{mermaidContent}</div>
    </div>
  );
};
