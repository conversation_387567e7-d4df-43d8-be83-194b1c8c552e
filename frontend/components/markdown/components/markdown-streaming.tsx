import React from 'react';
import { MessageLoading } from '@/components/ui/message-loading';
import { useMarkdownStreaming } from '../hooks/use-markdown-streaming';
import { MarkdownStreamingProps } from '../types';
import { highlightMentions } from '../utils';

/**
 * Component for rendering streaming markdown content
 */
export const MarkdownStreaming: React.FC<MarkdownStreamingProps> = ({
  content,
  isStreaming,
  revealDelay = 16,
  className = '',
}) => {
  const { visibleContent, isComplete } = useMarkdownStreaming({
    content,
    isStreaming,
    revealDelay,
  });

  if (!content) {
    return null;
  }

  // For status messages, show loading indicator
  if (isStreaming && !isComplete && content.length < 50) {
    return (
      <div className="flex items-center gap-2">
        <MessageLoading />
        <span>{visibleContent}</span>
      </div>
    );
  }

  // For regular streaming content, show with mention highlighting
  const { highlightedText } = highlightMentions(visibleContent);

  return (
    <div className={`break-words whitespace-pre-wrap ${className}`}>
      {highlightedText}
    </div>
  );
};