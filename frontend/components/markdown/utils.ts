import React from 'react';
import { MentionHighlightResult } from './types';

/**
 * Check if content contains markdown patterns
 */
export const hasMarkdown = (content: string): boolean => {
  if (!content || typeof content !== 'string') return false;

  // Clean the content first
  const cleanContent = content.trim();
  if (!cleanContent) return false;

  const markdownPatterns = [
    /[*_~`]/, // Basic formatting
    /\[.*?\]\(.*?\)/, // Links (non-greedy match)
    /^>|\n>/, // Blockquotes (including those after newlines)
    /^#{1,6}\s|\n#{1,6}\s/, // Headers with space (including those after newlines)
    /^[-*+]\s|\n[-*+]\s/, // Lists with space (including those after newlines)
    /```[\s\S]*?```/, // Code blocks (non-greedy match)
    /\|.*\|/, // Tables
    /^\s*[-*_]{3,}/, // Horizontal rules
    /!\[.*?\]\(.*?\)/, // Images (non-greedy match)
  ];

  return markdownPatterns.some((pattern) => pattern.test(cleanContent));
};

/**
 * Normalize header formatting by adding space after # if not present
 */
export const normalizeHeaders = (content: string): string => {
  return content.replace(/^(#{1,6})([^#\s])/gm, '$1 $2');
};

/**
 * Highlight @mentions in text
 */
export const highlightMentions = (text: string): MentionHighlightResult => {
  const mentionRegex = /@(\w+)/g;
  const mentions: string[] = [];
  let match;

  // Extract all mentions
  while ((match = mentionRegex.exec(text)) !== null) {
    mentions.push(match[1]);
  }

  // If no mentions found, return original text
  if (mentions.length === 0) {
    return {
      highlightedText: text,
      mentions: [],
    };
  }

  // Split text and highlight mentions
  const parts = text.split(mentionRegex);
  const highlightedText = parts.map((part, index) => {
    if (index % 2 === 1) {
      // This is a mention (odd indices after split)
      return React.createElement(
        'span',
        {
          key: index,
          className: 'bg-primary/20 text-primary rounded px-1 font-medium',
        },
        `@${part}`
      );
    }
    return part;
  });

  return {
    highlightedText,
    mentions,
  };
};

/**
 * Detect if content is a Mermaid diagram
 */
export const isMermaidContent = (content: string): boolean => {
  if (!content) return false;
  
  const mermaidPatterns = [
    /^\s*graph\s+(TD|TB|BT|RL|LR)/i,
    /^\s*flowchart\s+(TD|TB|BT|RL|LR)/i,
    /^\s*sequenceDiagram/i,
    /^\s*classDiagram/i,
    /^\s*stateDiagram/i,
    /^\s*erDiagram/i,
    /^\s*journey/i,
    /^\s*gantt/i,
    /^\s*pie/i,
    /^\s*gitgraph/i,
  ];

  return mermaidPatterns.some(pattern => pattern.test(content));
};

/**
 * Extract language from className (e.g., language-js)
 */
export const extractLanguage = (className?: string): string => {
  if (!className) return 'text';
  const match = /language-(\w+)/.exec(className);
  return match ? match[1] : 'text';
};

/**
 * Convert children to string
 */
export const childrenToString = (children: React.ReactNode): string => {
  if (typeof children === 'string') {
    return children;
  }
  if (Array.isArray(children)) {
    return children.join('');
  }
  if (children) {
    return String(children);
  }
  return '';
};

/**
 * Sanitize content for safe rendering
 */
export const sanitizeContent = (content: string): string => {
  if (!content || typeof content !== 'string') return '';
  return content.trim();
};

/**
 * Check if code should be rendered inline
 */
export const shouldRenderInline = (
  children: React.ReactNode,
  className?: string,
  inline?: boolean
): boolean => {
  const childrenAsString = childrenToString(children);
  const hasLanguageClass = className && className.startsWith('language-');
  const hasNewlines = childrenAsString.includes('\n');
  const isShortCode = childrenAsString.length < 100;

  return (
    inline ||
    (!hasLanguageClass && !hasNewlines) ||
    (!hasLanguageClass && isShortCode)
  );
};

/**
 * Process content for streaming
 */
export const processStreamingContent = (
  content: string,
  visibleLength: number
): string => {
  return content.slice(0, visibleLength);
};

/**
 * Calculate next visible length for streaming
 */
export const calculateNextVisibleLength = (
  currentLength: number,
  targetLength: number
): number => {
  if (currentLength >= targetLength) return currentLength;
  return currentLength + Math.max(1, Math.floor((targetLength - currentLength) / 8));
};