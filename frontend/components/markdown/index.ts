// Main components
export { MarkdownRenderer } from './components/markdown-renderer';
export { MarkdownCodeBlock } from './components/markdown-code-block';
export { MarkdownMermaid } from './components/markdown-mermaid';
export { MarkdownStreaming } from './components/markdown-streaming';

// Hooks
export { useMarkdownStreaming } from './hooks/use-markdown-streaming';
export type {
  UseMarkdownStreamingOptions,
  UseMarkdownStreamingReturn,
} from './hooks/use-markdown-streaming';

// Types
export type {
  MarkdownRendererProps,
  MarkdownCodeBlockProps,
  MarkdownMermaidProps,
  MarkdownStreamingProps,
  MarkdownTheme,
  MarkdownComponents,
  MentionHighlightResult,
  StreamingState,
} from './types';

// Utils
export {
  hasMarkdown,
  normalizeHeaders,
  highlightMentions,
  isMermaidContent,
  extractLanguage,
  childrenToString,
  sanitizeContent,
  shouldRenderInline,
  processStreamingContent,
  calculateNextVisibleLength,
} from './utils';

// Themes
export { getTheme } from './config/markdown-themes';
