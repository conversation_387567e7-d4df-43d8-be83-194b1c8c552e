import { useEffect, useState } from 'react';
import { calculateNextVisibleLength } from '../utils';
import { StreamingState } from '../types';

export interface UseMarkdownStreamingOptions {
  content: string;
  isStreaming: boolean;
  revealDelay?: number;
}

export interface UseMarkdownStreamingReturn {
  visibleContent: string;
  isComplete: boolean;
  progress: number;
}

/**
 * Hook for smooth streaming text rendering
 * Reveals text incrementally up to the received content
 */
export const useMarkdownStreaming = ({
  content,
  isStreaming,
  revealDelay = 0,
}: UseMarkdownStreamingOptions): UseMarkdownStreamingReturn => {
  const [state, setState] = useState<StreamingState>({
    visibleLength: isStreaming ? 0 : content.length,
    isComplete: !isStreaming,
  });

  useEffect(() => {
    if (!isStreaming) {
      setState({
        visibleLength: content.length,
        isComplete: true,
      });
      return;
    }

    // If content shrinks (shouldn't happen, but just in case)
    if (state.visibleLength > content.length) {
      setState({
        visibleLength: content.length,
        isComplete: true,
      });
      return;
    }

    // If we've revealed all content, mark as complete
    if (state.visibleLength >= content.length) {
      setState(prev => ({
        ...prev,
        isComplete: true,
      }));
      return;
    }

    // Set up interval to reveal more content
    const interval = setInterval(() => {
      setState(prev => {
        if (prev.visibleLength >= content.length) {
          return {
            ...prev,
            isComplete: true,
          };
        }

        const nextLength = calculateNextVisibleLength(
          prev.visibleLength,
          content.length
        );

        return {
          visibleLength: nextLength,
          isComplete: nextLength >= content.length,
        };
      });
    }, revealDelay);

    return () => clearInterval(interval);
  }, [content, isStreaming, state.visibleLength, revealDelay]);

  // When stream completes, reveal all content
  useEffect(() => {
    if (!isStreaming) {
      setState({
        visibleLength: content.length,
        isComplete: true,
      });
    }
  }, [isStreaming, content.length]);

  const visibleContent = content.slice(0, state.visibleLength);
  const progress = content.length > 0 ? state.visibleLength / content.length : 1;

  return {
    visibleContent,
    isComplete: state.isComplete,
    progress,
  };
};