'use client';

import Link from 'next/link';

import {
  Too<PERSON><PERSON>,
  Tooltip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import pathsConfig from '@/config/paths.config';
import { cn } from '@/lib/utils';
import pluralize from 'pluralize';

type Props = {
  quotaInfo: {
    quota_used: number;
    quota_limit: number;
    quota_remaining: number;
    usage_percentage: number;
  };
};

export function QuotaIndicator({ quotaInfo }: Props) {
  const messagesRemaining = quotaInfo.quota_remaining;

  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip>
        <TooltipTrigger asChild>
          <Link
            href={pathsConfig.app.dashboard + '?tab=usage'}
            passHref
            target="_blank"
          >
            <div
              className={cn(
                'card flex items-center gap-2 px-3 py-2 text-xs font-medium',
                'cursor-pointer rounded-lg border',
                'hover:scale-[1.02] hover:shadow-sm',
                'bg-primary/10 border-primary/20 text-primary hover:text-primary/80',
              )}
            >
              <div className="bg-primary h-2 w-2 flex-shrink-0 rounded-full" />
              <div className="min-w-0 flex-1">
                <div className="truncate font-medium">
                  {messagesRemaining.toLocaleString()}{' '}
                  {pluralize('message', messagesRemaining)} left
                </div>
              </div>
            </div>
          </Link>
        </TooltipTrigger>
        <TooltipContent side="top" align="center">
          <p>View usage details</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
