// components/chat/types.ts
import { MessageDisplayComponentPublic } from '@/client/types.gen';
import { InterruptConfirmation } from '@/hooks/message-stream';

export enum ToolCallStatus {
  Complete = 'completed',
  Error = 'error',
  Timeout = 'timeout',
  Running = 'running',
  Success = 'success',
}

export interface ToolCall {
  id: string;
  name: string;
  arguments: Record<string, never>;
  output?: string;
  status: ToolCallStatus;
  startTime?: Date;
  reasoning?: string;
  position?: number;
  thought?: string;
  agentRole?: string;
  agentName?: string;
}

// Attachment interface for chat messages
export interface MessageAttachment {
  id: string;
  filename: string;
  original_filename: string;
  file_type: string;
  file_size: number;
  storage_key: string;
  created_at: string;
}

// Update Message interface
export interface Message {
  id: string;
  role: string;
  content: string;
  toolCalls?: ToolCall[];
  timestamp: Date;
  recommendationResponse?: RecommendationResponse | null;
  displayComponents?: MessageDisplayComponentPublic[];
  confirmation?: InterruptConfirmation | null;
  isInterrupt?: boolean;
  interruptMessage?: string;
  attachments?: MessageAttachment[];
  attachmentIds?: string[]; // For newly sent messages before attachments are loaded
}

export interface Session {
  id: string;
  title: string;
  timestamp: Date;
  category?: string;
  lastMessage?: Message;
  resource?: {
    id: string;
    name: string;
    arn: string;
    type: string;
    region: string;
    description?: string | null;
  } | null;
}

export interface RecommendationData {
  type: string;
  title: string;
  description: string;
  potential_savings: number;
  effort: string;
  risk: string;
  status: string;
}

export interface RecommendationResponse {
  recommendations: RecommendationData[];
}

// Resource types for # mention feature
export interface ResourceItem {
  id: string;
  title: string;
  description: string;
  subtitle?: string;
  metadata?: ResourceItemMetadata;
}

export interface ResourceCategory {
  id: string;
  name: string;
  icon: React.ReactNode;
  items: ResourceItem[];
  isDynamic?: boolean;
  source?: string;
}

// Props for sub-components
export interface ModelSelectorProps {
  selectedModelId: string;
  onSelectModel: (modelId: string) => void;
  isDropdownOpen: boolean;
  setIsDropdownOpen: (isOpen: boolean) => void;
  models: Array<{
    id: string;
    name: string;
    version?: string;
    locked?: boolean;
  }>;
}

export interface MentionDropdownProps {
  isVisible: boolean;
  position: { top: number; left: number };
  filter: string;
  onSelect: (itemId: string, fullPath: string) => void;
  onClose: () => void;
}

export interface ResourceMentionProps extends MentionDropdownProps {
  categories: ResourceCategory[];
  dropdownRef?: React.RefObject<HTMLDivElement | null>;
}

export interface ActionButtonProps {
  isStreaming: boolean;
  hasContent: boolean;
  disabled?: boolean;
  onSubmit: () => void;
  onStop?: () => void;
}

// Update the ResourceItemMetadata interface to include usage_mode and other fields
export interface ResourceItemMetadata {
  access_level: 'private' | 'shared';
  usage_mode: 'manual' | 'agent_requested' | 'always';
  tags: string[];
  createdAt: string;
  updatedAt: string;
  formattedCreatedAt: string;
  formattedUpdatedAt: string;
  fullDescription: string;
  allowed_users: string[];
}
