'use client';

import { motion } from 'framer-motion';

type Props = {
  label?: string;
};

export const ThinkingAnimation = ({ label = 'Thinking' }: Props) => {
  return (
    <motion.div
      className="flex items-center"
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{
        opacity: 1,
        scale: 1,
      }}
      transition={{
        opacity: { duration: 0.3 },
        scale: { duration: 0.3, type: 'spring', stiffness: 300 },
      }}
    >
      <div className="relative">
        <motion.div
          className="bg-primary/20 absolute inset-0 rounded-full"
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.3, 0, 0.3],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: 'easeOut',
          }}
        />
      </div>

      <div className="flex items-center gap-2">
        <motion.p
          className="text-primary text-sm font-medium"
          animate={{ opacity: [0.7, 1, 0.7] }}
          transition={{ duration: 1.5, repeat: Infinity, ease: 'easeInOut' }}
        >
          {label}
        </motion.p>

        {/* <div className="flex space-x-1">
          {[0, 1, 2].map((index) => (
            <motion.div
              key={index}
              className="bg-primary size-1.5 rounded-full"
              animate={{
                y: [2, -4, 2],
                opacity: [0.4, 1, 0.4],
              }}
              transition={{
                duration: 1.2,
                repeat: Infinity,
                delay: index * 0.15,
                ease: 'easeInOut',
              }}
            />
          ))}
        </div> */}
      </div>
    </motion.div>
  );
};
