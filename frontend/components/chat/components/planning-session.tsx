'use client';

import { useMemo, useState } from 'react';

import { useChatContext } from '@/features/chat/context/chat-context';
import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import { AnimatePresence, motion } from 'framer-motion';
import {
  AlertCircle,
  CheckCircle2,
  ChevronDown,
  ChevronUp,
  Circle,
} from 'lucide-react';

import { BotAvatar } from './common/bot-icon';

export function PlanningSession() {
  const [isExpanded, setIsExpanded] = useState(false);
  const { planningContent } = useChatContext();

  const planningTasks = useMemo(
    () => planningContent?.planning || [],
    [planningContent],
  );

  const hasActivePlanning = useMemo(
    () => planningTasks.length > 0,
    [planningTasks],
  );

  const completedItems = useMemo(
    () => planningTasks.filter((item) => item.status === 'completed'),
    [planningTasks],
  );

  const totalItems = planningTasks.length;
  const completedCount = completedItems.length;
  const progressPercentage =
    totalItems > 0 ? (completedCount / totalItems) * 100 : 0;

  const currentItem = useMemo(
    () => planningTasks.find((item) => item.status !== 'completed'),
    [planningTasks],
  );

  const isCompleted = totalItems > 0 && completedCount === totalItems;

  const hideWhenCompleted = isCompleted && progressPercentage === 100;

  return (
    <If condition={hasActivePlanning && !hideWhenCompleted}>
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        className={cn(
          'border-muted-foreground/10 bg-background/50 mb-4 rounded-lg border p-3 backdrop-blur-sm transition-all duration-300',
          isExpanded && 'max-h-[40vh] overflow-y-auto',
        )}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between gap-2">
            <div className="flex items-center gap-2 overflow-hidden">
              <BotAvatar
                role={planningContent?.agent_name || 'assistant'}
                variant="compact"
                hideRoleText={true}
              />
              <div className="min-w-0 flex-1">
                <span className="text-foreground truncate text-sm font-medium">
                  {currentItem?.content || 'Planning Session'}
                </span>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-6 w-6 shrink-0"
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </div>

          <If condition={isExpanded}>
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span className="text-muted-foreground">
                  {completedCount}/{totalItems} items
                </span>
                <span
                  className={cn('font-medium', isCompleted && 'text-green-500')}
                >
                  {isCompleted
                    ? 'Completed'
                    : `${Math.round(progressPercentage)}%`}
                </span>
              </div>
              <Progress
                value={progressPercentage}
                className={cn('h-1.5', isCompleted && '[&>div]:bg-green-500')}
              />
            </div>
          </If>

          <AnimatePresence>
            <If condition={isExpanded}>
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="overflow-hidden pt-2"
              >
                <div className="border-muted-foreground/10 space-y-2 border-t pt-2">
                  {planningTasks.map((item) => (
                    <div
                      key={item.id}
                      className="flex items-start gap-3 text-sm"
                    >
                      <div
                        className={cn(
                          'mt-0.5 shrink-0',
                          item.status === 'completed' && 'text-green-500',
                          item.status === 'in_progress' && 'text-blue-500',
                          item.status === 'cancelled' && 'text-red-500',
                          item.status === 'pending' && 'text-gray-400',
                        )}
                      >
                        {item.status === 'completed' && (
                          <CheckCircle2 className="h-4 w-4" />
                        )}
                        {item.status === 'cancelled' && (
                          <AlertCircle className="h-4 w-4" />
                        )}
                        {(item.status === 'pending' ||
                          item.status === 'in_progress') && (
                          <Circle className="h-4 w-4" />
                        )}
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="text-foreground">{item.content}</p>
                        <If condition={item.notes}>
                          {(notes) => (
                            <p className="text-muted-foreground mt-1 text-xs">
                              {notes}
                            </p>
                          )}
                        </If>
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>
            </If>
          </AnimatePresence>
        </div>
      </motion.div>
    </If>
  );
}
