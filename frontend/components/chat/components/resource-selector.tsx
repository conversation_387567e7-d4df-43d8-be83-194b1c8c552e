'use client';

import React, { createElement, useCallback, useEffect, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { RESOURCE_TYPE_CONFIG } from '@/features/resource/config/resource-type.config';
import { If } from '@/components/ui/common/if';
import { resourceQuery } from '@/features/resource/hooks/resource.query';
import { cn } from '@/lib/utils';
import { SchemaResourcePublic } from '@/openapi-ts/gens';
import { Loader2, Search } from 'lucide-react';
import { useInView } from 'react-intersection-observer';

import { RESOURCE_STATUS_CONFIG } from '@/features/resource/config/resource-status.config';

interface ResourceSelectorProps {
  onResourceSelect: (resourceId: string | null) => void;
  selectedResourceId: string | null;
}

export function ResourceSelector({
  onResourceSelect,
  selectedResourceId,
}: ResourceSelectorProps) {
  const [searchInput, setSearchInput] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');

  // Debounce search input with 200ms delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchInput);
    }, 200);

    return () => clearTimeout(timer);
  }, [searchInput]);

  const {
    data: resourcesData,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = resourceQuery.query.useInfiniteList({
    name: debouncedSearchQuery || undefined, // Only pass name if there's a search query
  });

  const { ref: loadMoreRef, inView } = useInView({
    threshold: 0.1,
    rootMargin: '100px',
  });

  // Load more when in view
  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage, isFetchingNextPage, fetchNextPage]);

  const resources =
    resourcesData?.pages.flatMap((page) => page.data) || [];

  const handleResourceToggle = (resource: SchemaResourcePublic) => {
    if (selectedResourceId === resource.id) {
      // Unselect if already selected
      onResourceSelect(null);
    } else {
      // Select this resource
      onResourceSelect(resource.id);
    }
  };

  if (isLoading && resources.length === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2 text-sm text-muted-foreground">
          Loading resources...
        </span>
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col">
      {/* Search Header */}
      <div className="border-b p-3">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search resources..."
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            className="pl-9 h-8"
          />
        </div>
        {debouncedSearchQuery && (
          <div className="mt-2 flex items-center gap-2">
            <span className="text-xs text-muted-foreground">
              Searching for: "{debouncedSearchQuery}"
            </span>
            {resources.length > 0 && (
              <div className="bg-primary/10 text-primary rounded-full px-2 py-0.5 text-xs">
                {resources.length} {resources.length === 1 ? 'result' : 'results'}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Resource List */}
      <ScrollArea className="flex-1">
        <div className="space-y-0.5 p-1">
          <If
            condition={resources.length > 0}
            fallback={
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <div className="bg-muted/50 mb-2 flex h-10 w-10 items-center justify-center rounded-full">
                  <Search className="text-muted-foreground/70 h-5 w-5" />
                </div>
                <p className="text-muted-foreground text-sm">
                  {isLoading ? 'Loading resources...' : 
                   debouncedSearchQuery ? 'No resources found' : 'No resources available'}
                </p>
                {debouncedSearchQuery && !isLoading && (
                  <p className="text-muted-foreground/70 mt-1 max-w-[80%] text-xs">
                    Try a different search term
                  </p>
                )}
              </div>
            }
          >
            {resources.map((resource) => (
              <ResourceItem
                key={resource.id}
                resource={resource}
                isSelected={selectedResourceId === resource.id}
                onToggle={() => handleResourceToggle(resource)}
              />
            ))}

            {/* Load More Trigger */}
            {hasNextPage && (
              <div
                ref={loadMoreRef}
                className="flex min-h-[40px] items-center justify-center py-4"
              >
                {isFetchingNextPage ? (
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Loading more resources...
                  </div>
                ) : (
                  <div className="h-4" />
                )}
              </div>
            )}

            {!hasNextPage && resources.length > 0 && (
              <div className="my-4 h-4 text-center text-sm text-muted-foreground">
                No more resources
              </div>
            )}
          </If>
        </div>
      </ScrollArea>
    </div>
  );
}

interface ResourceItemProps {
  resource: SchemaResourcePublic;
  isSelected: boolean;
  onToggle: () => void;
}

function ResourceItem({ resource, isSelected, onToggle }: ResourceItemProps) {
  const statusConfig = RESOURCE_STATUS_CONFIG.CONFIG[resource.status];
  const { icon } = RESOURCE_TYPE_CONFIG.CONFIG[resource.type];

  return (
    <div
      className={cn(
        "flex cursor-pointer items-center gap-3 rounded-lg p-2 transition-colors hover:bg-muted/50",
        isSelected ? "bg-muted/50" : ""
      )}
      onClick={onToggle}
    >
      <Checkbox checked={isSelected} />
      
      <div className="bg-background/60 flex h-6 w-6 shrink-0 items-center justify-center rounded-lg">
        {createElement(icon, { className: 'h-4 w-4 text-foreground' })}
      </div>
      
      <div className="flex-1 space-y-1">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium leading-none text-foreground truncate">{resource.name}</h4>
          <Badge variant={statusConfig.variant} className="text-xs ml-2 shrink-0">
            {statusConfig.label}
          </Badge>
        </div>
        
        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
          <span className="truncate">{resource.type}</span>
          {resource.region && (
            <>
              <span>•</span>
              <span className="truncate">{resource.region}</span>
            </>
          )}
        </div>
      </div>
    </div>
  );
}