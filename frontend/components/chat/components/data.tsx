import React from 'react';

import { ResourceCategory } from '@/components/chat/types';
import { BookOpen } from 'lucide-react';

// Resource categories for # mention feature
export const resourceCategories: ResourceCategory[] = [
  {
    id: 'documents',
    name: 'Knowledge Base',
    icon: <BookOpen className="h-5 w-5 text-blue-500" />,
    items: [
      // These will be dynamically populated from KB collections
      // For now, keep a placeholder to maintain structure
      {
        id: 'kb_collections',
        title: 'Knowledge Base',
        description: 'Search your knowledge base collections',
      },
    ],
    isDynamic: true, // Flag to indicate this category should be populated dynamically
    source: 'kb_collections', // Source identifier for the data
  },
];
