'use client';

import { useEffect, useRef, useState } from 'react';

import { createPortal } from 'react-dom';

import { MentionDropdownProps } from '@/components/chat/types';
import { If } from '@/components/ui/common/if';
import { WithTooltip } from '@/components/ui/tooltip';
import { agentQuery } from '@/features/agent/hooks/agent.query';
import { cn } from '@/lib/utils';
import { Info, Lock, User } from 'lucide-react';

export function AgentMentionDropdown({
  isVisible,
  position,
  filter,
  onSelect,
  onClose,
}: MentionDropdownProps) {
  const [highlightedIndex, setHighlightedIndex] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const agentItemsRef = useRef<Array<HTMLDivElement | null>>([]);

  // Use our custom hook with caching
  const { data: agentsResponse } = agentQuery.query.useList();
  const agentsData = agentsResponse?.data;

  // Filter agents based on input - search both name and description
  const filteredAgents = Array.isArray(agentsData)
    ? agentsData
        .filter(
          (agent) => agent.type === 'conversation_agent' && agent.is_active,
        ) // Only show active conversation agents
        .filter((agent) => {
          const lowercaseFilter = filter.toLowerCase();
          return (
            agent.title?.toLowerCase().includes(lowercaseFilter) ||
            (agent.goal && agent.goal.toLowerCase().includes(lowercaseFilter))
          );
        })
        .map((agent) => ({
          id: agent.id,
          name: agent.title || '',
          role: agent.role,
          goal: agent.goal,
          instructions: agent.instructions,
          avatar: `/avatars/${agent.title?.toLowerCase().split(' ')[0]}.webp`,
          locked: false,
        }))
    : [];

  // Reset highlighted index when filtered data changes
  useEffect(() => {
    setHighlightedIndex(0);
  }, [filteredAgents.length]);

  // Initialize when dropdown becomes visible
  useEffect(() => {
    if (isVisible) {
      setHighlightedIndex(0);
    }
  }, [isVisible]);

  // Handle keyboard events
  useEffect(() => {
    if (!isVisible) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowUp') {
        e.preventDefault();
        setHighlightedIndex((prev) =>
          prev > 0 ? prev - 1 : filteredAgents.length - 1,
        );
      } else if (e.key === 'ArrowDown') {
        e.preventDefault();
        setHighlightedIndex((prev) =>
          prev < filteredAgents.length - 1 ? prev + 1 : 0,
        );
      } else if (e.key === 'Enter') {
        e.preventDefault();
        const agent = filteredAgents[highlightedIndex];
        if (agent && !agent.locked) {
          onSelect(agent.id, agent.name);
        }
      } else if (e.key === 'Escape') {
        e.preventDefault();
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isVisible, filteredAgents, highlightedIndex, onSelect, onClose]);

  // Ensure the highlighted item is visible
  useEffect(() => {
    if (!isVisible || filteredAgents.length === 0) return;

    const highlightedElement = agentItemsRef.current[highlightedIndex];
    const containerElement = containerRef.current;

    if (highlightedElement && containerElement) {
      const containerRect = containerElement.getBoundingClientRect();
      const elementRect = highlightedElement.getBoundingClientRect();

      if (elementRect.bottom > containerRect.bottom) {
        containerElement.scrollTop += elementRect.bottom - containerRect.bottom;
      } else if (elementRect.top < containerRect.top) {
        containerElement.scrollTop -= containerRect.top - elementRect.top;
      }
    }
  }, [highlightedIndex, isVisible, filteredAgents.length]);

  if (!isVisible) return null;

  const dropdown = (
    <div
      className={cn(
        'bg-background/95 fixed z-50 backdrop-blur-xs',
        'border-muted-foreground/10 border',
        'rounded-lg shadow-lg',
        'max-h-[320px] w-[500px] overflow-hidden',
        'transition-all duration-200 ease-in-out',
        'animate-in fade-in-50 zoom-in-95',
      )}
      style={{
        top: `${position.top}px`,
        left: `${position.left}px`,
      }}
    >
      <div className="border-muted-foreground/10 bg-muted/30 flex items-center border-b p-3">
        <span className="text-sm font-medium">Mention an active agent</span>
        <div className="bg-primary/10 text-primary ml-2 rounded-full px-2 py-0.5 text-xs">
          {filteredAgents.length}{' '}
          {filteredAgents.length === 1 ? 'agent' : 'agents'}
        </div>
      </div>

      <div
        ref={containerRef}
        className="custom-scrollbar max-h-[240px] overflow-y-scroll py-1.5"
      >
        <If
          condition={filteredAgents.length > 0}
          fallback={
            <div className="flex flex-col items-center justify-center py-6 text-center">
              <div className="bg-muted/50 mb-2 flex h-10 w-10 items-center justify-center rounded-full">
                <User className="text-muted-foreground/70 h-5 w-5" />
              </div>
              <p className="text-muted-foreground text-sm">
                No active agents found
              </p>
              <p className="text-muted-foreground/70 mt-1 max-w-[80%] text-xs">
                Try a different search term or activate some agents
              </p>
            </div>
          }
        >
          {filteredAgents.map((agent, index) => (
            <WithTooltip
              key={agent.id}
              contentClassName={cn(
                'bg-black/80 text-white',
                'p-2',
                'max-w-[280px] text-xs',
              )}
              tooltip={
                <If
                  condition={agent.locked}
                  fallback={
                    <>
                      <p className="mb-1 font-semibold">
                        What this agent can do:
                      </p>
                      <p>
                        {agent.goal ||
                          'Help with various tasks related to their expertise.'}
                      </p>
                    </>
                  }
                >
                  This agent is locked. Upgrade to access their expertise.
                </If>
              }
            >
              <div
                key={agent.id}
                ref={(el) => {
                  agentItemsRef.current[index] = el;
                }}
                className={cn(
                  'flex items-center justify-between px-4 py-3',
                  'hover:bg-muted/50 transition-colors duration-150',
                  'relative border-l-2',
                  index === highlightedIndex
                    ? 'border-primary bg-muted/50'
                    : 'border-transparent',
                  agent.locked && 'cursor-not-allowed opacity-70',
                )}
                onClick={() => {
                  if (!agent.locked) {
                    onSelect(agent.id, agent.name);
                  }
                }}
              >
                <div className="flex items-center gap-3">
                  <div
                    className={cn(
                      'bg-background/60 flex h-10 w-10 shrink-0 items-center justify-center rounded-full',
                      agent.locked ? 'bg-muted/30' : '',
                    )}
                  >
                    <If
                      condition={agent.avatar}
                      fallback={
                        <User
                          className={cn(
                            'h-5 w-5',
                            agent.locked
                              ? 'text-muted-foreground/50'
                              : 'text-blue-500',
                          )}
                        />
                      }
                    >
                      <img
                        src={agent.avatar}
                        alt={agent.name}
                        className="h-8 w-8 rounded-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/avatars/cloud.webp';
                        }}
                      />
                    </If>
                  </div>
                  <div className="flex flex-col">
                    <span className="text-foreground font-medium">
                      {agent.name}
                    </span>
                    <span className="text-muted-foreground text-xs">
                      {agent.goal && agent.goal.length > 50
                        ? `${agent.goal.substring(0, 50)}...`
                        : agent.goal || 'AI assistant'}
                    </span>
                  </div>
                </div>

                <If
                  condition={agent.locked}
                  fallback={
                    <div className="group relative">
                      <Info className="text-muted-foreground/60 hover:text-primary h-4 w-4 transition-colors" />
                    </div>
                  }
                >
                  <div className="text-muted-foreground flex items-center">
                    <Lock className="mr-1 h-4 w-4" />
                    <span className="text-xs">Locked</span>
                  </div>
                </If>

                {/* Tooltip on hover */}
                {/* <If condition={index === hoveredIndex}> */}
                {/* <If condition={true}>
                  <div
                    className={cn(
                      'fixed right-2',
                      index === 0 ? 'top-14' : '-top-12',
                      'z-50',
                      'bg-black/80 text-white',
                      'rounded p-2 shadow-lg',
                      'max-w-[280px] text-xs',
                      'animate-in fade-in-50 zoom-in-95',
                    )}
                  >
                    <If
                      condition={agent.locked}
                      fallback={
                        <>
                          <p className="mb-1 font-semibold">
                            What this agent can do:
                          </p>
                          <p>
                            {agent.instructions ||
                              'Help with various tasks related to their expertise.'}
                          </p>
                        </>
                      }
                    >
                      This agent is locked. Upgrade to access their expertise.
                    </If>
                  </div>
                </If> */}
              </div>
            </WithTooltip>
          ))}
        </If>
      </div>
    </div>
  );

  return createPortal(dropdown, document.body);
}
