import { useCallback, useEffect, useState } from 'react';

import { useChatContext } from '@/features/chat/context/chat-context';
import { MessageDisplayComponentPublic } from '@/client/types.gen';
import { MarkdownRenderer } from '@/components/markdown';
import { If } from '@/components/ui/common/if';
import { useUserContext } from '@/features/user/provider/user-provider';
import { InterruptConfirmation } from '@/hooks/message-stream';

import { ToolCall } from '../../types';
import { ThinkingAnimation } from '../common/thinking-animation';
import { InlineConfirmation } from '../tools/tool-call-interrupt';
import { ToolCallRenderer } from '../tools/tool-call-renderer';
import { parseThoughtSections } from '../tools/utils/parse-thought-sections';
import { replaceCustomerFullname } from '../tools/utils/replace-customer-fullname';
import { DisplayComponent } from './display-component';
import { GroupChatSection } from './group-chat-section';
import { StreamingMessageContainer } from './streaming-message-container';

interface MessageContentProps {
  toolCalls?: ToolCall[];
  isStreaming?: boolean;
  displayComponents?: MessageDisplayComponentPublic[];
  confirmation?: InterruptConfirmation | null;
  groupChatOnly?: boolean;
  agentInfo?: {
    name: string;
    role: string;
  };
}

export const MessageContent = ({
  toolCalls,
  isStreaming = false,
  displayComponents = [],
  confirmation,
  groupChatOnly = false,
  agentInfo,
}: MessageContentProps) => {
  const [showConfirmation, setShowConfirmation] = useState(true);
  const { user } = useUserContext();
  const { thinkingContent } = useChatContext();

  useEffect(() => {
    if (confirmation) {
      setShowConfirmation(true);
    }
  }, [confirmation]);

  const handleConfirm = useCallback(() => {
    if (confirmation) {
      confirmation.onConfirm();
      setShowConfirmation(false);
    }
  }, [confirmation]);

  const handleCancel = useCallback(
    (cancelMessage: string) => {
      if (confirmation) {
        confirmation.onCancel(cancelMessage);
        setShowConfirmation(false);
      }
    },
    [confirmation],
  );

  // Merge tool calls and display components, sort by position
  const mergedByPositions = [
    ...(toolCalls || []).map((tc) => ({
      type: 'toolCall' as const,
      id: tc.id,
      position: tc.position ?? 0,
      item: tc,
    })),
    ...(displayComponents || []).map((dc) => ({
      type: 'displayComponent' as const,
      id: dc.id,
      position: dc.position ?? 0,
      item: dc,
    })),
  ].sort((a, b) => a.position - b.position || a.id.localeCompare(b.id));

  return (
    <div className="space-y-4">
      {/* Group Chat Only Mode */}
      {groupChatOnly ? (
        <>
          {/* Render group chat sections from toolCalls */}
          {toolCalls
            ?.filter((tc) => tc.name === 'group_chat')
            .map((tc, i) => {
              let parsedContent: string = '';
              try {
                if (typeof tc.arguments === 'string') {
                  parsedContent = replaceCustomerFullname({
                    content: JSON.parse(tc.arguments).message,
                    fullName: user.full_name!,
                  });
                } else {
                  // Handle case where arguments is already an object
                  const args = tc.arguments as any;
                  parsedContent = args?.message || '';
                }
              } catch (e) {
                console.warn('Failed to parse group chat arguments:', e);
                parsedContent = '';
              }
              return (
                <div key={`group-chat-${i}`} className="text-sm">
                  <MarkdownRenderer
                    content={parsedContent}
                    enableMentions={true}
                  />
                </div>
              );
            })}
        </>
      ) : (
        <>
          {/* Merged tool calls and display components, sorted by position */}
          {mergedByPositions.length > 0 && (
            <>
              {mergedByPositions.map((mergedItem, index) => {
                if (mergedItem.type === 'toolCall') {
                  const toolCall = mergedItem.item;
                  let toolContent: React.ReactNode;

                  if (toolCall.name === 'thought') {
                    toolContent = null;
                  } else if (toolCall.name === 'group_chat') {
                    toolContent = <GroupChatSection toolCall={toolCall} />;
                  } else if (toolCall.name === 'planning') {
                    return null;
                  } else {
                    toolContent = (
                      <ToolCallRenderer
                        toolCall={toolCall}
                        agentInfo={agentInfo}
                        useAnimations={true}
                      />
                    );
                  }

                  return (
                    <div key={`${toolCall.id}-message-content-${index}`}>
                      <StreamingMessageContainer isStreaming={isStreaming}>
                        <If condition={toolCall.thought}>
                          {(thought) => (
                            <div>
                              {parseThoughtSections(thought)
                                .filter(
                                  (section) => section.type !== 'thinking',
                                )
                                .map((section, i) => (
                                  <div
                                    key={`${toolCall.id}-markdown-${i}`}
                                    className="text-sm"
                                  >
                                    <MarkdownRenderer
                                      content={section.content}
                                      enableMentions={true}
                                    />
                                  </div>
                                ))}
                            </div>
                          )}
                        </If>
                        {toolContent}
                      </StreamingMessageContainer>
                    </div>
                  );
                }

                const displayComponent = mergedItem.item;

                return (
                  <StreamingMessageContainer
                    isStreaming={isStreaming}
                    key={displayComponent.id}
                  >
                    <DisplayComponent component={displayComponent} />
                  </StreamingMessageContainer>
                );
              })}
            </>
          )}
          <If condition={isStreaming}>
            <ThinkingAnimation label={thinkingContent || 'Working...'} />
          </If>

          {/* Confirmation dialogs - moved to end */}
          {confirmation && showConfirmation && (
            <div className="mt-4">
              <InlineConfirmation
                confirmation={{
                  ...confirmation,
                  onConfirm: handleConfirm,
                  onCancel: handleCancel,
                }}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
};
