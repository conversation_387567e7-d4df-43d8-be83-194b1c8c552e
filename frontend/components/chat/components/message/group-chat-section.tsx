import { Markdown<PERSON>enderer } from '@/components/markdown';
import { useUserContext } from '@/features/user/provider/user-provider';

import { ToolCall } from '../../types';
import { replaceCustomerFullname } from '../tools/utils/replace-customer-fullname';

// Helper component for group chat section
export const GroupChatSection = ({ toolCall }: { toolCall: ToolCall }) => {
  const { user } = useUserContext();

  const messageWithMention =
    toolCall.arguments &&
    typeof toolCall.arguments === 'object' &&
    'message' in toolCall.arguments &&
    toolCall.arguments.message
      ? replaceCustomerFullname({
          content: (toolCall.arguments as { message: string }).message,
          fullName: user.full_name!,
        })
      : '';

  if (!messageWithMention) return null;

  return (
    <div className="border-primary/30 bg-primary/5 rounded-lg border p-4 text-sm">
      <MarkdownRenderer content={messageWithMention} enableMentions={true} />
    </div>
  );
};
