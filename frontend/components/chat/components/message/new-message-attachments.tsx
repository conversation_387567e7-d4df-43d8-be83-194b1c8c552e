'use client';

import React, { useEffect, useState } from 'react';

import { useFileUpload } from '@/hooks/use-file-upload';

import { MessageAttachments } from './message-attachments';

type Attachment = unknown;
interface NewMessageAttachmentsProps {
  attachmentIds: string[];
}

export const NewMessageAttachments: React.FC<NewMessageAttachmentsProps> = ({
  attachmentIds,
}) => {
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { fetchAttachmentMetadata } = useFileUpload();

  useEffect(() => {
    const fetchAttachments = async () => {
      setIsLoading(true);
      try {
        const fetchedAttachments = await Promise.all(
          attachmentIds.map((id) => fetchAttachmentMetadata(id)),
        );
        setAttachments(fetchedAttachments.filter(Boolean) as Attachment[]);
      } catch (error) {
        console.error('Error fetching attachment metadata:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (attachmentIds && attachmentIds.length > 0) {
      fetchAttachments();
    }
  }, [attachmentIds, fetchAttachmentMetadata]);

  if (isLoading) {
    return (
      <div className="mb-2">
        <div className="flex animate-pulse space-x-2">
          {attachmentIds.map((id) => (
            <div
              key={id}
              className="h-16 w-24 rounded-md bg-gray-200 dark:bg-gray-700"
            />
          ))}
        </div>
      </div>
    );
  }

  if (attachments.length === 0) {
    return null;
  }

  return (
    <div className="mb-2">
      <MessageAttachments attachments={attachments} />
    </div>
  );
};
