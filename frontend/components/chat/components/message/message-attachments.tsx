'use client';

import React, { useState } from 'react';
import { File, Image, FileText } from 'lucide-react';

import { toast } from '@/hooks/use-toast';
import { MessageAttachment } from '@/components/chat/types';
import { attachmentUrlCache } from '@/lib/attachment-url-cache';
import { FilePreviewDialog } from '@/components/chat/components/common/file-preview-dialog';

interface MessageAttachmentsProps {
  attachments: MessageAttachment[];
  showPreview?: boolean;
}

interface AttachmentItemProps {
  attachment: MessageAttachment;
  showPreview?: boolean;
}

// Get file type category
function getFileCategory(mimeType: string): 'image' | 'pdf' | 'text' | 'document' | 'other' {
  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType === 'application/pdf') return 'pdf';
  if (mimeType.startsWith('text/') || mimeType === 'application/json') return 'text';
  if (mimeType.includes('word') || mimeType.includes('sheet')) return 'document';
  return 'other';
}

// Get file icon based on type
function getFileIcon(mimeType: string) {
  const category = getFileCategory(mimeType);

  if (category === 'image') {
    return <Image className="h-6 w-6" />;
  }
  if (category === 'pdf') {
    return (
      <div className="h-6 w-6 bg-red-500 rounded flex items-center justify-center text-white text-xs font-bold">
        PDF
      </div>
    );
  }
  if (category === 'document') {
    return <FileText className="h-6 w-6 text-blue-500" />;
  }
  if (category === 'text') {
    return <FileText className="h-6 w-6 text-green-500" />;
  }

  return <File className="h-6 w-6" />;
}

// Format file size
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
}

function AttachmentItem({ attachment, showPreview = true }: AttachmentItemProps) {
  const [previewOpen, setPreviewOpen] = useState(false);
  const [imagePreviewUrl, setImagePreviewUrl] = useState<string | null>(null);
  const [textContent, setTextContent] = useState<string | null>(null);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [thumbnailUrl, setThumbnailUrl] = useState<string | null>(null);
  const [isLoadingThumbnail, setIsLoadingThumbnail] = useState(false);

  const fileCategory = getFileCategory(attachment.file_type);
  const canPreview = fileCategory === 'image' || fileCategory === 'text' || fileCategory === 'pdf';

  // Load image thumbnail for display
  React.useEffect(() => {
    if (fileCategory === 'image' && !thumbnailUrl && !isLoadingThumbnail) {
      // For images, get the download URL to display as thumbnail
      const loadThumbnail = async () => {
        setIsLoadingThumbnail(true);
        try {
          const downloadUrl = await attachmentUrlCache.getDownloadUrl(attachment.id);
          setThumbnailUrl(downloadUrl);
        } catch (error) {
          console.error('Failed to load image thumbnail:', error);
        } finally {
          setIsLoadingThumbnail(false);
        }
      };
      loadThumbnail();
    }
  }, [attachment.id, fileCategory, thumbnailUrl, isLoadingThumbnail]);

  // Handle preview
  const handlePreview = async () => {
    if (!canPreview || !showPreview) return;

    try {
      // Get download URL from cache or backend
      const downloadUrl = await attachmentUrlCache.getDownloadUrl(attachment.id);

      if (fileCategory === 'image') {
        setImagePreviewUrl(downloadUrl);
        setPreviewOpen(true);
      } else if (fileCategory === 'pdf') {
        setPdfUrl(downloadUrl);
        setPreviewOpen(true);
      } else if (fileCategory === 'text') {
        // For text files, fetch content and display
        try {
          const textResponse = await fetch(downloadUrl);
          const text = await textResponse.text();
          setTextContent(text);
          setPreviewOpen(true);
        } catch {
          toast({
            title: 'Text Preview Error',
            description: 'Failed to load text content.',
            variant: 'destructive',
          });
        }
      }
    } catch {
      toast({
        title: 'Preview Error',
        description: 'Failed to load file preview.',
        variant: 'destructive',
      });
    }
  };

  // Cleanup URLs when component unmounts
  const handleClosePreview = () => {
    setImagePreviewUrl(null);
    setPdfUrl(null);
    setTextContent(null);
    setPreviewOpen(false);
  };

  // Render for image files
  if (fileCategory === 'image') {
    return (
      <>
        <div className="relative group">
          {/* Clickable Preview Container */}
          <div
            className="w-20 h-20 border rounded-lg bg-card overflow-hidden relative cursor-pointer hover:shadow-md transition-shadow"
            onClick={canPreview && showPreview ? handlePreview : undefined}
          >
            {/* Image Preview */}
            {thumbnailUrl ? (
              <img
                src={thumbnailUrl}
                alt={attachment.filename}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-muted">
                {isLoadingThumbnail ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                ) : (
                  getFileIcon(attachment.file_type)
                )}
              </div>
            )}
          </div>
        </div>

        <FilePreviewDialog
          fileCategory={fileCategory}
          filename={attachment.filename}
          imagePreviewUrl={imagePreviewUrl}
          pdfUrl={pdfUrl}
          textContent={textContent}
          previewOpen={previewOpen}
          onOpenChange={handleClosePreview}
        />
      </>
    );
  }

  // Render for non-image files (document-style card)
  return (
    <>
      <div className="relative group">
        {/* Clickable Document Card */}
        <div
          className={`flex items-center gap-3 p-3 rounded-lg border bg-card hover:shadow-md transition-shadow min-w-[200px] max-w-[280px] ${
            canPreview && showPreview ? 'cursor-pointer hover:bg-muted/50' : ''
          }`}
          onClick={canPreview && showPreview ? handlePreview : undefined}
        >
          {/* File Icon */}
          <div className="flex-shrink-0 p-2 rounded-lg bg-muted">
            {getFileIcon(attachment.file_type)}
          </div>

          {/* File Info */}
          <div className="flex-1 min-w-0">
            <div className="font-medium text-sm truncate" title={attachment.filename}>
              {attachment.filename}
            </div>
            <div className="text-xs text-muted-foreground">
              {formatFileSize(attachment.file_size)}
            </div>
          </div>
        </div>
      </div>

      <FilePreviewDialog
        fileCategory={fileCategory}
        filename={attachment.filename}
        imagePreviewUrl={imagePreviewUrl}
        pdfUrl={pdfUrl}
        textContent={textContent}
        previewOpen={previewOpen}
        onOpenChange={handleClosePreview}
      />
    </>
  );
}

export function MessageAttachments({ attachments, showPreview = true }: MessageAttachmentsProps) {
  if (!attachments || attachments.length === 0) return null;

  return (
    <div className="space-y-3">
      {/* Grid Layout for Clean Previews */}
      <div className="flex flex-wrap gap-3 max-h-48 overflow-y-auto">
        {attachments.map((attachment) => (
          <AttachmentItem
            key={attachment.id}
            attachment={attachment}
            showPreview={showPreview}
          />
        ))}
      </div>
    </div>
  );
}
