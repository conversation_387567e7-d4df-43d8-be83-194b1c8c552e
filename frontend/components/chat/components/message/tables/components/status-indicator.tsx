import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

import { StatusIndicatorProps } from '../common/types';
import {
  getStatusBgColor,
  getStatusBorderColor,
  getStatusColor,
} from '../common/utils';

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({ value }) => {
  if (!value) return null;

  const statusText = String(value);

  return (
    <Badge
      variant="outline"
      className={cn(
        'flex items-center gap-1 px-2 py-0.5 text-xs font-medium uppercase',
        'inline-flex w-auto max-w-fit',
        getStatusColor(statusText),
        getStatusBgColor(statusText),
        getStatusBorderColor(statusText),
      )}
    >
      {statusText.toUpperCase()}
    </Badge>
  );
};
