import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import { TooltipProvider } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { ChevronDown } from 'lucide-react';

import { ColoredToolIcon } from '../colored-tool-icon';

interface StatusConfig {
  icon: React.ReactElement;
  color: string;
  bgColor: string;
  border: string;
  ariaLabel: string;
  label: string;
}

interface ToolType {
  icon: React.ReactElement;
  display: string;
  description: string;
  iconColor: string;
  bgColor: string;
}

interface ToolHeaderProps {
  toolType: ToolType;
  status: StatusConfig;
  isExpanded: boolean;
  onToggleExpand: () => void;
  reasoning?: string | null;
  className?: string;
}

export function ToolHeader({
  toolType,
  status,
  isExpanded,
  onToggleExpand,
  reasoning,
  className = '',
}: ToolHeaderProps) {
  const hasReasoning = !!(
    reasoning &&
    typeof reasoning === 'string' &&
    reasoning.trim()
  );

  return (
    <TooltipProvider>
      <Button
        variant="ghost"
        className={cn(
          'flex h-auto w-full items-center rounded-none px-2 py-2 md:px-3 md:py-2',
          'hover:bg-muted/30 hover:text-foreground transition-colors',
          'group focus-visible:ring-primary/40 focus-visible:ring-1',
          isExpanded && 'bg-muted/20',
          className,
        )}
        onClick={onToggleExpand}
        aria-expanded={isExpanded}
      >
        <div className="flex w-full min-w-0 items-center justify-between">
          <div className="flex min-w-0 flex-1 items-center gap-2 overflow-hidden">
            {/* Tool Icon */}
            <ColoredToolIcon
              icon={toolType.icon}
              iconColor={toolType.iconColor}
              bgColor={toolType.bgColor}
              size={28}
              className="shrink-0 rounded-lg p-1 md:rounded-lg md:p-1.5"
            />

            {/* Tool Info */}
            <div className="flex min-w-0 flex-1 flex-col items-start overflow-hidden">
              <div className="flex w-full items-center gap-1 md:gap-2">
                <span className="flex-1 truncate text-left text-xs font-medium md:text-sm">
                  {toolType.display}
                </span>
                <Badge
                  variant="outline"
                  className={cn(
                    'h-4 shrink-0 border-none px-1 text-xs md:h-5',
                    status.color,
                    'transition-colors',
                  )}
                >
                  <span className="flex items-center gap-0.5 md:gap-1">
                    {status.icon}
                    <span className="text-[9px] font-medium md:text-[10px]">
                      {status.label}
                    </span>
                  </span>
                </Badge>
              </div>

              {/* Reasoning or Agent Info */}
              <If condition={hasReasoning}>
                <div className="text-muted-foreground w-full text-left text-xs">
                  <span className="line-clamp-1 italic md:line-clamp-1">
                    {reasoning}
                  </span>
                </div>
              </If>
            </div>
          </div>

          {/* Expand/Collapse */}
          {/* <If condition={enableExpand}>
            <ChevronDown
              className={cn(
                'text-muted-foreground/60 ml-2 h-4 w-4 shrink-0 transition-transform',
                'group-hover:text-muted-foreground',
                isExpanded && 'rotate-180',
              )}
            />
          </If> */}
        </div>
      </Button>
    </TooltipProvider>
  );
}
