import { useState } from 'react';

import { If } from '@/components/ui/common/if';
import { cn } from '@/lib/utils';
import { AnimatePresence, motion } from 'framer-motion';
import { CheckCircle2, LoaderCircle, XCircle } from 'lucide-react';

import { Too<PERSON><PERSON>all, ToolCallStatus } from '../../types';
import { ToolContentSections } from './components/tool-content-sections';
import { ToolHeader } from './components/tool-header';
import { ANIMATION_CONFIG } from './constants/ui-constants';
import { getToolType } from './tool-config';
import { extractReasoningFromArgs } from './utils/message-parser';

// Status configuration
const STATUS_CONFIG = {
  running: {
    icon: <LoaderCircle className="size-3.5 animate-spin" />,
    color: 'text-blue-600 dark:text-blue-400 border-none',
    bgColor: 'bg-blue-500/10 dark:bg-blue-500/20',
    border: 'border-blue-200/60 dark:border-blue-800/60',
    ariaLabel: 'Operation in progress',
    label: 'Running',
  },
  completed: {
    icon: <CheckCircle2 className="size-3.5" />,
    color: 'text-green-600 dark:text-green-400 border-none',
    bgColor: 'bg-green-500/10 dark:bg-green-500/20',
    border: 'border-green-200/60 dark:border-green-800/60',
    ariaLabel: 'Operation completed successfully',
    label: 'Completed',
  },
  error: {
    icon: <XCircle className="size-3.5" />,
    color: 'text-red-600 dark:text-red-400 border-none',
    bgColor: 'bg-red-500/10 dark:bg-red-500/20',
    border: 'border-red-200/60 dark:border-red-800/60',
    ariaLabel: 'Operation failed with error',
    label: 'Error',
  },
  timeout: {
    icon: <XCircle className="size-3.5" />,
    color: 'text-red-600 dark:text-red-400 border-none',
    bgColor: 'bg-red-500/10 dark:bg-red-500/20',
    border: 'border-red-200/60 dark:border-red-800/60',
    ariaLabel: 'Operation timed out',
    label: 'Timeout',
  },
  success: {
    icon: <CheckCircle2 className="size-3.5" />,
    color: 'text-green-600 dark:text-green-400 border-none',
    bgColor: 'bg-green-500/10 dark:bg-green-500/20',
    border: 'border-green-200/60 dark:border-green-800/60',
    ariaLabel: 'Operation completed successfully',
    label: 'Success',
  },
} as const satisfies Record<
  ToolCallStatus,
  {
    icon: React.ReactNode;
    color: string;
    bgColor: string;
    border: string;
    ariaLabel: string;
    label: string;
  }
>;

interface ToolCallRendererProps {
  toolCall: ToolCall;
  isExpanded?: boolean;
  onExpandChange?: (expanded: boolean) => void;
  className?: string;
  agentInfo?: {
    name: string;
    role: string;
  };
  useAnimations?: boolean;
}

export function ToolCallRenderer({
  toolCall,
  isExpanded: initialExpanded = false,
  onExpandChange,
  className = '',
  agentInfo,
  useAnimations = true,
}: ToolCallRendererProps) {
  const [isExpandedState, setIsExpandedState] = useState(initialExpanded);

  // Use either controlled or uncontrolled expansion state
  const isExpanded = onExpandChange ? initialExpanded : isExpandedState;
  const setIsExpanded = (expanded: boolean) => {
    setIsExpandedState(expanded);
    onExpandChange?.(expanded);
  };

  // Basic tool data
  const status = STATUS_CONFIG[toolCall.status] || STATUS_CONFIG.running;
  const toolType = getToolType(toolCall.name);
  const reasoning = extractReasoningFromArgs(toolCall.arguments);
  const formattedArgs = toolCall.arguments;

  const toggleExpand = () => setIsExpanded(!isExpanded);
  const isCliTool = toolCall.name.includes("cli");
  const isPlanningTool = toolCall.name.includes("planning");
  const showExpand = !isCliTool && !isPlanningTool;

  return (
    <div
      className={cn(
        'overflow-hidden rounded-lg border border-black/10 will-change-transform md:mx-0 md:my-4 md:rounded-lg dark:border-white/10',
        isExpanded ? 'shadow-md' : 'hover:shadow-xs',
        className,
      )}
      data-tool-call="true"
    >
      <ToolHeader
        toolType={toolType}
        status={status}
        isExpanded={isExpanded}
        onToggleExpand={toggleExpand}
        reasoning={reasoning}
      />

      {/* Expanded Content */}
      <If condition={useAnimations}>
        <AnimatePresence>
          <If condition={isExpanded && showExpand}>
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: ANIMATION_CONFIG.DURATION }}
              className="overflow-hidden"
            >
              <ToolContentSections
                reasoning={reasoning}
                toolCallName={toolCall.name}
                formattedArgs={formattedArgs}
                output={toolCall.output}
              />
            </motion.div>
          </If>
        </AnimatePresence>
      </If>
      <If condition={!useAnimations && isExpanded}>
        <ToolContentSections
          reasoning={reasoning}
          toolCallName={toolCall.name}
          formattedArgs={formattedArgs}
          output={toolCall.output}
        />
      </If>
    </div>
  );
}
