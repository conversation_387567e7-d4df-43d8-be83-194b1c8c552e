export // Helper to parse thought into sections (thinking, group_chat, markdown)
function parseThoughtSections(thought: string) {
  const sections: {
    type: 'thinking' | 'group_chat' | 'markdown';
    content: string;
  }[] = [];
  const text = thought;
  const pattern =
    /(<thinking>([\s\S]*?)(?:\n?<\/thinking>|$))|(<group_chat>([\s\S]*?)(?:<\/group_chat>|$))/g;
  let lastIndex = 0;
  let match;
  while ((match = pattern.exec(text)) !== null) {
    if (match.index > lastIndex) {
      // Markdown between blocks
      const md = text.slice(lastIndex, match.index).trim();
      if (md) sections.push({ type: 'markdown', content: md });
    }
    if (match[1]) {
      // Thinking block (may be open or closed)
      sections.push({ type: 'thinking', content: match[2].trim() });
    } else if (match[3]) {
      // Group chat block
      sections.push({ type: 'group_chat', content: match[4].trim() });
    }
    lastIndex = pattern.lastIndex;
  }
  // Any remaining markdown after last block
  if (lastIndex < text.length) {
    const md = text.slice(lastIndex).trim();
    if (md) sections.push({ type: 'markdown', content: md });
  }
  return sections;
}
