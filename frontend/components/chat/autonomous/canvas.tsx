'use client';

import { Suspense, useEffect, useMemo, useState } from 'react';

import dynamic from 'next/dynamic';

import { Badge } from '@/components/ui/badge';
import { PageSkeleton } from '@/components/ui/common/page';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AgentCardList } from '@/features/agent/components/agent-card-list';
import { AgentProvider } from '@/features/agent/provider/agent-provider';
import { useChatContext } from '@/features/chat/context/chat-context';
import { useDashboard } from '@/hooks/use-dashboard';
import { useReport } from '@/hooks/use-report';
import { cn } from '@/lib/utils';
import {
  SchemaDashboard,
  SchemaMessageDisplayComponentPublic,
  SchemaReport,
} from '@/openapi-ts/gens';
import { Message } from '@/types/chat';

import { ToolCall } from '../types';

// Dynamically import tab components
const ReportTab = dynamic(() => import('./report-tab'));
const DashboardTab = dynamic(() => import('./dashboard-tab'));
const ChartsTab = dynamic(() => import('./charts-tab'));
const ConsoleCanvas = dynamic(() =>
  import('./console-canvas').then((mod) => mod.ConsoleCanvas),
);

interface CanvasItemWithAgent {
  id: string;
  type: 'toolCall' | 'displayComponent';
  content: ToolCall | SchemaMessageDisplayComponentPublic;
  agentName?: string;
  agentRole?: string;
  messageIndex: number; // Changed from optional to required to match chat-panel.tsx
}

interface ChartsCanvasProps {
  toolCalls: ToolCall[];
  displayComponents?: SchemaMessageDisplayComponentPublic[];
  canvasItemsWithAgent?: CanvasItemWithAgent[];
  className?: string;
  lastMessage?: Message;
  conversationId?: string;
  currentReport?: SchemaReport | null;
  currentDashboard?: SchemaDashboard | null;
  onPlanningToolsChange?: (tools: ToolCall[]) => void;
  isEmptyConversation?: boolean;
}

// Combined interface for all items that can be shown in the canvas
export interface CanvasItem {
  id: string;
  type: 'toolCall' | 'displayComponent';
  timestamp: Date;
  content: ToolCall | SchemaMessageDisplayComponentPublic;
  status?: 'running' | 'completed' | 'error';
  agentName?: string;
  agentRole?: string;
  messageIndex: number; // Changed from optional to required
}

export function ChartsCanvas({
  toolCalls,
  displayComponents = [],
  canvasItemsWithAgent = [],
  className,
  conversationId,
  currentReport,
  currentDashboard,
  onPlanningToolsChange,
  isEmptyConversation = false,
}: ChartsCanvasProps) {
  const [canvasItems, setCanvasItems] = useState<CanvasItem[]>([]);
  const [isWorkspaceUserActivated, setIsWorkspaceUserActivated] =
    useState(true);

  const { hasReport, hasDashboard } = useChatContext();

  // Get initial report data using the hook - only enabled if hasReport is true
  const { report: initialReport, isLoading: isReportLoading } = useReport(
    conversationId || null,
    { enabled: hasReport },
  );

  // Get initial dashboard data using the hook - only enabled if hasDashboard is true
  const { dashboard: initialDashboard, isLoading: isDashboardLoading } =
    useDashboard(conversationId || null, { enabled: hasDashboard });

  // Determine the current report to display (streaming takes precedence over initial)
  const displayReport = currentReport || initialReport;

  // Determine the current dashboard to display (streaming takes precedence over initial)
  const displayDashboard = currentDashboard || initialDashboard;

  // Process planning tool calls - keep this to populate planningTools but remove tab switching
  useEffect(() => {
    // Only process planning tools if the conversation is not empty
    if (!isEmptyConversation && toolCalls && toolCalls.length > 0) {
      // Extract planning tool calls by name
      const planningToolCalls = toolCalls.filter(
        (tool) =>
          tool.name === 'planning' ||
          tool.name.includes('planning') ||
          tool.name.includes('plan'),
      );

      // Update planning tools state if there are planning tools
      if (planningToolCalls.length > 0) {
        onPlanningToolsChange?.(planningToolCalls);
      }
    } else if (isEmptyConversation) {
      // Clear planning tools for empty conversations
      onPlanningToolsChange?.([]);
    }
  }, [toolCalls, onPlanningToolsChange, isEmptyConversation]);

  // Reset planningTools and workspace activation when conversation changes (lastMessage changes)
  useEffect(() => {
    onPlanningToolsChange?.([]);
    setIsWorkspaceUserActivated(false); // Reset workspace activation for new conversation

    // Only populate planning tools if the conversation is not empty
    if (!isEmptyConversation && toolCalls && toolCalls.length > 0) {
      const planningToolCalls = toolCalls.filter(
        (tool) =>
          tool.name === 'planning' ||
          tool.name.includes('planning') ||
          tool.name.includes('plan'),
      );
      if (planningToolCalls.length > 0) {
        onPlanningToolsChange?.(planningToolCalls);
      }
    }
    setIsWorkspaceUserActivated(true);
  }, [toolCalls, onPlanningToolsChange, conversationId, isEmptyConversation]);

  // Build the agent map for tools
  useEffect(() => {
    // Reset the map first to avoid stale data
    const newAgentMap: Record<string, { name: string; role: string }> = {};

    // Look through canvasItemsWithAgent to find tool calls with agent info
    canvasItemsWithAgent.forEach((item) => {
      // Add more defensive checks
      if (!item || !item.content) {
        return;
      }

      if (item.type === 'toolCall' && item.agentName) {
        const tool = item.content as ToolCall;

        // Check if tool has valid ID
        if (!tool || !tool.id) {
          return;
        }

        newAgentMap[tool.id] = {
          name: item.agentName,
          role: item.agentRole || item.agentName || '',
        };
      }
    });

    // Also look through messages to extract agent information for
    // (Only set this if not already found in canvasItemsWithAgent)
    toolCalls.forEach((tool) => {
      // Add defensive check for tool ID
      if (!tool || !tool.id) {
        return;
      }

      if (!newAgentMap[tool.id]) {
        // Check if tool call has agent information directly attached
        if (tool.agentRole || tool.agentName) {
          const agentInfo = tool.agentRole || tool.agentName || 'assistant';
          newAgentMap[tool.id] = {
            name: agentInfo,
            role: agentInfo,
          };
        } else {
          // Use a default agent name if we can't determine it
          newAgentMap[tool.id] = {
            name: 'assistant',
            role: 'assistant',
          };
        }
      }
    });
  }, [canvasItemsWithAgent, toolCalls]);

  // Standardize on using canvasItemsWithAgent as the primary data source
  useEffect(() => {
    // Process data from canvasItemsWithAgent (preferred) or fall back to displayComponents
    if (canvasItemsWithAgent.length > 0) {
      // Filter to only include display components - tool calls are handled separately
      const items: CanvasItem[] = canvasItemsWithAgent
        .filter((item) => item.type === 'displayComponent')
        .map((item) => {
          const component = item.content as SchemaMessageDisplayComponentPublic;
          return {
            id: item.id,
            type: 'displayComponent',
            timestamp: component.created_at
              ? new Date(component.created_at)
              : new Date(),
            content: component,
            agentName: item.agentName,
            agentRole: item.agentRole,
            messageIndex: item.messageIndex,
          };
        });

      // Simplified sorting logic - always prioritize message index if available
      const sortedItems = items.sort((a, b) => {
        // First try to sort by messageIndex
        if (a.messageIndex !== undefined && b.messageIndex !== undefined) {
          return a.messageIndex - b.messageIndex;
        }
        // Fall back to timestamp if message index is not available
        return a.timestamp.getTime() - b.timestamp.getTime();
      });

      setCanvasItems(sortedItems);
    }
    // Only use displayComponents if canvasItemsWithAgent is empty
    else if (displayComponents.length > 0) {
      const displayComponentItems: CanvasItem[] = displayComponents.map(
        (component) => ({
          id: component.id,
          type: 'displayComponent',
          timestamp: component.created_at
            ? new Date(component.created_at)
            : new Date(),
          content: component,
          messageIndex: 0, // Provide a default message index when not available
        }),
      );

      // Sort chronologically (oldest first)
      const sortedItems = displayComponentItems.sort(
        (a, b) => a.timestamp.getTime() - b.timestamp.getTime(),
      );

      setCanvasItems(sortedItems);
    } else {
      setCanvasItems([]);
    }
  }, [canvasItemsWithAgent, displayComponents]);

  // Pre-render calculation of dimensions
  const totalCount = canvasItems.length;

  // Check if we should show the charts tab
  const shouldShowChartsTab = canvasItems.length > 0;

  // Check if we should show the report tab
  const shouldShowReportTab = displayReport;

  // Build tabs configuration
  const tabs = useMemo(() => {
    // Define all possible tabs
    const allTabs = [
      {
        id: 'report',
        label: 'Report',
        content: shouldShowReportTab && displayReport && (
          <ReportTab report={displayReport} isLoading={isReportLoading} />
        ),
        show: !!shouldShowReportTab,
      },
      {
        id: 'dashboard',
        label: 'Dashboard',
        content: displayDashboard && (
          <DashboardTab
            dashboard={displayDashboard}
            isLoading={isDashboardLoading}
          />
        ),
        show: !!displayDashboard,
      },
      {
        id: 'charts',
        label: (
          <div className="flex items-center gap-2">
            <span>Chart & Table</span>
            <Badge variant="default">{totalCount}</Badge>
          </div>
        ),
        show: shouldShowChartsTab,
        content: shouldShowChartsTab && <ChartsTab canvasItems={canvasItems} />,
      },
      {
        id: 'workspace',
        label: 'Workspace',
        show: true, // Always show workspace
        content: (
          <ConsoleCanvas
            // TODO: Remove isVisible
            isVisible
            isUserActivated={isWorkspaceUserActivated}
            conversationId={conversationId}
            toolCalls={toolCalls}
          />
        ),
      },
      {
        id: 'teams',
        label: 'Teams',
        content: (
          <AgentProvider>
            <AgentCardList className="h-full overflow-y-auto px-2 pt-2" />
          </AgentProvider>
        ),
        show: true,
      },
    ];

    // Filter and reorder based on defaultTab
    const tabOrder = [
      'report',
      'dashboard',
      'charts',
      'planning',
      'workspace',
      'teams',
    ];

    return tabOrder
      .map((id) => allTabs.find((tab) => tab.id === id))
      .filter(
        (tab): tab is NonNullable<typeof tab> => tab !== undefined && tab.show,
      )
      .map(({ show: _show, ...tab }) => tab); // Remove the 'show' property
  }, [
    canvasItems,
    conversationId,
    displayDashboard,
    displayReport,
    isDashboardLoading,
    isReportLoading,
    isWorkspaceUserActivated,
    shouldShowChartsTab,
    shouldShowReportTab,
    toolCalls,
    totalCount,
  ]);

  return (
    <div
      className={cn(
        'z-10 flex h-full flex-col shadow-lg max-md:pt-2',
        className,
      )}
    >
      <Tabs defaultValue={tabs[0].id} className="flex h-full flex-col">
        <TabsList>
          {tabs.map((tab) => (
            <TabsTrigger key={tab.id} value={tab.id}>
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>
        <ScrollArea className="grow">
          {tabs.map((tab) => (
            <TabsContent
              key={tab.id}
              value={tab.id}
              className="h-full grow overflow-hidden"
            >
              <Suspense fallback={<PageSkeleton />}>{tab.content}</Suspense>
            </TabsContent>
          ))}
        </ScrollArea>
      </Tabs>
    </div>
  );
}
