'use client';

import { memo } from 'react';

import { Button } from '@/components/ui/button';
import { ShareButton } from '@/features/chat/components/share-button';
import { MessageSquarePlus } from 'lucide-react';

import { ConversationHeader } from '../conversation-header';

export interface ChatPanelHeaderContentProps {
  conversationId?: string;
  conversationTitle?: string;
  conversationCreatedAt?: string;
  modelProvider?: string;
  resourceId?: string;
}

export interface ChatPanelHeaderActionsContentProps {
  conversationId?: string;
  isSharedView?: boolean;
  onNewChat?: () => void;
}

const MemoizedConversationHeader = memo(ConversationHeader);

export const ChatPanelHeaderContent = memo(
  ({
    conversationId,
    conversationTitle,
    conversationCreatedAt,
    modelProvider,
    resourceId,
  }: ChatPanelHeaderContentProps) => {
    if (conversationId || resourceId) {
      return (
        <div className="flex items-center gap-2">
          {/* Show conversation title if we have conversationId */}
          {conversationId && conversationTitle && (
            <MemoizedConversationHeader
              conversationId={conversationId}
              conversationTitle={conversationTitle}
              conversationCreatedAt={conversationCreatedAt}
              modelProvider={modelProvider}
            />
          )}
        </div>
      );
    }
    return null;
  },
);
ChatPanelHeaderContent.displayName = 'ChatPanelHeaderContent';

export const ChatPanelHeaderActionsContent = memo(
  ({
    conversationId,
    isSharedView = false,
    onNewChat,
  }: ChatPanelHeaderActionsContentProps) => {
    if (isSharedView) return null;

    return (
      <div className="relative flex items-center justify-end gap-2">
        {conversationId && <ShareButton conversationId={conversationId} />}
        {onNewChat && conversationId && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onNewChat}
            className="flex items-center gap-2"
          >
            <MessageSquarePlus className="size-4" />
            <span className="@max-md:hidden">New chat</span>
          </Button>
        )}
      </div>
    );
  },
);
ChatPanelHeaderActionsContent.displayName = 'ChatPanelHeaderActionsContent';
