'use client';

import { memo } from 'react';

import { Button } from '@/components/ui/button';
import { ShareButton } from '@/features/chat/components/share-button';
import { MessageSquarePlus } from 'lucide-react';

import { TitleSession } from '../title-session';

export interface ChatPanelHeaderContentProps {
  conversationId?: string;
  conversationTitle?: string;
  resourceId?: string;
}

export interface ChatPanelHeaderActionsContentProps {
  conversationId?: string;
  isSharedView?: boolean;
  onNewChat?: () => void;
}

const MemoizedTitleSession = memo(TitleSession);

export const ChatPanelHeaderContent = memo(
  ({
    conversationId,
    conversationTitle,
    resourceId,
  }: ChatPanelHeaderContentProps) => {
    if (conversationId || resourceId) {
      return (
        <div className="flex items-center gap-2">
          {conversationId && conversationTitle && (
            <MemoizedTitleSession
              conversationId={conversationId}
              conversationTitle={conversationTitle}
            />
          )}
        </div>
      );
    }
    return null;
  },
);
ChatPanelHeaderContent.displayName = 'ChatPanelHeaderContent';

export const ChatPanelHeaderActionsContent = memo(
  ({
    conversationId,
    isSharedView = false,
    onNewChat,
  }: ChatPanelHeaderActionsContentProps) => {
    if (isSharedView) return null;

    return (
      <div className="relative flex items-center justify-end gap-2">
        {conversationId && <ShareButton conversationId={conversationId} />}
        {onNewChat && conversationId && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onNewChat}
            className="flex items-center gap-2"
          >
            <MessageSquarePlus className="size-4" />
            <span className="@max-md:hidden">New chat</span>
          </Button>
        )}
      </div>
    );
  },
);
ChatPanelHeaderActionsContent.displayName = 'ChatPanelHeaderActionsContent';
