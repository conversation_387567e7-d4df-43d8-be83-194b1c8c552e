import React from 'react';

import { FolderOpen } from 'lucide-react';

import ContentRenderer from './content-renderer';
import { EmptyState, ReportSectionSkeleton } from './skeletons';

interface ContentItem {
  index: number;
  type: 'paragraph' | 'chart' | 'table' | 'card' | 'timeline';
  content: any;
}

interface ReportSectionData {
  index: number;
  header?: string;
  content?: ContentItem[];
}

interface ReportSectionProps {
  section?: ReportSectionData;
  isLoading?: boolean;
}

const ReportSection: React.FC<ReportSectionProps> = ({
  section,
  isLoading = false,
}) => {
  if (isLoading) {
    return <ReportSectionSkeleton />;
  }

  if (!section || typeof section !== 'object') {
    return null;
  }

  if (!section.content?.length && section.header) {
    return (
      <div className="mb-12">
        <div className="mb-6">
          <div className="mb-2 flex items-center justify-between">
            <h2 className="text-foreground text-2xl font-bold">
              {section.header}
            </h2>
          </div>
        </div>
        <EmptyState
          title="Section Content Not Available"
          description="This section is being generated. Content will appear here once the analysis is complete."
          icon={<FolderOpen className="text-muted-foreground/50 h-8 w-8" />}
        />
      </div>
    );
  }

  // Group consecutive cards together for grid layout
  const groupedContent = section.content
    ? section.content
        .sort((a, b) => a.index - b.index)
        .reduce((acc: (ContentItem | ContentItem[])[], item) => {
          if (item.type === 'card') {
            // Check if the last item in acc is an array of cards
            const lastItem = acc[acc.length - 1];
            if (
              Array.isArray(lastItem) &&
              lastItem.length > 0 &&
              lastItem[0].type === 'card'
            ) {
              // Add to the existing card group
              lastItem.push(item);
            } else {
              // Start a new card group
              acc.push([item]);
            }
          } else {
            // Non-card item, add individually
            acc.push(item);
          }
          return acc;
        }, [])
    : [];

  // Function to get grid classes based on number of cards
  const getCardGridClasses = (cardCount: number) => {
    switch (cardCount) {
      case 1:
        // 1 card = 1/2 width centered
        return 'grid gap-6 grid-cols-1 max-w-md mx-auto my-6';
      case 2:
        // 2 cards = full width (2 columns)
        return 'grid gap-6 grid-cols-2 my-6';
      case 3:
        // 3 cards = 3 columns same row
        return 'grid gap-6 grid-cols-3 my-6';
      case 4:
        // 4 cards = 2 rows 2 cols
        return 'grid gap-6 grid-cols-2 my-6';
      default:
        // More than 4 cards = responsive grid with max 4 columns
        return 'grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 my-6';
    }
  };

  return (
    <div className="mb-12 transition-all duration-300">
      {section.header && (
        <div className="mb-6">
          <div className="mb-2 flex items-center">
            <h2 className="text-foreground text-2xl font-bold">
              {section.header}
            </h2>
          </div>
        </div>
      )}

      {groupedContent.length > 0 && (
        <div className="space-y-6">
          {groupedContent.map((contentGroup, groupIndex) => {
            if (Array.isArray(contentGroup)) {
              // This is a group of cards - render as grid with dynamic sizing
              const cardCount = contentGroup.length;
              return (
                <div
                  key={`card-group-${groupIndex}`}
                  className={getCardGridClasses(cardCount)}
                >
                  {contentGroup.map((contentItem, cardIndex) => (
                    <ContentRenderer
                      key={`${section.index}-${contentItem.index}-${cardIndex}`}
                      content={contentItem}
                      isLoading={isLoading}
                    />
                  ))}
                </div>
              );
            } else {
              // Single non-card item
              return (
                <ContentRenderer
                  key={`${section.index}-${contentGroup.index}-${groupIndex}`}
                  content={contentGroup}
                  isLoading={isLoading}
                />
              );
            }
          })}
        </div>
      )}
    </div>
  );
};

export default ReportSection;
