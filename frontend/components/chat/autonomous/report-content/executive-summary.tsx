import React from 'react';

import TrendIndicator from '@/components/shared/TrendIndicator';
import { getAlertStyles, renderAlertOverlay } from '@/utils/alert-styles';
import { renderLucideIcon } from '@/utils/icon-helpers';
import {
  AlertTriangle,
  DollarSign,
  Lightbulb,
  Target,
  TrendingUp,
} from 'lucide-react';

import { EmptyState, ExecutiveSummarySkeleton } from './skeletons';

interface KPICard {
  title: string;
  value: string;
  description?: string;
  trend?: {
    direction: 'up' | 'down' | 'neutral';
    value: string;
    description?: string;
  };
  icon?: string;
  alert?: boolean;
}

interface ExecutiveSummaryData {
  key_findings: string[];
  business_impact: string[];
  key_metrics?: KPICard[];
  recommendations?: string[];
}

interface ExecutiveSummaryProps {
  data?: ExecutiveSummaryData;
  isLoading?: boolean;
}

const ExecutiveSummary: React.FC<ExecutiveSummaryProps> = ({
  data,
  isLoading = false,
}) => {
  if (isLoading) {
    return <ExecutiveSummarySkeleton />;
  }

  if (
    !data ||
    (!data.key_findings?.length &&
      !data.business_impact?.length &&
      !data.key_metrics?.length &&
      !data.recommendations?.length)
  ) {
    return (
      <div className="mb-8">
        <h2 className="text-foreground mb-6 text-2xl font-bold">
          Executive Summary
        </h2>
        <EmptyState
          title="Executive Summary Not Available"
          description="The executive summary is being generated. Key findings and business impact will appear here once the analysis is complete."
          icon={<Target className="text-muted-foreground/50 h-8 w-8" />}
        />
      </div>
    );
  }

  return (
    <div className="mb-8">
      <h3 className="text-foreground mb-6 text-2xl font-bold">
        Executive Summary
      </h3>

      <div className="space-y-6">
        {/* Key Metrics Grid */}
        {data.key_metrics && data.key_metrics.length > 0 && (
          <div>
            <h4 className="text-foreground mb-4 text-lg font-semibold">
              Key Metrics
            </h4>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {data.key_metrics?.map((metric, index) => (
                <div
                  key={index}
                  className="group hover:bg-primary/5 from-card/80 to-card/40 relative overflow-hidden rounded-lg border bg-gradient-to-br p-4 text-left backdrop-blur-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-xl active:translate-y-0 active:scale-[0.98]"
                  style={{
                    animationDelay: `${index * 50}ms`,
                  }}
                >
                  {/* Animated gradient background for hover */}
                  <div className="from-primary/5 to-secondary/5 absolute inset-0 bg-gradient-to-br opacity-0 transition-all duration-500 group-hover:opacity-100" />

                  {/* Alert overlay for error states */}
                  {renderAlertOverlay(!!metric.alert)}

                  <div className="relative z-10 flex h-full flex-col justify-between gap-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {metric.icon && (
                          <div
                            className={`group-hover:bg-background/90 flex h-9 w-9 items-center justify-center rounded-lg backdrop-blur-sm transition-colors ${getAlertStyles(!!metric.alert).iconBg} ${getAlertStyles(!!metric.alert).iconText}`}
                          >
                            {renderLucideIcon(metric.icon, 'h-4 w-4')}
                          </div>
                        )}
                        <h3 className="text-foreground text-sm leading-tight font-semibold transition-colors">
                          {metric.title}
                        </h3>
                      </div>
                      {metric.alert && (
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                      )}
                    </div>

                    <div className="flex-1 space-y-2">
                      <div
                        className={`text-2xl font-bold transition-colors ${getAlertStyles(!!metric.alert).valueText}`}
                      >
                        {metric.value}
                      </div>

                      <TrendIndicator trend={metric.trend} variant="symbol" />

                      {metric.description && (
                        <p className="text-muted-foreground group-hover:text-foreground/80 text-xs leading-relaxed transition-colors">
                          {metric.description}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Key Findings */}
        {data.key_findings?.length > 0 && (
          <div className="border-primary/30 bg-primary/5 rounded-lg border p-6">
            <div className="mb-4 flex items-center gap-2">
              <TrendingUp className="size-4 text-blue-600 dark:text-blue-400" />
              <h4 className="text-foreground text-lg font-semibold">
                Key Findings
              </h4>
            </div>
            <ul className="space-y-3">
              {data.key_findings.map((finding, index) => (
                <li key={index} className="flex items-start gap-3">
                  <div className="mt-2 h-1.5 w-1.5 shrink-0 rounded-full bg-blue-600 dark:bg-blue-400" />
                  <span className="text-foreground leading-relaxed">
                    {finding}
                  </span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Business Impact */}
        {data.business_impact?.length > 0 && (
          <div className="border-primary/30 bg-primary/5 rounded-lg border p-6">
            <div className="mb-4 flex items-center gap-2">
              <DollarSign className="size-4 text-green-600 dark:text-green-400" />
              <h4 className="text-foreground text-lg font-semibold">
                Business Impact
              </h4>
            </div>
            <ul className="space-y-3">
              {data.business_impact.map((impact, index) => (
                <li key={index} className="flex items-start gap-3">
                  <div className="mt-2 h-1.5 w-1.5 shrink-0 rounded-full bg-green-600 dark:bg-green-400" />
                  <span className="text-foreground leading-relaxed">
                    {impact}
                  </span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Recommendations */}
        {data.recommendations && data.recommendations.length > 0 && (
          <div className="border-primary/30 bg-primary/5 rounded-lg border p-6">
            <div className="mb-4 flex items-center gap-2">
              <Lightbulb className="size-4 text-yellow-600 dark:text-yellow-400" />
              <h4 className="text-foreground text-lg font-semibold">
                Recommendations
              </h4>
            </div>
            <ul className="space-y-3">
              {data.recommendations?.map((recommendation, index) => (
                <li key={index} className="flex items-start gap-3">
                  <div className="mt-2 h-1.5 w-1.5 shrink-0 rounded-full bg-yellow-600 dark:bg-yellow-400" />
                  <span className="text-foreground leading-relaxed">
                    {recommendation}
                  </span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExecutiveSummary;
