import React from 'react';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from '@/components/chat/components/message/charts';
import { processChartData } from '@/components/chat/components/message/charts/common/functional-utils';
import { BasicTable } from '@/components/chat/components/message/tables';
import { TableColumnConfig } from '@/components/chat/components/message/tables/common/types';
import TrendIndicator from '@/components/shared/TrendIndicator';
import { CardContent } from '@/components/ui/card';
import { getAlertStyles } from '@/utils/alert-styles';
import { renderLucideIcon } from '@/utils/icon-helpers';
import { AlertTriangle, BarChart3, Database } from 'lucide-react';

import {
  CardSkeleton,
  ChartSkeleton,
  EmptyState,
  TableSkeleton,
} from './skeletons';

interface ChartContent {
  title: string;
  description?: string;
  chart_type:
    | 'bar'
    | 'pie'
    | 'line'
    | 'area'
    | 'step_area'
    | 'radar'
    | 'sankey';
  categories: string[];
  datasets: {
    data: number[];
    label?: string;
  }[];
  x_axis?: { title?: string; type?: string };
  y_axis?: { title?: string; type?: string };
  show_legend?: boolean;
  show_grid?: boolean;
  currency_format?: boolean;
  percentage_format?: boolean;
  sankey_nodes?: Array<{ id: string; label: string; color?: string }>;
  sankey_links?: Array<{
    source: string;
    target: string;
    value: number;
    label?: string;
  }>;
}

interface TableStructuredOutput {
  title: string;
  description?: string;
  columns: TableColumnConfig[];
  rows: string[][];
}

interface CardContent {
  title: string;
  value: string;
  description?: string;
  trend?: {
    direction: 'up' | 'down' | 'neutral';
    value: string;
    description?: string;
  };
  icon?: string;
  alert?: boolean;
}

interface ContentItem {
  index: number;
  type: 'paragraph' | 'chart' | 'table' | 'card' | 'timeline';
  content: string | ChartContent | TableStructuredOutput | CardContent;
}

interface ContentRendererProps {
  content: ContentItem;
  isLoading?: boolean;
}

const ContentRenderer: React.FC<ContentRendererProps> = ({
  content,
  isLoading = false,
}) => {
  // Safety check for content object
  if (!content || typeof content !== 'object') {
    return null;
  }

  const renderContent = () => {
    switch (content.type) {
      case 'paragraph':
        if (isLoading || !content.content) {
          return (
            <div className="prose prose-sm max-w-none animate-pulse">
              <div className="space-y-2">
                <div className="bg-muted h-4 w-full rounded"></div>
                <div className="bg-muted h-4 w-4/5 rounded"></div>
                <div className="bg-muted h-4 w-3/5 rounded"></div>
              </div>
            </div>
          );
        }
        return (
          <div className="prose prose-sm max-w-none">
            <p className="text-foreground leading-relaxed transition-opacity duration-300">
              {content.content as string}
            </p>
          </div>
        );

      case 'chart':
        if (isLoading || !content.content) {
          return <ChartSkeleton />;
        }

        const chartContent = content.content as ChartContent;

        // Check data availability based on chart type
        const hasValidData =
          chartContent.chart_type === 'sankey'
            ? chartContent.sankey_nodes?.length &&
              chartContent.sankey_links?.length
            : chartContent.categories?.length && chartContent.datasets?.length;

        if (!hasValidData) {
          return (
            <div className="my-6">
              <div className="bg-card rounded-lg border p-4">
                <EmptyState
                  title="Chart Data Not Available"
                  description="Chart data is being processed. The visualization will appear here once ready."
                  icon={
                    <BarChart3 className="text-muted-foreground/50 h-8 w-8" />
                  }
                />
              </div>
            </div>
          );
        }

        // Convert to frontend chart format
        const chartData = {
          labels: chartContent.categories || [],
          datasets:
            chartContent.datasets?.map((dataset) => ({
              ...dataset,
              backgroundColor: undefined, // Let chart components handle colors
            })) || [],
          x_axis: chartContent.x_axis,
          y_axis: chartContent.y_axis,
          display_options: {
            show_legend: chartContent.show_legend !== false,
            show_grid: chartContent.show_grid !== false,
            currency_format: chartContent.currency_format || false,
            percentage_format: chartContent.percentage_format || false,
          },
        };

        // Process the data for chart components - skip for Sankey charts
        let processedData: any[] = [];
        if (chartContent.chart_type !== 'sankey') {
          try {
            processedData = processChartData(chartData);
          } catch (error) {
            console.error('Chart data processing error:', error);
            processedData = [];
          }
        }

        // Error boundary component for chart rendering
        const ChartErrorBoundary: React.FC<{ children: React.ReactNode }> = ({ children }) => {
          try {
            return <>{children}</>;
          } catch (error) {
            console.error('Chart rendering error:', error);
            return (
              <div className="bg-card rounded-lg border p-4">
                <EmptyState
                  title="Chart Rendering Error"
                  description="Unable to render the chart. The data may be invalid or corrupted."
                  icon={
                    <BarChart3 className="text-muted-foreground/50 h-8 w-8" />
                  }
                />
              </div>
            );
          }
        };

        // Render the appropriate chart component
        const renderChart = () => {
          const commonProps = {
            data: processedData,
            chartData: chartData,
            height: 350,
            title: chartContent.title,
          };

          try {
            switch (chartContent.chart_type) {
              case 'bar':
                return (
                  <ChartErrorBoundary>
                    <BarChart {...commonProps} />
                  </ChartErrorBoundary>
                );
              case 'line':
                return (
                  <ChartErrorBoundary>
                    <LineChart {...commonProps} />
                  </ChartErrorBoundary>
                );
              case 'pie':
                return (
                  <ChartErrorBoundary>
                    <PieChart {...commonProps} height={400} />
                  </ChartErrorBoundary>
                );
              case 'area':
                return (
                  <ChartErrorBoundary>
                    <AreaChart {...commonProps} />
                  </ChartErrorBoundary>
                );
              case 'step_area':
                return (
                  <ChartErrorBoundary>
                    <StepAreaChart {...commonProps} />
                  </ChartErrorBoundary>
                );
              case 'radar':
                return (
                  <ChartErrorBoundary>
                    <RadarChart {...commonProps} height={400} />
                  </ChartErrorBoundary>
                );
              case 'sankey':
                if (chartContent.sankey_nodes && chartContent.sankey_links) {
                  const sankeyData = {
                    sankey_nodes: chartContent.sankey_nodes,
                    sankey_links: chartContent.sankey_links,
                  };
                  return (
                    <ChartErrorBoundary>
                      <SankeyChart
                        data={sankeyData}
                        chartData={chartData}
                        height={400}
                        title={chartContent.title}
                      />
                    </ChartErrorBoundary>
                  );
                }
                return (
                  <ChartErrorBoundary>
                    <BarChart {...commonProps} />
                  </ChartErrorBoundary>
                );
              default:
                return (
                  <ChartErrorBoundary>
                    <BarChart {...commonProps} />
                  </ChartErrorBoundary>
                );
            }
          } catch (error) {
            console.error('Chart type switch error:', error);
            return (
              <div className="bg-card rounded-lg border p-4">
                <EmptyState
                  title="Chart Type Error"
                  description="Unable to determine chart type or render chart component."
                  icon={
                    <BarChart3 className="text-muted-foreground/50 h-8 w-8" />
                  }
                />
              </div>
            );
          }
        };

        return (
          <div className="my-6">
            <div className="relative mx-auto max-w-3xl">{renderChart()}</div>
            {(chartContent.title || chartContent.description) && (
              <div className="mt-2 text-center">
                <p className="text-muted-foreground text-sm">
                  {chartContent.description && (
                    <>
                      {chartContent.title}: {chartContent.description}
                    </>
                  )}
                </p>
              </div>
            )}
          </div>
        );

      case 'table':
        if (isLoading || !content.content) {
          return <TableSkeleton />;
        }

        const tableContent = content.content as TableStructuredOutput;

        if (!tableContent.columns?.length || !tableContent.rows?.length) {
          return (
            <div className="my-6">
              <div className="bg-card rounded-lg border p-4">
                <EmptyState
                  title="Table Data Not Available"
                  description="Table data is being processed. The table will appear here once ready."
                  icon={
                    <Database className="text-muted-foreground/50 h-8 w-8" />
                  }
                />
              </div>
            </div>
          );
        }

        const tableData = {
          headers: tableContent.columns,
          rows: tableContent.rows,
        };

        return (
          <div className="my-6">
            <div className="overflow-hidden rounded-lg border">
              <BasicTable data={tableData} showFullView />
            </div>
            {(tableContent.title || tableContent.description) && (
              <div className="mt-2 text-center">
                <p className="text-muted-foreground text-sm">
                  {tableContent.description && (
                    <>
                      {tableContent.title}: {tableContent.description}
                    </>
                  )}
                </p>
              </div>
            )}
          </div>
        );

      case 'card':
        if (isLoading || !content.content) {
          return <CardSkeleton />;
        }

        const cardContent = content.content as CardContent;

        const alertStyles = getAlertStyles(!!cardContent.alert);

        if (!cardContent.title && !cardContent.value) {
          return <CardSkeleton />;
        }

        return (
          <div className="my-6">
            <div className="group hover:bg-primary/5 from-card/80 to-card/40 focus-visible:ring-primary/50 relative overflow-hidden rounded-lg border bg-gradient-to-br p-4 text-left backdrop-blur-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-xl focus-visible:ring-2 focus-visible:outline-none active:translate-y-0 active:scale-[0.98]">
              {/* Animated gradient background for hover */}
              <div className="from-primary/5 to-secondary/5 absolute inset-0 bg-gradient-to-br opacity-0 transition-all duration-500 group-hover:opacity-100" />

              <div className="relative z-10 flex h-full flex-col">
                <div className="mb-4 flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    {cardContent.icon && (
                      <div
                        className={`group-hover:bg-background/90 flex h-9 w-9 items-center justify-center rounded-lg backdrop-blur-sm transition-colors ${alertStyles.iconBg} ${alertStyles.iconText}`}
                      >
                        {renderLucideIcon(cardContent.icon, 'h-4 w-4')}
                      </div>
                    )}
                    <h3 className="text-foreground text-sm leading-tight font-semibold transition-colors">
                      {cardContent.title}
                    </h3>
                  </div>
                  {cardContent.alert && (
                    <AlertTriangle className="h-4 w-4 text-red-500" />
                  )}
                </div>

                <div className="mb-2 flex-1">
                  <div
                    className={`text-2xl font-bold transition-colors ${alertStyles.valueText}`}
                  >
                    {cardContent.value}
                  </div>
                </div>

                <div className="space-y-2">
                  {cardContent.description && (
                    <p className="text-muted-foreground group-hover:text-foreground/80 text-xs leading-relaxed transition-colors">
                      {cardContent.description}
                    </p>
                  )}
                  <TrendIndicator trend={cardContent.trend} />
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return <div className="content-item">{renderContent()}</div>;
};

export default ContentRenderer;
