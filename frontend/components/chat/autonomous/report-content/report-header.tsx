import React from 'react';

import { Calendar } from 'lucide-react';
import { formatters } from '@/utils/date-formatters';

import { ReportHeaderSkeleton } from './skeletons';

interface ReportHeaderProps {
  title?: string;
  description?: string;
  createdAt?: string;
  status?: 'generating' | 'complete' | 'error';
  onExportPDF?: () => void;
  isLoading?: boolean;
}

const ReportHeader: React.FC<ReportHeaderProps> = ({
  title,
  description,
  createdAt,
  status = 'complete',
  onExportPDF,
  isLoading = false,
}) => {
  if (isLoading || !title) {
    return <ReportHeaderSkeleton />;
  }

  const handleExportPDF = () => {
    if (onExportPDF) {
      onExportPDF();
    }
  };

  return (
    <div className="border-b pb-6 mb-8">
      <div className="mb-4 flex items-start justify-between">
        <div className="flex-1">
          <h1 className="text-foreground text-3xl font-bold">
            {title}
          </h1>
        </div>

        <div className="ml-6 flex items-center gap-2">
          {createdAt && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Calendar className="h-4 w-4" />
              <span>
                {formatters.long(createdAt)}
              </span>
            </div>
          )}
        </div>
      </div>

      {description && (
        <div className="mt-3">
          <p className="text-muted-foreground leading-relaxed">
            {description}
          </p>
        </div>
      )}
    </div>
  );
};

export default ReportHeader;
