import React from 'react';

import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export const ReportHeaderSkeleton: React.FC = () => (
  <div className="border-border mb-8 animate-pulse border-b pb-6">
    <div className="mb-4 flex items-start justify-between">
      <div className="flex-1 space-y-3">
        <Skeleton className="from-muted via-muted/50 to-muted h-8 w-3/4 bg-linear-to-r" />
        <Skeleton className="from-muted via-muted/50 to-muted h-4 w-1/2 bg-linear-to-r" />
      </div>
      <div className="flex items-center gap-3">
        <Skeleton className="h-8 w-24 rounded-full" />
        <Skeleton className="h-8 w-28" />
      </div>
    </div>
    <div className="space-y-2">
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-4/5" />
    </div>
  </div>
);

export const ExecutiveSummarySkeleton: React.FC = () => (
  <div className="mb-8 animate-pulse">
    <div className="mb-6 flex items-center gap-3">
      <Skeleton className="h-7 w-48" />
    </div>

    <div className="grid gap-6 md:grid-cols-1">
      {/* Key Findings Skeleton */}
      <div className="border-primary/30 bg-primary/5 rounded-lg border p-6">
        <div className="mb-4 flex items-center gap-2">
          <Skeleton className="h-5 w-5 rounded-full" />
          <Skeleton className="h-5 w-32" />
        </div>
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex items-start gap-3">
              <Skeleton className="mt-2 h-2 w-2 shrink-0 rounded-full" />
              <Skeleton className="h-4 w-full" />
            </div>
          ))}
        </div>
      </div>

      {/* Business Impact Skeleton */}
      <div className="border-primary/30 bg-primary/5 rounded-lg border p-6">
        <div className="mb-4 flex items-center gap-2">
          <Skeleton className="h-5 w-5 rounded-full" />
          <Skeleton className="h-5 w-36" />
        </div>
        <div className="space-y-3">
          {[1, 2].map((i) => (
            <div key={i} className="flex items-start gap-3">
              <Skeleton className="mt-2 h-2 w-2 shrink-0 rounded-full" />
              <Skeleton className="h-4 w-full" />
            </div>
          ))}
        </div>
      </div>
    </div>
  </div>
);

export const ChartSkeleton: React.FC = () => (
  <div className="my-6 animate-pulse">
    <div className="bg-card rounded-lg border p-4">
      <div className="space-y-4">
        <Skeleton className="h-6 w-64" />
        <Skeleton className="h-4 w-96" />
        <div className="relative">
          <Skeleton className="h-[350px] w-full rounded-lg" />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="space-y-2 text-center">
              <div className="border-primary/20 border-t-primary mx-auto h-16 w-16 animate-spin rounded-full border-4"></div>
              <p className="text-muted-foreground text-sm">Loading chart...</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

export const TableSkeleton: React.FC = () => (
  <div className="my-6 animate-pulse">
    <div className="space-y-4">
      <Skeleton className="h-6 w-48" />
      <Skeleton className="h-4 w-72" />
      <div className="bg-card overflow-hidden rounded-lg border">
        {/* Table Header */}
        <div className="bg-muted/50 border-b p-4">
          <div className="grid grid-cols-4 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <Skeleton key={i} className="h-4" />
            ))}
          </div>
        </div>
        {/* Table Rows */}
        {[1, 2, 3, 4, 5].map((row) => (
          <div key={row} className="border-b p-4 last:border-b-0">
            <div className="grid grid-cols-4 gap-4">
              {[1, 2, 3, 4].map((col) => (
                <Skeleton key={col} className="h-4" />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
);

export const CardSkeleton: React.FC = () => (
  <div className="my-6">
    <div className="from-card/80 to-card/40 relative animate-pulse overflow-hidden rounded-lg border bg-gradient-to-br p-4 backdrop-blur-sm">
      <div className="relative z-10 flex h-full flex-col">
        <div className="mb-4 flex items-center gap-3">
          <Skeleton className="h-9 w-9 rounded-lg" />
          <Skeleton className="h-4 w-32" />
        </div>
        <div className="mb-2 flex-1">
          <Skeleton className="h-8 w-24" />
        </div>
        <div className="space-y-2">
          <Skeleton className="h-3 w-48" />
          <Skeleton className="h-3 w-20" />
        </div>
      </div>
    </div>
  </div>
);

export const ReportSectionSkeleton: React.FC = () => (
  <div className="mb-12 animate-pulse">
    <div className="mb-6">
      <Skeleton className="mb-2 h-7 w-64" />
    </div>

    <div className="space-y-6">
      {/* Mixed content skeleton */}
      <div className="prose prose-sm max-w-none">
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-4/5" />
          <Skeleton className="h-4 w-3/5" />
        </div>
      </div>

      {/* Cards grid skeleton */}
      <div className="my-6 grid grid-cols-3 gap-6">
        {[1, 2, 3].map((i) => (
          <CardSkeleton key={i} />
        ))}
      </div>

      <ChartSkeleton />
    </div>
  </div>
);

interface EmptyStateProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  icon,
}) => (
  <div className="flex flex-col items-center justify-center px-6 py-12 text-center">
    <div className="bg-muted/50 mb-4 flex h-16 w-16 items-center justify-center rounded-full">
      {icon || (
        <div className="border-muted-foreground/30 h-8 w-8 rounded border-2 border-dashed"></div>
      )}
    </div>
    <h3 className="text-foreground mb-2 text-lg font-semibold">{title}</h3>
    <p className="text-muted-foreground max-w-md text-sm leading-relaxed">
      {description}
    </p>
  </div>
);
