'use client';

import { MessageDisplayComponentPublic } from '@/client/types.gen';
import { Avatar } from '@/components/ui/avatar';
import { AvatarImage } from '@/components/ui/avatar';
import { AvatarFallback } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { motion } from 'framer-motion';
import { BarChart as BarChartIcon } from 'lucide-react';

import { DisplayComponent } from '../components/message/display-component';
import { CanvasItem } from './canvas';

interface ChartsTabProps {
  canvasItems: CanvasItem[];
}

export function ChartsTab({ canvasItems }: ChartsTabProps) {
  // No items placeholder
  if (canvasItems.length === 0) {
    return (
      <div className="text-muted-foreground flex h-full flex-col items-center justify-center p-4 text-center">
        <BarChartIcon className="mb-4 h-12 w-12 opacity-20" />
        <p className="text-sm">No charts or tables available yet</p>
        <p className="mt-1 text-xs">
          Visualizations will appear here when generated
        </p>
      </div>
    );
  }

  return (
    <ScrollArea className="h-full w-full p-3">
      {/* Chronological Items (oldest first) */}
      {canvasItems.map((item) => (
        <motion.div key={item.id}>
          {/* Agent information */}
          {item.agentName && (
            <div className="mb-2 flex items-center">
              <Avatar className="mr-2 h-6 w-6">
                <AvatarImage
                  src={`/avatars/${item.agentName.toLowerCase()}.webp`}
                />
                <AvatarFallback className="bg-primary/10 text-primary text-xs">
                  {item.agentName.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <span className="text-sm font-medium">{item.agentName}</span>
            </div>
          )}

          {/* Only render display components */}
          <div className="mt-4">
            <DisplayComponent
              component={item.content as MessageDisplayComponentPublic}
            />
          </div>
        </motion.div>
      ))}
    </ScrollArea>
  );
}

export default ChartsTab;
