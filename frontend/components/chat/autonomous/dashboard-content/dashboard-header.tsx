import React from 'react';

import { If } from '@/components/ui/common/if';
import { Heading } from '@/components/ui/heading';
import { formatters } from '@/utils/date-formatters';

export interface DashboardHeaderProps {
  title: string;
  createdAt?: string;
  onExportPDF?: () => void;
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  title,
  createdAt,
}) => {
  const formatDate = formatters.withTime;

  return (
    <div className="mb-8 border-b py-2">
      <div className="space-y-1">
        <Heading level={1}>{title}</Heading>
        <If condition={createdAt}>
          {(createdAt) => (
            <div className="text-muted-foreground text-sm">
              <span>Generated on {formatDate(createdAt)}</span>
            </div>
          )}
        </If>
      </div>
    </div>
  );
};

export default DashboardHeader;
