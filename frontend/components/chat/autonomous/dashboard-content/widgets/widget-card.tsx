import React from 'react';

import { cn } from '@/lib/utils';

interface WidgetCardProps {
  title?: string;
  description?: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

const WidgetCard: React.FC<WidgetCardProps> = ({
  title,
  description,
  icon,
  children,
  className,
  style,
}) => {
  return (
    <div
      className={cn(
        'group relative min-h-[200px] overflow-hidden rounded-lg border p-4 text-left transition-all duration-300',
        'hover:bg-primary/5 hover:-translate-y-1 hover:shadow-xl',
        'from-card/80 to-card/40 bg-gradient-to-br backdrop-blur-sm',
        'active:translate-y-0 active:scale-[0.98]',
        'focus-visible:ring-primary/50 focus-visible:ring-2 focus-visible:outline-none',
        className,
      )}
      style={style}
    >
      {/* Animated gradient background for hover */}
      <div className="from-primary/5 to-secondary/5 absolute inset-0 bg-gradient-to-br opacity-0 transition-all duration-500 group-hover:opacity-100" />

      <div className="relative z-10 flex h-full flex-col">
        {(title || icon) && (
          <div className="mb-4 flex items-center gap-3">
            {icon && (
              <div className="bg-background/80 group-hover:bg-background/90 text-primary rounded-lg p-2 backdrop-blur-sm transition-colors">
                {icon}
              </div>
            )}
            {title && (
              <h4 className="text-foreground text-lg font-semibold transition-colors">
                {title}
              </h4>
            )}
          </div>
        )}

        {description && (
          <div className="bg-muted/30 group-hover:bg-muted/20 mb-4 rounded-lg p-3 transition-colors">
            <p className="text-muted-foreground group-hover:text-foreground/80 text-sm transition-colors">
              {description}
            </p>
          </div>
        )}

        <div className="flex-1">{children}</div>
      </div>
    </div>
  );
};

export default WidgetCard;
