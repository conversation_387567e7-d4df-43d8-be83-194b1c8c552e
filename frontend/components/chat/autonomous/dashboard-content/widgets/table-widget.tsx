import React from 'react';

import TableRenderer from '@/components/shared/TableRenderer';
import { Database } from 'lucide-react';

import WidgetCard from './widget-card';

export interface TableWidgetProps {
  widget: any;
  style?: React.CSSProperties;
}

const TableWidget: React.FC<TableWidgetProps> = ({ widget, style }) => {
  const { title, description, columns, rows } = widget;

  return (
    <WidgetCard
      title={title}
      description={description}
      icon={<Database className="h-4 w-4" />}
      style={style}
    >
      <TableRenderer
        title={title}
        description={description}
        columns={columns}
        rows={rows}
        className="bg-card rounded-lg border-none dark:bg-transparent"
      />
    </WidgetCard>
  );
};

export default TableWidget;
