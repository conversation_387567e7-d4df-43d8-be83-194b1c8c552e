import React from 'react';

import BaseCard from '@/components/shared/BaseCard';

export interface KpiCardWidgetProps {
  widget: any;
  style?: React.CSSProperties;
}

const KpiCardWidget: React.FC<KpiCardWidgetProps> = ({ widget, style }) => {
  const { title, value, description, trend, icon, alert } = widget;

  return (
    <BaseCard
      title={title}
      value={value}
      description={description}
      trend={trend}
      icon={icon}
      alert={alert}
      style={style}
    />
  );
};

export default KpiCardWidget;
