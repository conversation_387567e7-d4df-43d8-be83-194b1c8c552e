import React from 'react';

export interface GaugeWidgetProps {
  widget: any;
  style?: React.CSSProperties;
}

const GaugeWidget: React.FC<GaugeWidgetProps> = ({ widget, style }) => {
  const { title, value, description } = widget;

  const normalizedValue = Math.max(0, Math.min(100, value || 0));
  const radius = 50;
  const circumference = 2 * Math.PI * radius;
  const strokeOffset = circumference - (normalizedValue / 100) * circumference;

  const getColorClass = (val: number) => {
    if (val >= 80) return 'text-red-500';
    if (val >= 60) return 'text-yellow-500';
    return 'text-green-500';
  };

  const colorClass = getColorClass(normalizedValue);

  return (
    <div
      className="group hover:bg-primary/5 from-card/80 to-card/40 focus-visible:ring-primary/50 relative min-h-[200px] overflow-hidden rounded-lg border bg-gradient-to-br p-4 text-left backdrop-blur-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-xl focus-visible:ring-2 focus-visible:outline-none active:translate-y-0 active:scale-[0.98]"
      style={style}
    >
      {/* Animated gradient background for hover */}
      <div className="from-primary/5 to-secondary/5 absolute inset-0 bg-gradient-to-br opacity-0 transition-all duration-500 group-hover:opacity-100" />

      <div className="relative z-10 flex h-full flex-col">
        {title && (
          <div className="mb-4">
            <h3 className="text-foreground text-sm leading-tight font-semibold transition-colors">
              {title}
            </h3>
          </div>
        )}

        <div className="flex flex-1 items-center justify-center">
          <div className="relative">
            <svg
              className="h-32 w-32 -rotate-90 transform transition-transform duration-300 group-hover:scale-105"
              viewBox="0 0 120 120"
            >
              <circle
                cx="60"
                cy="60"
                r={radius}
                stroke="currentColor"
                strokeWidth="8"
                fill="none"
                className="text-muted/20"
              />
              <circle
                cx="60"
                cy="60"
                r={radius}
                stroke="currentColor"
                strokeWidth="8"
                fill="none"
                strokeLinecap="round"
                strokeDasharray={circumference}
                strokeDashoffset={strokeOffset}
                className={`${colorClass} transition-all duration-1000 ease-out`}
              />
            </svg>

            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <div
                  className={`text-2xl font-bold transition-colors ${colorClass}`}
                >
                  {Math.round(normalizedValue)}%
                </div>
              </div>
            </div>
          </div>
        </div>

        {description && (
          <div className="mt-4">
            <p className="text-muted-foreground group-hover:text-foreground/80 text-center text-xs leading-relaxed transition-colors">
              {description}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default GaugeWidget;
