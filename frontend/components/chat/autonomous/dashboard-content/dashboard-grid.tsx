import React from 'react';

import { <PERSON>roll<PERSON><PERSON>, ScrollBar } from '@/components/ui/scroll-area';

import ChartWidget from './widgets/chart-widget';
import GaugeWidget from './widgets/gauge-widget';
import KpiCardWidget from './widgets/kpi-card-widget';
import TableWidget from './widgets/table-widget';

export interface DashboardGridProps {
  widgets: any[];
  gridConfig: { columns?: number };
}

const DashboardGrid: React.FC<DashboardGridProps> = ({
  widgets,
  gridConfig,
}) => {
  const columns = gridConfig.columns || 12;

  const maxRow = Math.max(
    ...widgets.map((widget) => {
      const layout = widget.layout || { y: 0, h: 4 };
      return layout.y + layout.h;
    }),
    1,
  );

  const gridStyle = {
    gridTemplateColumns: `repeat(${columns}, 1fr)`,
    gridTemplateRows: `repeat(${maxRow}, minmax(60px, auto))`,
  };

  return (
    <ScrollArea>
      <div className="grid w-max gap-4" style={gridStyle}>
        {widgets.map(renderWidget)}
      </div>
      <ScrollBar orientation="horizontal" />
    </ScrollArea>
  );
};

export default DashboardGrid;

const renderWidget = (widget: any, index: number) => {
  const layout = widget.layout || { x: 0, y: 0, w: 12, h: 4 };
  const gridArea = `${layout.y + 1} / ${layout.x + 1} / ${layout.y + layout.h + 1} / ${layout.x + layout.w + 1}`;

  const key = widget.id || `widget-${index}`;
  const widgetProps = {
    style: { gridArea },
    widget,
  };

  switch (widget.type) {
    case 'kpi_card':
      return <KpiCardWidget key={key} {...widgetProps} />;
    case 'gauge':
      return <GaugeWidget key={key} {...widgetProps} />;
    case 'table':
      return <TableWidget key={key} {...widgetProps} />;
    case 'chart':
      return <ChartWidget key={key} {...widgetProps} />;
    default:
      return (
        <div
          key={key}
          className="bg-card text-card-foreground min-h-[200px] rounded-lg border p-4 shadow-sm"
          style={{ gridArea }}
        >
          <div className="flex h-full items-center justify-center">
            <div className="text-center">
              <p className="text-muted-foreground text-sm">
                Unknown widget type: {widget.type}
              </p>
              <pre className="bg-muted/30 text-foreground mt-2 rounded p-2 text-xs">
                {JSON.stringify(widget, null, 2)}
              </pre>
            </div>
          </div>
        </div>
      );
  }
};
