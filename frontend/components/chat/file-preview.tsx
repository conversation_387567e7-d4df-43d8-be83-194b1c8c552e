'use client';

import React, { useState } from 'react';
import { File, Image, FileText } from 'lucide-react';

import { toast } from '@/hooks/use-toast';
import { AttachmentsService } from '@/client';
import { UploadingFile } from '@/hooks/use-file-upload';
import { FilePreviewDialog } from '@/components/chat/components/common/file-preview-dialog';

interface FilePreviewProps {
  file: UploadingFile;
  showPreview?: boolean;
  onRemove?: (fileId: string) => void;
}

// Get file type category
function getFileCategory(mimeType: string): 'image' | 'pdf' | 'text' | 'document' | 'other' {
  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType === 'application/pdf') return 'pdf';
  if (mimeType.startsWith('text/') || mimeType === 'application/json') return 'text';
  if (mimeType.includes('word') || mimeType.includes('sheet')) return 'document';
  return 'other';
}

// Get file icon based on type
function getFileIcon(mimeType: string) {
  const category = getFileCategory(mimeType);

  if (category === 'image') {
    return <Image className="h-6 w-6" />;
  }
  if (category === 'pdf') {
    return (
      <div className="h-6 w-6 bg-red-500 rounded flex items-center justify-center text-white text-xs font-bold">
        PDF
      </div>
    );
  }
  if (category === 'document') {
    return <FileText className="h-6 w-6 text-blue-500" />;
  }
  if (category === 'text') {
    return <FileText className="h-6 w-6 text-green-500" />;
  }

  return <File className="h-6 w-6" />;
}

// Format file size
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
}

export function FilePreview({ file, showPreview = true, onRemove }: FilePreviewProps) {
  const [previewOpen, setPreviewOpen] = useState(false);
  const [imagePreviewUrl, setImagePreviewUrl] = useState<string | null>(null);
  const [textContent, setTextContent] = useState<string | null>(null);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [thumbnailUrl, setThumbnailUrl] = useState<string | null>(null);

  const fileCategory = getFileCategory(file.file.type);
  const canPreview = fileCategory === 'image' || fileCategory === 'text' || fileCategory === 'pdf';
  const isCompleted = file.status === 'completed';

  // Generate thumbnail for images
  React.useEffect(() => {
    if (fileCategory === 'image') {
      const url = URL.createObjectURL(file.file);
      setThumbnailUrl(url);

      return () => {
        URL.revokeObjectURL(url);
      };
    }
  }, [file.file, fileCategory]);

  // Handle preview
  const handlePreview = async () => {
    if (!canPreview || !showPreview) return;

    try {
      if (fileCategory === 'image') {
        // For images, create object URL from the file
        const url = URL.createObjectURL(file.file);
        setImagePreviewUrl(url);
        setPreviewOpen(true);
      } else if (fileCategory === 'text' && file.file.size < 1024 * 1024) { // Max 1MB for text preview
        // For text files, read content directly
        const text = await file.file.text();
        setTextContent(text);
        setPreviewOpen(true);
      } else if (fileCategory === 'pdf') {
        if (isCompleted && file.attachmentId) {
          // For completed PDFs, get download URL from backend
          try {
            const response = await AttachmentsService.getAttachmentDownloadUrl({
              attachmentId: file.attachmentId
            });
            setPdfUrl(response.download_url);
            setPreviewOpen(true);
          } catch {
            toast({
              title: 'PDF Preview Error',
              description: 'Failed to load PDF for preview.',
              variant: 'destructive',
            });
          }
        } else {
          // For PDFs still being processed, create object URL from the file
          const url = URL.createObjectURL(file.file);
          setPdfUrl(url);
          setPreviewOpen(true);
        }
      }
    } catch {
      toast({
        title: 'Preview Error',
        description: 'Failed to load file preview.',
        variant: 'destructive',
      });
    }
  };

  // Cleanup URLs when component unmounts
  const handleClosePreview = () => {
    if (imagePreviewUrl) {
      URL.revokeObjectURL(imagePreviewUrl);
      setImagePreviewUrl(null);
    }
    if (pdfUrl && !isCompleted) {
      // Only revoke object URLs we created, not presigned URLs from backend
      URL.revokeObjectURL(pdfUrl);
    }
    setPdfUrl(null);
    setTextContent(null);
    setPreviewOpen(false);
  };

  // Render for image files
  if (fileCategory === 'image') {
    return (
      <>
        <div className="relative group">
          {/* Remove Button */}
          {onRemove && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onRemove(file.id);
              }}
              className="absolute top-1 right-1 z-10 h-6 w-6 rounded-full bg-red-500 text-white hover:bg-red-600 transition-colors flex items-center justify-center text-sm font-bold opacity-80 hover:opacity-100"
              title="Remove file"
            >
              ×
            </button>
          )}

          {/* Clickable Preview Container */}
          <div
            className="w-20 h-20 border rounded-lg bg-card overflow-hidden relative cursor-pointer hover:shadow-md transition-shadow"
            onClick={canPreview && showPreview ? handlePreview : undefined}
          >
            {/* Image Preview */}
            {thumbnailUrl ? (
              <img
                src={thumbnailUrl}
                alt={file.file.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-muted">
                {getFileIcon(file.file.type)}
              </div>
            )}

            {/* Progress Overlay */}
            {(file.status === 'uploading' || file.status === 'processing') && (
              <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                <div className="text-white text-xs font-medium">
                  {file.progress}%
                </div>
              </div>
            )}

            {/* Error Overlay */}
            {file.status === 'failed' && (
              <div className="absolute inset-0 bg-red-500/80 flex items-center justify-center">
                <div className="text-white text-xs text-center p-1">
                  Failed
                </div>
              </div>
            )}
          </div>
        </div>

        <FilePreviewDialog
          fileCategory={fileCategory}
          filename={file.file.name}
          imagePreviewUrl={imagePreviewUrl}
          pdfUrl={pdfUrl}
          textContent={textContent}
          previewOpen={previewOpen}
          onOpenChange={handleClosePreview}
        />
      </>
    );
  }

  // Render for non-image files (document-style card)
  return (
    <>
      <div className="relative group">
        {/* Remove Button */}
        {onRemove && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              onRemove(file.id);
            }}
            className="absolute top-1 right-1 z-10 h-6 w-6 rounded-full bg-red-500 text-white hover:bg-red-600 transition-colors flex items-center justify-center text-sm font-bold opacity-80 hover:opacity-100"
            title="Remove file"
          >
            ×
          </button>
        )}

        {/* Clickable Document Card */}
        <div
          className={`flex items-center gap-3 p-3 rounded-lg border bg-card hover:shadow-md transition-shadow min-w-[200px] max-w-[280px] ${
            canPreview && showPreview ? 'cursor-pointer hover:bg-muted/50' : ''
          }`}
          onClick={canPreview && showPreview ? handlePreview : undefined}
        >
          {/* File Icon */}
          <div className="flex-shrink-0 p-2 rounded-lg bg-muted">
            {getFileIcon(file.file.type)}
          </div>

          {/* File Info */}
          <div className="flex-1 min-w-0">
            <div className="font-medium text-sm truncate" title={file.file.name}>
              {file.file.name}
            </div>
            <div className="text-xs text-muted-foreground">
              {formatFileSize(file.file.size)}
            </div>

            {/* Progress during upload */}
            {(file.status === 'uploading' || file.status === 'processing') && (
              <div className="text-xs text-muted-foreground mt-1">
                {file.progress}%
              </div>
            )}

            {/* Error Message */}
            {file.status === 'failed' && file.error && (
              <div className="text-xs text-red-500 mt-1 truncate" title={file.error}>
                {file.error}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Preview Dialog */}
      <FilePreviewDialog
        fileCategory={fileCategory}
        filename={file.file.name}
        imagePreviewUrl={imagePreviewUrl}
        pdfUrl={pdfUrl}
        textContent={textContent}
        previewOpen={previewOpen}
        onOpenChange={handleClosePreview}
      />
    </>
  );
}

// Component for displaying multiple file previews
interface FilePreviewListProps {
  files: UploadingFile[];
  showPreview?: boolean;
  onRemove?: (fileId: string) => void;
}

export function FilePreviewList({
  files,
  showPreview = true,
  onRemove
}: FilePreviewListProps) {
  if (files.length === 0) return null;

  return (
    <div className="space-y-3">
      {/* Grid Layout for Clean Previews */}
      <div className="flex flex-wrap gap-3 max-h-48 overflow-y-auto">
        {files.map((file) => (
          <FilePreview
            key={file.id}
            file={file}
            showPreview={showPreview}
            onRemove={onRemove}
          />
        ))}
      </div>
    </div>
  );
}
