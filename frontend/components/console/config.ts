// Terminal configuration for easy customization
export const TERMINAL_CONFIG = {
  // Terminal appearance
  theme: {
    background: '#000000',
    foreground: '#ffffff',
    cursor: '#ffffff',
    black: '#000000',
    red: '#e06c75',
    green: '#22c55e',
    yellow: '#e5c07b',
    blue: '#61afef',
    magenta: '#c678dd',
    cyan: '#56b6c2',
    white: '#d4d4d4',
    brightBlack: '#5c6370',
    brightRed: '#e06c75',
    brightGreen: '#22c55e',
    brightYellow: '#e5c07b',
    brightBlue: '#61afef',
    brightMagenta: '#c678dd',
    brightCyan: '#56b6c2',
    brightWhite: '#ffffff',
  },

  // Terminal settings
  settings: {
    cursorBlink: true,
    fontSize: 14,
    fontFamily: 'JetBrains Mono, Menlo, Monaco, Consolas, monospace',
    rows: 24,
    cols: 80,
    scrollback: 1000,
    smoothScrollDuration: 0,
    altClickMovesCursor: true,
    convertEol: false,
    wordSeparator: ' ()[]{}\'"',
    cursorStyle: 'block' as const,
    macOptionIsMeta: true,
    macOptionClickForcesSelection: true,
    fastScrollModifier: 'alt' as const,
    fastScrollSensitivity: 5,
    scrollSensitivity: 3,
    allowTransparency: false,
    disableStdin: false,
    allowProposedApi: true,
  },

  // WebSocket configuration
  websocket: {
    reconnectDelay: 1000,
    maxReconnectAttempts: 3,
    messageBufferFlushDelay: 4,
    initialResizeDelay: 100,
    visibilityChangeDelay: 50,
  },

  // Tool call display configuration
  toolCalls: {
    scriptExecutingColor: '\x1b[33m', // Yellow
    scriptCompletedColor: '\x1b[32m', // Green
    outputColor: '\x1b[36m', // Cyan
    resetColor: '\x1b[0m', // Reset
    scriptKeywords: ['cli', 'console'],
    // Status types: 'complete' | 'error' | 'timeout'
  },

  // UI messages
  messages: {
    authRequired: '\r\n\x1b[31mAuthentication required - please login\x1b[0m',
    authFailed: '\r\n\x1b[31mAuthentication failed - please refresh and login again\x1b[0m',
    serverError: '\r\n\x1b[31mServer error - please try again later\x1b[0m',
    connectionLost: '\r\n\x1b[33mConnection lost - attempting to reconnect...\x1b[0m',
    disconnected: '\r\n\x1b[31mDisconnected from terminal server\x1b[0m',
    connectionError: '\r\n\x1b[31mError connecting to terminal server - check your connection\x1b[0m',
    initializing: 'Initializing terminal...',
    activationPrompt: {
      title: 'Console Terminal',
      subtitle: 'Click on the Console tab to start the interactive terminal',
    },
  },

  // Default paths
  defaultPath: '/workspace',
} as const;

// WebSocket URL builder
export const buildWebSocketUrl = (apiUrl: string, token: string): string => {
  const protocol = window.location.protocol === 'https:' ? 'wss' : 'ws';
  const wsUrl = new URL(`${apiUrl.replace(/^https?/, protocol)}/api/v1/console/ws`);
  wsUrl.searchParams.set('token', token);
  return wsUrl.toString();
};

// Terminal theme variants (for future extensibility)
export const THEME_VARIANTS = {
  dark: TERMINAL_CONFIG.theme,
  light: {
    background: '#000000',
    foreground: '#24292e',
    cursor: '#24292e',
    // ... other light theme colors
  },
} as const;

export type TerminalTheme = keyof typeof THEME_VARIANTS;
export type TerminalConfig = typeof TERMINAL_CONFIG;
