import React, { memo } from 'react';

import { useTerminalConnection } from '@/hooks/use-terminal-connection';

import { ActivationPrompt } from './activation-prompt';
import { LoadingOverlay } from './loading-overlay';

interface ConsoleTerminalProps {
  shouldActivateTerminal: boolean;
  hasBeenUserActivated: boolean;
  isVisible: boolean;
  conversationId?: string;
}

export const ConsoleTerminal = memo(
  ({
    shouldActivateTerminal,
    hasBeenUserActivated,
    isVisible,
    conversationId,
  }: ConsoleTerminalProps) => {
    const { terminalRef, isLoading } = useTerminalConnection(
      shouldActivateTerminal,
      isVisible,
      conversationId,
    );
    return (
      <div className="relative h-full flex-1">
        <ActivationPrompt hasBeenUserActivated={hasBeenUserActivated} />
        <LoadingOverlay
          isLoading={isLoading}
          hasBeenUserActivated={hasBeenUserActivated}
        />

        <div
          ref={terminalRef}
          className="absolute inset-0 flex flex-col overflow-hidden bg-black font-mono text-green-400"
          style={{
            height: '100%',
            width: '100%',
            minHeight: 0,
            minWidth: 0,
          }}
        >
          <div className="relative min-h-0 flex-1 bg-black">
            <div className="absolute inset-0 bg-black p-2" />
          </div>
        </div>
      </div>
    );
  },
);

ConsoleTerminal.displayName = 'ConsoleTerminal';
