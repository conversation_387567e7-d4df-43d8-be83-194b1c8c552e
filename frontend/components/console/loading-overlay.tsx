import React, { memo } from 'react';
import { TERMINAL_CONFIG } from './config';

interface LoadingOverlayProps {
  isLoading: boolean;
  hasBeenUserActivated: boolean;
}

/**
 * LoadingOverlay component for displaying terminal initialization status
 * Shows spinner and loading message when terminal is initializing
 */
export const LoadingOverlay = memo(({ isLoading, hasBeenUserActivated }: LoadingOverlayProps) => {
  if (!isLoading || !hasBeenUserActivated) return null;

  return (
    <div className="bg-background/80 absolute inset-0 flex items-center justify-center backdrop-blur-sm">
      <div className="text-muted-foreground flex items-center gap-2">
        <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
        {TERMINAL_CONFIG.messages.initializing}
      </div>
    </div>
  );
});

LoadingOverlay.displayName = 'LoadingOverlay';
