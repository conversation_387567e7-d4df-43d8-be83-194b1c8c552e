import React, { memo, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Terminal, Clock, CheckCircle, AlertCircle, Copy } from 'lucide-react';

interface ProcessedToolCall {
  id: string;
  name: string;
  status: 'pending' | 'executing' | 'completed' | 'failed';
  arguments?: {
    script?: string;
    command?: string;
  };
  output?: {
    output?: string;
    error?: string;
  };
  timestamp?: string;
  duration?: number;
}

interface ToolExecutionCardProps {
  toolCall: ProcessedToolCall;
  onCopyCommand: (command: string) => void;
}

/**
 * ToolExecutionCard - Displays individual tool executions in a structured format
 * Shows script, output, status, and provides actions for interactive mode
 */
export const ToolExecutionCard = memo(({
  toolCall,
  onCopyCommand
}: ToolExecutionCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const getStatusIcon = () => {
    switch (toolCall.status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'executing':
        return <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-500 border-t-transparent" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Terminal className="h-4 w-4" />;
    }
  };

  const getStatusColor = () => {
    switch (toolCall.status) {
      case 'pending': return 'bg-yellow-500';
      case 'executing': return 'bg-blue-500';
      case 'completed': return 'bg-green-500';
      case 'failed': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const script = toolCall.arguments?.script || toolCall.arguments?.command || '';
  const output = toolCall.output?.output || '';
  const error = toolCall.output?.error || '';

  const handleCopyCommand = () => {
    if (script) {
      onCopyCommand(script);
    }
  };

  return (
    <Card className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {getStatusIcon()}
            <div>
              <CardTitle className="text-base">
                {toolCall.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </CardTitle>
              <div className="flex items-center gap-2 mt-1">
                <Badge
                  variant="secondary"
                  className={`text-white ${getStatusColor()}`}
                >
                  {toolCall.status}
                </Badge>
                {toolCall.timestamp && (
                  <span className="text-xs text-muted-foreground">
                    {new Date(toolCall.timestamp).toLocaleTimeString()}
                  </span>
                )}
                {toolCall.duration && (
                  <span className="text-xs text-muted-foreground">
                    ({toolCall.duration}ms)
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {script && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyCommand}
                className="gap-2"
              >
                <Copy className="h-3 w-3" />
                Copy
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? 'Collapse' : 'Expand'}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Command/Script Section */}
        {script && (
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <Terminal className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Command</span>
            </div>
            <div className="bg-muted rounded-md p-3 font-mono text-sm overflow-x-auto">
              <pre className="whitespace-pre-wrap">{script}</pre>
            </div>
          </div>
        )}

        {/* Output Section */}
        {(output || error) && (
          <div className={isExpanded ? 'block' : 'max-h-32 overflow-hidden relative'}>
            {output && (
              <div className="mb-4">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm font-medium">Output</span>
                </div>
                <div className="bg-black rounded-md p-3 font-mono text-sm text-green-400 overflow-x-auto">
                  <pre className="whitespace-pre-wrap">{output}</pre>
                </div>
              </div>
            )}

            {error && (
              <div className="mb-4">
                <div className="flex items-center gap-2 mb-2">
                  <AlertCircle className="h-4 w-4 text-red-500" />
                  <span className="text-sm font-medium">Error</span>
                </div>
                <div className="bg-black rounded-md p-3 font-mono text-sm text-red-400 overflow-x-auto">
                  <pre className="whitespace-pre-wrap">{error}</pre>
                </div>
              </div>
            )}

            {!isExpanded && (output || error) && (
              <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-background to-transparent pointer-events-none" />
            )}
          </div>
        )}

        {/* Status Messages */}
        {toolCall.status === 'executing' && (
          <div className="flex items-center gap-2 text-blue-600 text-sm">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-500 border-t-transparent" />
            Executing command...
          </div>
        )}

        {toolCall.status === 'pending' && (
          <div className="flex items-center gap-2 text-yellow-600 text-sm">
            <Clock className="h-4 w-4" />
            Waiting to execute...
          </div>
        )}
      </CardContent>
    </Card>
  );
});

ToolExecutionCard.displayName = 'ToolExecutionCard';
