import React, { memo } from 'react';
import { Terminal } from 'lucide-react';
import { TERMINAL_CONFIG } from './config';

interface ActivationPromptProps {
  hasBeenUserActivated: boolean;
}

/**
 * ActivationPrompt component for prompting user to activate the terminal
 * Shows helpful message when terminal hasn't been activated yet
 */
export const ActivationPrompt = memo(({ hasBeenUserActivated }: ActivationPromptProps) => {
  if (hasBeenUserActivated) return null;

  return (
    <div className="absolute inset-0 flex items-center justify-center bg-[#1a1b1e]/95 backdrop-blur-sm">
      <div className="text-muted-foreground flex flex-col items-center gap-3 text-center">
        <Terminal className="h-8 w-8" />
        <div className="flex flex-col gap-1">
          <div className="text-sm font-medium">
            {TERMINAL_CONFIG.messages.activationPrompt.title}
          </div>
          <div className="text-xs">
            {TERMINAL_CONFIG.messages.activationPrompt.subtitle}
          </div>
        </div>
      </div>
    </div>
  );
});

ActivationPrompt.displayName = 'ActivationPrompt';
