'use client';

import { Suspense, useEffect, useRef } from 'react';

import dynamic from 'next/dynamic';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarNavigation,
  SidebarRail,
  SidebarSeparator,
  useSidebar,
} from '@/components/ui/sidebar';
import pathsConfig from '@/config/paths.config';
import { SidebarConversation } from '@/features/conversation/components/sidebar-conversation';
import { useUserContext } from '@/features/user/provider/user-provider';
import { useCallbackRef } from '@/hooks/use-callback-ref';
import { useGetNavigationConfig } from '@/hooks/use-get-navigation-config';
import { useQuota } from '@/hooks/use-quota';
import useAuth from '@/hooks/useAuth';
import useSaasEnabled from '@/hooks/useSaasEnabled';
import { useSubscription } from '@/lib/hooks/useSubscription';
import { cn } from '@/lib/utils';
import { partition } from 'lodash';
import {
  ChevronsUpDown,
  GalleryVerticalEnd,
  Loader2,
  LogOut,
} from 'lucide-react';

import { AppLogo, MinimizedAppLogo } from '../app-logo';
import { NotificationButton } from '../notifications/notification-button';
import { QuotaIndicator } from '../quota-indicator';
import { If } from '../ui/common/if';
import { Skeleton } from '../ui/skeleton';

const WorkspaceSelector = dynamic(
  () => import('./workspace-selector').then((mod) => mod.WorkspaceSelector),
  {
    ssr: false,
  },
);

export default function AppSidebar() {
  const { logout } = useAuth();
  const { user } = useUserContext();
  const { hasActiveSubscription, isLoading, isFetched } = useSubscription();
  const { open, setOpen } = useSidebar();
  const { quotaInfo } = useQuota();

  const pathname = usePathname();
  const currentPathName = useRef('');

  // Close sidebar when navigating to a different page
  const setOpenCallback = useCallbackRef(setOpen);
  useEffect(() => {
    if (currentPathName.current) {
      setOpenCallback(false);
    }
    currentPathName.current = pathname;
  }, [pathname, setOpenCallback]);

  // SaaS feature flag
  const { isEnabled: saasEnabled } = useSaasEnabled();

  const { routes } = useGetNavigationConfig();

  const [settingRoutes, unSettingRoutes] = partition(routes, (r) => {
    if ('label' in r) {
      return r.label === 'Settings';
    }

    return false;
  });

  return (
    <div className="group">
      <Sidebar collapsible="icon">
        <SidebarHeader>
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              {/* <CloudThinkerLogo /> */}
              <If condition={open} fallback={<MinimizedAppLogo />}>
                <AppLogo />
              </If>
            </div>

            <div className={'group-data-[minimized=true]:hidden'}>
              <NotificationButton />
            </div>
          </div>
          <div>
            <SidebarSeparator />
            {/* Current Workspace */}
            <SidebarGroupLabel className={cn({ hidden: !open })}>
              Current Workspace
            </SidebarGroupLabel>
            <Suspense fallback={<Skeleton className="h-10 w-full" />}>
              <WorkspaceSelector />
            </Suspense>

            <SidebarSeparator />
            <SidebarConversation />
            <SidebarSeparator />
          </div>
        </SidebarHeader>

        <SidebarContent className="overflow-x-hidden">
          <SidebarNavigation routes={unSettingRoutes} />
        </SidebarContent>
        <SidebarFooter>
          <SidebarMenu>
            {/* Only show subscription/upgrade section if SaaS is enabled and user doesn't have an active subscription */}
            {saasEnabled &&
              isFetched &&
              !isLoading &&
              !hasActiveSubscription && (
                <SidebarMenuItem>
                  <div className="px-2 group-data-[collapsible=icon]:hidden">
                    <div className="mb-2 space-y-1">
                      <h4 className="text-sm font-semibold">Starter Plan</h4>
                      <p className="text-muted-foreground text-xs">
                        Upgrade to unlock more features
                      </p>
                    </div>
                    <div className="bg-muted mb-2 h-1.5 w-full rounded-full">
                      <div
                        className="bg-primary h-1.5 rounded-full"
                        style={{ width: '15%' }}
                      />
                    </div>
                  </div>

                  <div className="px-2 group-data-[collapsible=icon]:px-0">
                    <SidebarMenuButton
                      asChild
                      className="bg-primary text-primary-foreground hover:bg-primary/90"
                    >
                      <Link
                        href={pathsConfig.app.purchase}
                        className="flex items-center justify-center gap-2"
                      >
                        <GalleryVerticalEnd />
                        <span className="group-data-[collapsible=icon]:hidden">
                          Upgrade Now
                        </span>
                      </Link>
                    </SidebarMenuButton>
                  </div>
                </SidebarMenuItem>
              )}

            {/* Show loading state while fetching subscription data (only if SaaS is enabled) */}
            {saasEnabled && (isLoading || !isFetched) && (
              <SidebarMenuItem>
                <div className="flex items-center space-x-2 px-2 group-data-[collapsible=icon]:hidden">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-xs">Loading subscription...</span>
                </div>
              </SidebarMenuItem>
            )}

            {/* Quota Indicator */}
            <If condition={quotaInfo}>
              {(quotaInfo) => (
                <SidebarMenuItem>
                  <div className="px-2 group-data-[collapsible=icon]:hidden">
                    <QuotaIndicator quotaInfo={quotaInfo} />
                  </div>
                </SidebarMenuItem>
              )}
            </If>

            <SidebarMenuItem>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <SidebarMenuButton
                    size="lg"
                    className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                  >
                    <Avatar className="h-8 w-8 rounded-lg">
                      {user.avatar_url ? (
                        <AvatarImage
                          src={user.avatar_url}
                          alt={user.full_name || 'User'}
                          className="rounded-lg"
                        />
                      ) : null}
                      <AvatarFallback className="rounded-lg">
                        {user.full_name?.slice(0, 2)?.toUpperCase() || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-semibold">
                        {user.full_name}
                      </span>
                      <span className="truncate text-xs">{user.email}</span>
                    </div>
                    <ChevronsUpDown className="ml-auto size-4" />
                  </SidebarMenuButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
                  side="bottom"
                  align="end"
                  sideOffset={4}
                >
                  <DropdownMenuItem className={'h-10! rounded-none'}>
                    <div
                      className={
                        'flex flex-col justify-start truncate text-left text-xs'
                      }
                    >
                      <div className={'text-muted-foreground'}>
                        Signed in as
                      </div>

                      <div>
                        <span className={'block truncate'}>{user.email}</span>
                      </div>
                    </div>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />

                  {'children' in settingRoutes[0] &&
                    settingRoutes[0].children?.map((r) => {
                      return (
                        <DropdownMenuItem
                          key={r.label}
                          onClick={(e) => e.preventDefault()}
                        >
                          <Link
                            href={r.path}
                            prefetch
                            className="flex items-center gap-2"
                          >
                            {r.Icon && <r.Icon className="h-4 w-4" />}
                            {r.label}
                            {r.extraLabel}
                          </Link>
                        </DropdownMenuItem>
                      );
                    })}
                  <DropdownMenuItem
                    onClick={logout}
                    className="flex items-center gap-2"
                  >
                    <LogOut className="h-4 w-4" />
                    Log out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>
    </div>
  );
}
