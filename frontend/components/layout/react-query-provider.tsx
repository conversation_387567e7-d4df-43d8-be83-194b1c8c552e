// In Next.js, this file would be called: app/providers.tsx
'use client';

import { useCallback, useState } from 'react';

import { useRouter } from 'next/navigation';

import { OpenAPI } from '@/client';
import pathsConfig from '@/config/paths.config';
import { urlConfig } from '@/config/url.config';
import { UserInfo } from '@/types/common.enum';
// Since QueryClientProvider relies on useContext under the hood, we have to put 'use client' on top
import {
  MutationCache,
  QueryCache,
  QueryClient,
  QueryClientProvider,
  isServer,
} from '@tanstack/react-query';
import clientCookie from 'js-cookie';

import { Modal } from '../ui/modal';

// In Next.js, this file would be called: app/providers.tsx

// In Next.js, this file would be called: app/providers.tsx

// In Next.js, this file would be called: app/providers.tsx

// In Next.js, this file would be called: app/providers.tsx

// In Next.js, this file would be called: app/providers.tsx

// Initialize OpenAPI configuration
OpenAPI.BASE = urlConfig.apiUrl;

function makeQueryClient(handleExpired?: () => void) {
  return new QueryClient({
    queryCache: new QueryCache({
      onError: (error) => {
        // 🎉 only show error toasts if we already have data in the cache
        // which indicates a failed background update
        if (error.message === 'Unauthorized') {
          handleExpired?.();
        }
        return error.message;
      },
    }),
    mutationCache: new MutationCache({
      onError: (error) => {
        // 🎉 only show error toasts if we already have data in the cache
        // which indicates a failed background update
        if (error.message === 'Unauthorized') {
          handleExpired?.();
        }
        return error.message;
      },
    }),
    defaultOptions: {
      queries: {
        // With SSR, we usually want to set some default staleTime
        // above 0 to avoid refetching immediately on the client
        staleTime: 60 * 1000,
        retry: false,
      },
    },
  });
}

let browserQueryClient: QueryClient | undefined = undefined;

function getQueryClient(handleExpired?: () => void) {
  if (isServer) {
    // Server: always make a new query client
    return makeQueryClient(handleExpired);
  } else {
    // Browser: make a new query client if we don't already have one
    // This is very important, so we don't re-make a new client if React
    // suspends during the initial render. This may not be needed if we
    // have a suspense boundary BELOW the creation of the query client
    if (!browserQueryClient)
      browserQueryClient = makeQueryClient(handleExpired);
    return browserQueryClient;
  }
}

OpenAPI.TOKEN = async () => {
  return clientCookie.get(UserInfo.AccessToken) || '';
};

function TokenExpired({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) {
  return (
    <Modal
      title=""
      description="Your session has expired. Redirecting to login page..."
      isOpen={isOpen}
      onClose={onClose}
    />
  );
}

export default function ReactQueryProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  // NOTE: Avoid useState when initializing the query client if you don't
  //       have a suspense boundary between this and the code that may
  //       suspend because React will throw away the client on the initial
  //       render if it suspends and there is no boundary

  const [isOpen, setIsOpen] = useState(false);

  const router = useRouter();

  const handleExpired = () => {
    if (clientCookie.get(UserInfo.AccessToken)) {
      setIsOpen(true);

      clientCookie.remove(UserInfo.AccessToken);
      clientCookie.remove('sidebar:state');
    }

    setTimeout(() => {
      setIsOpen(false);
      router.push(pathsConfig.auth.signIn);
    }, 2000);
  };

  const closeModalAction = useCallback(() => {}, []);

  return (
    <QueryClientProvider client={getQueryClient(handleExpired)}>
      <TokenExpired isOpen={isOpen} onClose={closeModalAction} />
      {children}
    </QueryClientProvider>
  );
}
