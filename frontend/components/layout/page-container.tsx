import React from 'react';

import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';

export default function PageContainer({
  children,
  scrollable = true,
  className,
}: {
  children: React.ReactNode;
  scrollable?: boolean;
  className?: string;
}) {
  return (
    <>
      {scrollable ? (
        <ScrollArea className={cn('h-[calc(100dvh-4rem)]', className)}>
          <div className="px-2 pt-0 pb-4">{children}</div>
        </ScrollArea>
      ) : (
        <div
          className={cn(
            'flex h-full flex-col overflow-hidden px-2 pt-0 pb-4',
            className,
          )}
        >
          {children}
        </div>
      )}
    </>
  );
}
