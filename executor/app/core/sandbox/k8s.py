from pathlib import Path

from app.core.config import settings
from app.schemas import K8SCredentials


class K8SCredentialsManager:
    def __init__(self, k8s_credentials: K8SCredentials | None = None):
        self.k8s_credentials = k8s_credentials

    def create_or_update_kubeconfig(self, workspace_path: Path) -> None:
        """Create or update kubeconfig file in the home directory"""
        if self.k8s_credentials and self.k8s_credentials.kubeconfig:
            kubeconfig_path = (
                workspace_path / settings.SANDBOX_HOME_DIR / ".kube" / "config"
            )
            kubeconfig_path.parent.mkdir(parents=True, exist_ok=True)
            with open(kubeconfig_path, "w") as f:
                f.write(self.k8s_credentials.kubeconfig)

    def get_environment_variables(self) -> dict[str, str]:
        if not self.k8s_credentials:
            return {}

        return {
            "KUBECONFIG": str(Path(settings.SANDBOX_VIRTUAL_DIR) / ".kube" / "config")
        }
