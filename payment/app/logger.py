from __future__ import annotations

import logging
import os
import sys
import traceback

# Import loguru conditionally
try:
    from loguru import logger as loguru_logger

    LOGURU_AVAILABLE = True
except ImportError:
    LOGURU_AVAILABLE = False


class LoggerConfig:
    """Enhanced logger configuration with diagnose and backtrace support."""

    def __init__(self):
        self.log_level = os.getenv("LOG_LEVEL", "INFO").upper()
        self.diagnose = os.getenv("LOG_DIAGNOSE", "true").lower() == "true"
        self.backtrace = os.getenv("LOG_BACKTRACE", "false").lower() == "true"
        self.environment = os.getenv("ENVIRONMENT", "local")
        self.use_loguru = LOGURU_AVAILABLE and self.environment == "local"

    def setup_logger(self):
        """Configure enhanced console logger with diagnose and backtrace support."""
        if self.use_loguru:
            return self._setup_loguru_logger()
        else:
            return self._setup_standard_logger()

    def _setup_loguru_logger(self):
        """Setup loguru logger for local development with colors and diagnostics."""
        # Remove default handler
        loguru_logger.remove()

        # Add colorized console handler with detailed format for local development
        loguru_logger.add(
            sys.stderr,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | <level>{message}</level>",
            level=self.log_level,
            colorize=True,
            backtrace=True,  # Enable backtrace for better error tracking
            diagnose=True,  # Enable variable inspection in tracebacks
            catch=True,  # Catch exceptions and log them
        )

        # Intercept standard logging and redirect to loguru
        self._setup_logging_intercept()

        # Add enhanced methods for compatibility (loguru handles tracing automatically)
        self._add_loguru_enhanced_methods(loguru_logger)

        return loguru_logger

    def _setup_logging_intercept(self):
        """Intercept standard Python logging and redirect to loguru."""

        class InterceptHandler(logging.Handler):
            """Intercept standard logging records and emit them via loguru."""

            def emit(self, record):
                # Get corresponding Loguru level if it exists
                try:
                    level = loguru_logger.level(record.levelname).name
                except ValueError:
                    level = record.levelno

                # Find caller from where original logging call was made
                frame, depth = sys._getframe(6), 6
                while frame and frame.f_code.co_filename == logging.__file__:
                    frame = frame.f_back
                    depth += 1

                # Handle exceptions properly - let loguru format them
                exception = (
                    record.exc_info if record.exc_info != (None, None, None) else None
                )
                loguru_logger.opt(depth=depth, exception=exception).log(
                    level, record.getMessage()
                )

        # Remove all existing handlers from root logger
        root = logging.getLogger()
        for handler in root.handlers[:]:
            root.removeHandler(handler)

        # Configure root logger to use intercept handler
        intercept_handler = InterceptHandler()
        logging.basicConfig(handlers=[intercept_handler], level=0, force=True)

        # Ensure FastAPI and Uvicorn logs go through our handler
        for logger_name in ["uvicorn", "uvicorn.error", "uvicorn.access", "fastapi"]:
            logger = logging.getLogger(logger_name)
            logger.handlers = [intercept_handler]
            logger.propagate = False
            logger.setLevel(logging.INFO)

        # Override exception handling to use loguru
        def loguru_excepthook(exc_type, exc_value, exc_traceback):
            """Custom exception hook that uses loguru for unhandled exceptions."""
            if issubclass(exc_type, KeyboardInterrupt):
                # Allow KeyboardInterrupt to be handled normally
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return

            loguru_logger.opt(exception=(exc_type, exc_value, exc_traceback)).error(
                "Unhandled exception"
            )

        # Install the custom exception hook
        sys.excepthook = loguru_excepthook

    def _setup_standard_logger(self) -> logging.Logger:
        """Setup standard Python logger for production environments."""
        # Clear any existing handlers
        logging.root.handlers = []

        # Create main logger
        logger = logging.getLogger("app")
        logger.setLevel(self.log_level)
        logger.handlers = []

        # Setup console handler
        console_handler = logging.StreamHandler()

        # Enhanced formatter with diagnose info
        if self.diagnose:
            formatter = logging.Formatter(
                fmt="%(asctime)s  %(levelname)-8s  %(name)s  [%(filename)s:%(lineno)d:%(funcName)s]  %(message)s",
                datefmt="%Y-%m-%d %H:%M:%S",
            )
        else:
            formatter = logging.Formatter(
                fmt="%(asctime)s  %(levelname)-8s  %(name)s  %(message)s",
                datefmt="%Y-%m-%d %H:%M:%S",
            )

        console_handler.setFormatter(formatter)
        console_handler.setLevel(self.log_level)
        logger.addHandler(console_handler)

        # Add enhanced logging methods
        self._add_enhanced_methods(logger)

        return logger

    def _add_enhanced_methods(self, logger: logging.Logger) -> None:
        """Add enhanced logging methods with backtrace support for standard logging."""

        def log_with_backtrace(
            level: int, msg: str, exc_info: Exception | None = None
        ) -> None:
            """Log message with optional backtrace."""
            if self.backtrace:
                if exc_info:
                    # Log exception with full traceback
                    logger.log(level, f"{msg}\nTraceback:")
                    logger.log(
                        level,
                        "".join(
                            traceback.format_exception(
                                type(exc_info), exc_info, exc_info.__traceback__
                            )
                        ),
                    )
                else:
                    # Log current stack trace
                    stack = traceback.format_stack()[:-1]  # Exclude current frame
                    logger.log(level, f"{msg}\nCall stack:")
                    logger.log(level, "".join(stack))
            else:
                logger.log(level, msg)
                if exc_info:
                    logger.exception("")

        def debug_trace(msg: str) -> None:
            """Debug with call stack trace."""
            log_with_backtrace(logging.DEBUG, msg)

        def info_trace(msg: str) -> None:
            """Info with call stack trace."""
            log_with_backtrace(logging.INFO, msg)

        def warning_trace(msg: str) -> None:
            """Warning with call stack trace."""
            log_with_backtrace(logging.WARNING, msg)

        def error_trace(msg: str, exc_info: Exception | None = None) -> None:
            """Error with call stack trace and optional exception."""
            log_with_backtrace(logging.ERROR, msg, exc_info)

        def exception_trace(msg: str, exc_info: Exception | None = None) -> None:
            """Exception with full backtrace."""
            if exc_info is None:
                exc_info = sys.exc_info()[1]
            log_with_backtrace(logging.ERROR, msg, exc_info)

        # Bind methods to logger
        logger.debug_trace = debug_trace
        logger.info_trace = info_trace
        logger.warning_trace = warning_trace
        logger.error_trace = error_trace
        logger.exception_trace = exception_trace

    def _add_loguru_enhanced_methods(self, logger):
        """Add enhanced logging methods for loguru logger compatibility."""

        def debug_trace(msg: str) -> None:
            """Debug with call stack trace - loguru handles this automatically."""
            logger.debug(msg)

        def info_trace(msg: str) -> None:
            """Info with call stack trace - loguru handles this automatically."""
            logger.info(msg)

        def warning_trace(msg: str) -> None:
            """Warning with call stack trace - loguru handles this automatically."""
            logger.warning(msg)

        def error_trace(msg: str, exc_info: Exception | None = None) -> None:
            """Error with call stack trace and optional exception."""
            if exc_info:
                logger.error(f"{msg}: {exc_info}")
            else:
                logger.error(msg)

        def exception_trace(msg: str, exc_info: Exception | None = None) -> None:
            """Exception with full backtrace - loguru handles this automatically."""
            if exc_info:
                logger.exception(f"{msg}: {exc_info}")
            else:
                logger.exception(msg)

        # Bind methods to logger for compatibility
        logger.debug_trace = debug_trace
        logger.info_trace = info_trace
        logger.warning_trace = warning_trace
        logger.error_trace = error_trace
        logger.exception_trace = exception_trace


config = LoggerConfig()
logger = config.setup_logger()


__all__ = [
    "logger",
    "config",
]
